'use client';

import React, { useState, useEffect, useRef } from 'react';
import Image from 'next/image';

// Interfaces (podem ser movidas para um arquivo de tipos compartilhado no futuro)
interface ContatoCliente {
  email: string | null;
  telefone: string | null;
  endereco: string | null;
}

interface Cliente {
  id: number;
  nome: string | null;
  identificador: string | null;
  numeroDocumento: string | null; // Mantido para consistência, mesmo que não usado no header
  ContatoCliente: ContatoCliente[]; // Mantido para consistência
}

interface ClienteHeaderProps {
  cliente: Cliente | null;
  handleLogout: () => void;
}

const ClienteHeader: React.FC<ClienteHeaderProps> = ({ cliente, handleLogout }) => {
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const toggleDropdown = () => {
    setDropdownOpen(!dropdownOpen);
  };

  // Fecha o dropdown se clicar fora dele
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setDropdownOpen(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <header className="bg-green-500 shadow-md fixed top-0 left-0 right-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-2 flex justify-between items-center">
        <div>
          <Image
            src="/logo.svg"
            alt="Logo RGSE"
            width={150}
            height={40}
            className="h-16 w-auto" // Mantém o estilo original da logo
            priority
          />
        </div>
        <div className="relative" ref={dropdownRef}>
          <button
            onClick={toggleDropdown}
            className="flex items-center justify-center h-10 w-10 rounded-full bg-white text-green-500 hover:bg-green-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-green-500 focus:ring-white cursor-pointer"
            aria-label="Menu do usuário"
          >
            {/* Ícone de Perfil: Letra ou SVG */}
            {cliente && cliente.nome && cliente.nome.trim() !== '' ? (
              <span className="font-semibold text-lg">
                {cliente.nome.trim().charAt(0).toUpperCase()}
              </span>
            ) : (
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
            )}
          </button>
          {dropdownOpen && cliente && (
            <div className="absolute right-0 mt-2 w-64 bg-white rounded-md shadow-lg py-1 z-50 ring-1 ring-white ring-opacity-5">
              <div className="px-4 py-3">
                <p className="text-sm font-medium text-gray-900 truncate">
                  {cliente.nome || 'Nome não disponível'}
                </p>
                <p className="text-xs text-gray-500 truncate">
                  ID: {cliente.id}
                </p>
                <p className="text-xs text-gray-500 truncate">
                  Identificador: {cliente.identificador || 'N/A'}
                </p>
              </div>
              <div className="border-t border-gray-100"></div>
              <button
                onClick={() => {
                  handleLogout();
                  setDropdownOpen(false); // Fecha o dropdown ao sair
                }}
                className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 cursor-pointer"
              >
                Sair
              </button>
            </div>
          )}
        </div>
      </div>
    </header>
  );
};

export default ClienteHeader;

