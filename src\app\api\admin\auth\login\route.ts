import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';
import { SignJWT } from 'jose';
import { cookies } from 'next/headers';

const prisma = new PrismaClient();

export async function POST(request: NextRequest) {
  try {
    const { email, senha, manterConectado } = await request.json();

    if (!email || !senha) {
      return NextResponse.json(
        { error: 'Email e senha são obrigatórios' },
        { status: 400 }
      );
    }

    // Buscar usuário
    const user = await prisma.adminUser.findUnique({
      where: { email: email.toLowerCase() },
    });
    console.log(user);
    if (!user || !user.ativo) {
      return NextResponse.json(
        { error: 'Credenciais inválidas' },
        { status: 401 }
      );
    }
    console.log(senha);
    // Verificar senha
    const senhaValida = await bcrypt.compare(senha, user.senha);
    console.log(senhaValida);
    if (!senhaValida) {
      return NextResponse.json(
        { error: 'Credenciais inválidas' },
        { status: 401 }
      );
    }

    // Gerar token JWT
    const secret = new TextEncoder().encode(process.env.JWT_SECRET || 'fallback-secret');
    const token = await new SignJWT({ 
      userId: user.id, 
      email: user.email, 
      role: user.role 
    })
      .setProtectedHeader({ alg: 'HS256' })
      .setIssuedAt()
      .setExpirationTime(manterConectado ? '30d' : '8h')
      .sign(secret);

    // Salvar sessão no banco
    const expiresAt = new Date();
    expiresAt.setTime(
      expiresAt.getTime() + (manterConectado ? 30 * 24 * 60 * 60 * 1000 : 8 * 60 * 60 * 1000)
    );

    const userAgent = request.headers.get('user-agent') || '';
    const ipAddress = request.headers.get('x-forwarded-for') || 
                     request.headers.get('x-real-ip') || 
                     'unknown';

    await prisma.adminSession.create({
      data: {
        adminUserId: user.id,
        token,
        expiresAt,
        persistent: manterConectado,
        ipAddress,
        userAgent,
      },
    });

    // Atualizar último login
    await prisma.adminUser.update({
      where: { id: user.id },
      data: { ultimoLogin: new Date() },
    });

    // Configurar cookie
    const cookieStore = await cookies();
    cookieStore.set('admin-session', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: manterConectado ? 30 * 24 * 60 * 60 : 8 * 60 * 60, // 30 dias ou 8 horas
      path: '/', // Mudando para '/' para funcionar em toda a aplicação
    });

    return NextResponse.json({
      success: true,
      user: {
        id: user.id,
        email: user.email,
        nome: user.nome,
        role: user.role,
      },
    });

  } catch (error) {
    console.error('Erro no login admin:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
} 