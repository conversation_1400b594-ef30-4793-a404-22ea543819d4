'use client';

import { useEffect, useState } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { hasValidSession, getClienteSession } from '@/lib/persistentSession';
import Image from 'next/image';

export default function ShortUrlRedirectPage() {
  const router = useRouter();
  const params = useParams();
  const [status, setStatus] = useState<'loading' | 'error' | 'redirecting'>('loading');
  const [mensagem, setMensagem] = useState<string>('Processando link...');

  // Log para verificar se a página está sendo executada
  console.log('🚀 ShortUrlRedirectPage carregada!');
  console.log('📋 Params recebidos:', params);

  useEffect(() => {
    const processCode = async () => {
      try {
        const code = params.code as string;
        
        if (!code) {
          setStatus('error');
          setMensagem('Código de acesso inválido');
          return;
        }

        // ✅ ATUALIZADO: Se há sessão ativa, informar que será substituída
        if (hasValidSession()) {
          const session = getClienteSession();
          if (session) {
            console.log('🔄 Usuário já logado, mas processando novo shortcode para substituir sessão...', {
              sessaoAtual: session.nome,
              identificador: session.identificador
            });
            setMensagem(`Substituindo sessão de ${session.nome}...`);
          }
        }

        console.log('🔗 Processando shortcode:', code);
        setMensagem('Verificando link...');

        // Buscar token longo via API
        const response = await fetch(`/api/short-url/${code}`);
        console.log('📡 Response status:', response.status);
        
        if (!response.ok) {
          const errorData = await response.json();
          console.log('❌ Error data:', errorData);
          throw new Error(errorData.error || 'Link inválido ou expirado');
        }

        const data = await response.json();
        
        if (!data.longToken) {
          throw new Error('Token não encontrado');
        }

        console.log('✅ Shortcode válido, redirecionando para auto-login');
        setStatus('redirecting');
        setMensagem('Link válido! Fazendo login automático...');

        // Redirecionar para auto-login com token longo
        const autoLoginUrl = `/auth/auto?token=${data.longToken}`;
        
        setTimeout(() => {
          router.push(autoLoginUrl);
        }, 1000);

      } catch (error: any) {
        console.error('❌ Erro ao processar shortcode:', error);
        setStatus('error');
        setMensagem(error.message || 'Link inválido ou expirado');
        
        // Redirecionar para login manual após erro
        setTimeout(() => {
          router.push('/');
        }, 3000);
      }
    };

    processCode();
  }, [params.code, router]);

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col items-center justify-center p-4">
      <div className="bg-white shadow-lg rounded-lg p-8 max-w-md w-full text-center">
        {/* Logo */}
        <div className="mb-6">
          <Image
            src="/logo.svg"
            alt="Logo"
            width={200}
            height={80}
            className="mx-auto"
          />
        </div>

        {/* Status */}
        <div className="space-y-4">
          {status === 'loading' && (
            <div className="flex flex-col items-center">
              <svg
                className="animate-spin h-8 w-8 text-blue-500 mb-4"
                viewBox="0 0 24 24"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                ></circle>
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
              <p className="text-gray-800 text-lg">{mensagem}</p>
            </div>
          )}

          {status === 'redirecting' && (
            <div className="flex flex-col items-center">
              <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center mb-4">
                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                </svg>
              </div>
              <p className="text-green-600 text-lg font-semibold">{mensagem}</p>
            </div>
          )}

          {status === 'error' && (
            <div className="flex flex-col items-center">
              <div className="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center mb-4">
                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
              </div>
              <p className="text-red-600 text-lg">{mensagem}</p>
              <p className="text-gray-500 text-sm mt-2">
                Você será redirecionado para a página de login em alguns segundos...
              </p>
            </div>
          )}
        </div>

        {/* Link manual */}
        {status === 'error' && (
          <div className="mt-6 pt-4 border-t border-gray-200">
            <button
              onClick={() => router.push('/')}
              className="text-blue-600 hover:text-blue-800 underline text-sm"
            >
              Ir para login manual
            </button>
          </div>
        )}
      </div>
    </div>
  );
} 