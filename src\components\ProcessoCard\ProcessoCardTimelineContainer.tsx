import React from 'react';
import TimelineUnificada from '@/components/timeline/TimelineUnificada';

interface ProcessoCardTimelineContainerProps {
  etapasTimeline?: any[]; // Novo prop para as etapas
  dataMeritoEstimadaFormatada: string | null;
  useSpacingForTimeline: boolean;
  showOposicaoBadge: boolean;
  estimativasDb?: {
    mediaSemIntervencoes?: number | null;
    mediaComOposicao?: number | null;
    mediaComSobrestamento?: number | null;
    mediaComExigencia?: number | null;
  } | null;
  processo?: any; // Adicionar processo
}

const ProcessoCardTimelineContainer: React.FC<ProcessoCardTimelineContainerProps> = ({ 
    etapasTimeline, 
    dataMeritoEstimadaFormatada, 
    useSpacingForTimeline,
    showOposicaoBadge,
    estimativasDb,
    processo
}) => {
  return (
    <div className={`${useSpacingForTimeline ? 'mt-[50px]' : 'mt-4'}`}>
        {etapasTimeline && etapasTimeline.length > 0 ? (
            <TimelineUnificada 
              etapas={etapasTimeline} 
              showOposicaoBadge={showOposicaoBadge}
              estimativasDb={estimativasDb}
              processo={processo}
            />
        ) : dataMeritoEstimadaFormatada ? (
            <div className="mt-4 text-right">
                <p className="text-sm text-[#4597B5]">
                Previsão:{" "}
                <span className="font-bold">
                    {dataMeritoEstimadaFormatada}
                </span>
                </p>
            </div>
        ) : null}
    </div>
  );
};

export default ProcessoCardTimelineContainer; 