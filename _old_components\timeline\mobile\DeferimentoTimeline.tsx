import { format, addDays } from 'date-fns';
import { ptBR } from 'date-fns/locale';

interface DeferimentoTimelineProps {
  dataDeferimento: Date;
  taxaPaga: boolean; // Combinação de crmStageId === 280246 ou taxaConcessaoPaga === true
  isRecursoProvido: boolean; // Nova prop
}

// --- Interfaces e Funções Auxiliares --- 
interface ProgressoInfo {
  etapaAtual: number; // Índice do marcador inicial da etapa atual (0: Def->Ord, 1: Ord->Ext/Conc, etc.)
  progressoNaEtapaAtual: number; // 0 a 1
}

// Função auxiliar para verificar se um marcador está completo
function calcularSeMarcadorCompleto(index: number, progInfo: ProgressoInfo): boolean {
  return index < progInfo.etapaAtual || 
         (index === progInfo.etapaAtual && progInfo.progressoNaEtapaAtual === 1);
}

// Função para calcular o progresso entre duas datas
function calcularProgressoEntreDatas(hojeMs: number, inicioMs: number | null, fimMs: number | null): number {
  if (inicioMs === null || fimMs === null || fimMs <= inicioMs) {
    return 0; // Não há intervalo válido
  }
  const duracaoEtapa = fimMs - inicioMs;
  const tempoDecorrido = hojeMs - inicioMs;
  return Math.max(0, Math.min(1, tempoDecorrido / duracaoEtapa));
}

// --- Componente Principal --- 
export default function DeferimentoTimeline({
  dataDeferimento,
  taxaPaga,
  isRecursoProvido,
}: DeferimentoTimelineProps) {
  const hoje = new Date();
  const hojeMs = hoje.getTime();
  
  // --- Cálculo das Datas --- 
  const dataDeferimentoMs = dataDeferimento.getTime();
  const dataPrazoOrdinario = addDays(dataDeferimento, 60);
  const dataPrazoOrdinarioMs = dataPrazoOrdinario.getTime();
  const dataPrazoExtraordinario = addDays(dataPrazoOrdinario, 30);
  const dataPrazoExtraordinarioMs = dataPrazoExtraordinario.getTime();

  // Cálculo da data de concessão estimada
  const getDataConcessaoEstimada = () => {
    const estimativaDias = isRecursoProvido ? 42 : 47;
    let dataBase;

    if (taxaPaga) {
        // Se taxa foi paga (independente de quando), a base é o prazo ordinário
        // ou hoje se hoje for depois do prazo ordinário (mas isso parece errado para *estimativa* futura)
        // Vamos simplificar: a estimativa SEMPRE parte do prazo máximo.
        // Se pagou antes do prazo ord, base é prazo ord.
        // Se pagou entre ord e extraord, base é prazo extraord.
        // A lógica original estava um pouco confusa, vamos usar o prazo máximo como base da estimativa
        if (hojeMs < dataPrazoOrdinarioMs) { 
            // Ainda não venceu prazo ord, estimativa a partir do prazo ord.
            dataBase = dataPrazoOrdinarioMs;
        } else if (hojeMs < dataPrazoExtraordinarioMs) {
            // Pagou entre ord e extraord, estimativa a partir do prazo extraord
            dataBase = dataPrazoExtraordinarioMs; 
        } else {
             // Pagou após extraord - tecnicamente não deveria conceder, mas vamos estimar a partir do extraord
             dataBase = dataPrazoExtraordinarioMs;
        }

    } else {
      // Se não pagou e está após extraord, não há concessão estimada
      if (hojeMs > dataPrazoExtraordinarioMs) {
        return null; 
      }
      // Se não pagou e ainda está no prazo, estima a partir do fim do prazo extraord
      dataBase = dataPrazoExtraordinarioMs;
    }
    
    return dataBase ? addDays(new Date(dataBase), estimativaDias) : null;
  };

  const dataConcessaoEstimada = getDataConcessaoEstimada();
  const dataConcessaoEstimadaMs = dataConcessaoEstimada?.getTime() ?? null;
  const dataPrazoNulidade = dataConcessaoEstimada ? addDays(dataConcessaoEstimada, 180) : null;
  const dataPrazoNulidadeMs = dataPrazoNulidade?.getTime() ?? null;

  // --- Lógica de Progresso --- 
  const calcularProgresso = (): ProgressoInfo => {
    // 0. Deferimento -> Prazo Ordinário
    if (hojeMs < dataPrazoOrdinarioMs) {
      return { 
        etapaAtual: 0, 
        progressoNaEtapaAtual: calcularProgressoEntreDatas(hojeMs, dataDeferimentoMs, dataPrazoOrdinarioMs) 
      };
    }

    // Situação onde taxa NÃO foi paga
    if (!taxaPaga) {
      // 1a. Prazo Ordinário -> Prazo Extraordinário
      if (hojeMs < dataPrazoExtraordinarioMs) {
        return { 
          etapaAtual: 1, 
          progressoNaEtapaAtual: calcularProgressoEntreDatas(hojeMs, dataPrazoOrdinarioMs, dataPrazoExtraordinarioMs)
        };
      }
      // Após Prazo Extraordinário (sem pagar)
      // Fica "preso" no marcador Extraordinário completo
      return { etapaAtual: 2, progressoNaEtapaAtual: 1 }; 
    }

    // Situação onde taxa FOI paga
    // 1b. Prazo Ordinário -> Concessão Estimada (pulando Extraordinário visualmente)
    if (dataConcessaoEstimadaMs && hojeMs < dataConcessaoEstimadaMs) {
       // O ponto de partida do cálculo depende de *quando* vence o prazo ordinário
      const inicioEtapa = dataPrazoOrdinarioMs; // Estimativa sempre parte do fim dos prazos
      return { 
        etapaAtual: 1, // Ainda na etapa que começa no marcador Ord (índice 1)
        progressoNaEtapaAtual: calcularProgressoEntreDatas(hojeMs, inicioEtapa, dataConcessaoEstimadaMs)
      };
    }
    
    // 2. Concessão Estimada -> Fim Prazo Nulidade
    if (dataPrazoNulidadeMs && hojeMs < dataPrazoNulidadeMs) {
      const inicioEtapa = dataConcessaoEstimadaMs;
      return { 
        etapaAtual: 3, // Etapa que começa no marcador Concessão (índice 3)
        progressoNaEtapaAtual: calcularProgressoEntreDatas(hojeMs, inicioEtapa, dataPrazoNulidadeMs)
      };
    }

    // 3. Após Fim Prazo Nulidade (ou sem datas futuras)
    const ultimaEtapaPossivel = dataPrazoNulidadeMs ? 4 : (dataConcessaoEstimadaMs ? 3 : 1);
    return { etapaAtual: ultimaEtapaPossivel, progressoNaEtapaAtual: 1 }; 
  };

  const progressoInfo = calcularProgresso();

  // --- Componente Interno TimelineMarker --- 
  const TimelineMarker = ({ 
    data, 
    titulo, 
    descricao, 
    isAlert = false,
    isEstimated = false,
    index, 
    isUltimoMarcador, 
    showWarning = false 
  }: { 
    data: Date | null, 
    titulo: string, 
    descricao: string,
    isAlert?: boolean,
    isEstimated?: boolean,
    index: number,
    isUltimoMarcador: boolean,
    showWarning?: boolean
  }) => {
    // Determina conclusão temporal base
    const isTemporallyComplete = calcularSeMarcadorCompleto(index, progressoInfo);

    // Lógica Específica para Marcadores de Taxa
    let finalIsCompleted: boolean;
    let showCheck = false;
    let showCross = false;

    if (index === 1) { // Marcador 1: Prazo Ordinário
      finalIsCompleted = taxaPaga; // Completo *apenas* se taxaPaga for true
      showCheck = taxaPaga; 
      // isAlert é passado via props e determina a cor da borda/texto se !finalIsCompleted
    } else if (index === 2) { // Marcador 2: Prazo Extraordinário
      finalIsCompleted = false; // Nunca mostra check azul
      showCheck = false;
      showCross = isAlert; // Mostra X se alerta estiver ativo (prazo vencido)
      // isAlert é passado via props
    } else { // Outros marcadores (Deferimento, Concessão, Nulidade)
      finalIsCompleted = isTemporallyComplete;
      showCheck = finalIsCompleted;
    }

    // Linhas de progresso continuam baseadas na progressão temporal
    const showBlueLine = index < progressoInfo.etapaAtual || (index === progressoInfo.etapaAtual && progressoInfo.progressoNaEtapaAtual > 0);
    const blueLineHeightPercent = index < progressoInfo.etapaAtual ? 100 : progressoInfo.progressoNaEtapaAtual * 100;

    // Determinar classes CSS com base nos estados finais
    const markerClass = finalIsCompleted
      ? "bg-[#4597B5] border-2 border-[#4597B5]" // Completo (Azul)
      : isAlert
        ? "border-2 border-red-500 bg-white"    // Alerta (Borda Vermelha)
        : "border-2 border-[#C3C3C3] bg-white";   // Pendente (Borda Cinza)

    const textClass = isAlert && !finalIsCompleted ? 'text-red-600' : 'text-black';
    const dateClass = isAlert && !finalIsCompleted ? 'text-red-600' : 'text-[#A3A3A3]';

    return (
      <div className="flex relative"> 
        {/* Data (lado esquerdo) */}
        <div className="w-24 flex-shrink-0 mr-4 text-right">
          {data && (
            <span className={`text-sm font-medium ${dateClass}`}> 
              {format(data, "dd/MM/yyyy")}
              {isEstimated && <em className="text-[10px] not-italic"> (est.)</em>}
            </span>
          )}
        </div>

        {/* Marcador e Linha de Conexão (Centro) */}
        <div className="flex flex-col items-center w-5 relative">
          {/* Marcador */}
          <div className={`w-5 h-5 rounded-full shadow-md flex items-center justify-center ${markerClass} relative z-10`}> 
            {showCheck && ( // Usa showCheck calculado
              <svg className="w-3.5 h-3.5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M5 13l4 4L19 7" />
              </svg>
            )}
            {showCross && ( // Usa showCross calculado
              <span className="text-xs text-red-500 font-bold">X</span>
            )}
          </div>

          {/* Linhas de Conexão (Lógica inalterada) */}
          {!isUltimoMarcador && (
            <div className="absolute top-5 left-1/2 transform -translate-x-1/2 w-1 h-full"> 
              <div className="absolute top-0 left-0 w-full h-full bg-[#C3C3C3]"></div>
              {showBlueLine && (
                <div
                  className="absolute top-0 left-0 w-full bg-[#4597B5] transition-height duration-300 ease-in-out"
                  style={{ height: `${blueLineHeightPercent}%` }}
                ></div>
              )}
            </div>
          )}
        </div>

        {/* Texto (lado direito) */}
        <div className="ml-4 flex-grow pb-6"> 
          <h3 className={`text-base font-semibold leading-tight ${textClass}`}> 
            {titulo}
          </h3>
          <p className="text-sm text-gray-500 mt-0.5">{descricao}</p>
          
          {showWarning && ( // Aviso de prazo vencendo
            <span className="text-red-600 text-xs font-normal italic flex items-center gap-1 mt-0.5">
              ⚠️ Prazo {titulo.includes("extraordinário") ? "extraordinário" : "ordinário"} vencendo
            </span>
          )}
        </div>
      </div>
    );
  };

  // --- Determinar qual é o último marcador visível --- 
  let ultimoIndiceVisivel = 1; // Pelo menos Deferimento (0) e Prazo Ord (1) são visíveis
  const mostraExtraordinario = !taxaPaga && hojeMs >= dataPrazoOrdinarioMs;
  const mostraConcessao = !!dataConcessaoEstimada;
  const mostraNulidade = !!dataPrazoNulidade;

  if (mostraNulidade) ultimoIndiceVisivel = 4;
  else if (mostraConcessao) ultimoIndiceVisivel = 3;
  else if (mostraExtraordinario) ultimoIndiceVisivel = 2;

  // --- Renderização Principal --- 
  return (
    <div className="pt-4 pb-2 px-2">
        {/* Marcador 0: Deferimento */}
        <TimelineMarker
          index={0} 
          data={dataDeferimento}
          titulo="Deferimento"
          descricao="Data em que o INPI deferiu o pedido de registro."
          isUltimoMarcador={ultimoIndiceVisivel === 0} 
        />
        
        {/* Marcador 1: Prazo Ordinário */}
        <TimelineMarker
          index={1}
          data={dataPrazoOrdinario}
          titulo="Taxa de Concessão"
          descricao="Prazo para pagamento da taxa com valor normal."
          isAlert={!taxaPaga && hojeMs >= dataPrazoOrdinarioMs} // Alerta se venceu e não pagou
          showWarning={!taxaPaga && hojeMs < dataPrazoOrdinarioMs && hojeMs >= addDays(dataPrazoOrdinario, -7).getTime()} // Aviso 7 dias antes se não pagou
          isUltimoMarcador={ultimoIndiceVisivel === 1}
        />
        
        {/* Marcador 2: Prazo Extraordinário (Condicional) */}
        {mostraExtraordinario && (
          <TimelineMarker
            index={2}
            data={dataPrazoExtraordinario}
            titulo="Taxa de Concessão (extraordinário)"
            descricao="Último prazo para pagamento da taxa com valor adicional."
            isAlert={!taxaPaga && hojeMs >= dataPrazoExtraordinarioMs} // Alerta se venceu
            showWarning={!taxaPaga && hojeMs < dataPrazoExtraordinarioMs && hojeMs >= addDays(dataPrazoExtraordinario, -7).getTime()} // Aviso 7 dias antes
            isUltimoMarcador={ultimoIndiceVisivel === 2}
          />
        )}
        
        {/* Marcador 3: Concessão Estimada (Condicional) */}
        {mostraConcessao && (
          <TimelineMarker
            index={3}
            data={dataConcessaoEstimada}
            titulo="Concessão"
            descricao="Data estimada para concessão do registro pelo INPI."
            isEstimated={true}
            isUltimoMarcador={ultimoIndiceVisivel === 3}
          />
        )}
        
        {/* Marcador 4: Fim Prazo Nulidade (Condicional) */}
        {mostraNulidade && (
          <TimelineMarker
            index={4}
            data={dataPrazoNulidade}
            titulo="Fim do prazo de nulidade"
            descricao="Data limite para terceiros pedirem a nulidade do registro."
            isEstimated={true}
            isUltimoMarcador={ultimoIndiceVisivel === 4}
          />
        )}
    </div>
  );
} 