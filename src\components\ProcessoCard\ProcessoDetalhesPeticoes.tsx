import React from 'react';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';

// Interfaces baseadas no schema.prisma e uso anterior
// (Idealmente importadas de tipos compartilhados)
interface DespachoRelacionado {
  nome: string | null;
  // Outros campos do Despacho se necessários para complemento
}

interface ProtocoloDespacho {
  id: string;
  numero: string;
  data: Date | string; // Pode ser Date ou string dependendo do fetch
  codigoServico: string;
  requerenteNomeRazaoSocial: string;
  // Adiciona a relação com Despacho para pegar a descrição
  Despacho?: DespachoRelacionado | null;
  // Adiciona campo para complemento (ajustar conforme dados reais)
  complemento?: string | null;
}

// Adiciona a função auxiliar para links (igual a de Citacoes e Despachos)
const linkifyProcessNumbers = (text: string): React.ReactNode[] => {
  if (!text) return [text];
  // Ajusta a regex para pegar exatamente 9 dígitos usando word boundaries (\b)
  const processNumberRegex = /\b(\d{9})\b/g;
  const parts: React.ReactNode[] = [];
  let lastIndex = 0;
  let match;

  while ((match = processNumberRegex.exec(text)) !== null) {
    const index = match.index;
    const number = match[0];
    if (index > lastIndex) {
      parts.push(text.substring(lastIndex, index));
    }
    parts.push(
      <a
        key={`${number}-${index}`}
        href={`/processo/${number}`}
        target="_blank"
        rel="noopener noreferrer"
        // Remove text-blue-600, usa text-current e mantém hover:underline
        className="text-current underline font-extrabold hover:text-blue-600"
      >
        {number}
      </a>
    );
    lastIndex = index + number.length;
  }
  if (lastIndex < text.length) {
    parts.push(text.substring(lastIndex));
  }
  return parts.length > 0 ? parts : [text];
};

interface ProcessoDetalhesPeticoesProps {
  peticoes: ProtocoloDespacho[]; // Ajusta a prop para não ser null/undefined, pois só renderiza se houver itens
}

// Helper para formatar data
const formatDate = (dateInput: Date | string | null | undefined): string => {
  if (!dateInput) return '--';
  try {
    // Converte para Date se for string
    const date = typeof dateInput === 'string' ? new Date(dateInput) : dateInput;
    return format(date, 'dd/MM/yyyy', { locale: ptBR });
  } catch (e) {
    return 'Inválida';
  }
};

// Função para formatar o código de serviço
const formatCodigoServico = (codigo: string | null | undefined): string => {
  if (!codigo) return "--";

  const codigosEspeciais: Record<string, string> = {
    "30001": "3000.1",
    "30041": "3004.1",
    "30051": "3005.1",
    "30151": "3015.1",
    "30161": "3016.1",
    "30171": "3017.1",
  };

  if (codigosEspeciais[codigo]) {
    return codigosEspeciais[codigo];
  }

  if (codigo.length > 3) {
    return `${codigo.substring(0, 3)}.${codigo.substring(3)}`;
  }

  return codigo;
};

const ProcessoDetalhesPeticoes: React.FC<ProcessoDetalhesPeticoesProps> = ({ peticoes }) => {
  // Ordenar petições: mais recentes primeiro
  const peticoesOrdenadas = [...peticoes].sort((a, b) => {
    const dataA = a.data ? new Date(a.data).getTime() : 0;
    const dataB = b.data ? new Date(b.data).getTime() : 0;
    return dataB - dataA; // Ordem decrescente
  });

  // Lógica para obter complemento (similar à tabela de despachos, ajustar)
  const getComplementoText = (peticao: ProtocoloDespacho): string => {
    if (peticao.complemento) {
      return peticao.complemento;
    }
    // Exemplo: pegar complemento do despacho relacionado?
    // if (peticao.Despacho?.complemento) { return peticao.Despacho.complemento; }
    if (peticao.Despacho?.nome?.toLowerCase().includes('deferimento')){
        return 'Sem direito ao uso exclusivo dos elementos nominativos.'; // Exemplo da imagem
    }
    return 'Sem complemento'; // Padrão
  };

  return (
    <div className="mb-6 overflow-x-auto">
      <h4
        className="text-base font-semibold mb-0 uppercase text-center text-white py-1"
        style={{ backgroundColor: "var(--color-headerTabela)" }}
      >
        Petições
      </h4>
      <table
        className="min-w-full text-sm"
        style={{ borderBottom: "2px solid var(--color-bordaTabela)" }}
      >
        <thead
          className=" text-gray-700"
          style={{ backgroundColor: "var(--color-linhaTabela)" }}
        >
          <tr>
            <th className="px-3 py-2 text-center font-semibold tracking-wider w-[20%] text-black text-xs">
              PROTOCOLO
            </th>
            <th className="px-3 py-2 text-center font-semibold tracking-wider w-[15%] text-black text-xs">
              DATA
            </th>
            <th className="px-3 py-2 text-center font-semibold tracking-wider w-[10%] text-black text-xs">
              SERVIÇO
            </th>
            <th className="px-3 py-2 text-left font-semibold tracking-wider w-[25%] text-black text-xs">
              DESCRIÇÃO
            </th>
            <th className="px-3 py-2 text-left font-semibold tracking-wider w-[30%] text-black text-xs">
              COMPLEMENTO
            </th>
          </tr>
        </thead>
        <tbody>
          {peticoesOrdenadas.map((peticao, index) => (
            <tr
              key={peticao.id}
              style={{
                backgroundColor:
                  index % 2 === 0 ? "white" : "var(--color-linhaTabela)",
              }}
            >
              <td className="px-3 py-2 whitespace-nowrap text-black font-semibold text-xs text-center">
                {peticao.numero || "--"}
              </td>
              <td className="px-3 py-2 whitespace-nowrap text-black font-semibold text-xs text-center">
                {formatDate(peticao.data)}
              </td>
              <td className="px-3 py-2 whitespace-nowrap text-black font-semibold text-xs text-center">
                {formatCodigoServico(peticao.codigoServico)}
              </td>
              {/* Usa o nome do Despacho relacionado como Descrição */}
              <td className="px-3 py-2 text-black font-semibold text-xs text-left">{peticao.Despacho?.nome || "--"}</td>
              {/* Usa linkifyProcessNumbers para o complemento */}
              <td className="px-3 py-2 text-black font-semibold text-xs text-left whitespace-normal">
                 {linkifyProcessNumbers(getComplementoText(peticao))}
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default ProcessoDetalhesPeticoes; 