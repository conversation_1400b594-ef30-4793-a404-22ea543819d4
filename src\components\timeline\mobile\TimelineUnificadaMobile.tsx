import React, { useMemo } from 'react';
import styles from './TimelineUnificadaMobile.module.css';
import {
  EtapaTimeline,
  filtrarEtapasLimpas,
  formatarDataExibicaoMobile,
  isPrazoExpirado,
  temSobrestamento,
  calcularStatusMarcador,
  isRecursoExpiradoSemApresentacao,
  getTipoEtapa,
  calcularDataEstimadaConcessao,
  formatarEtapaTaxaConcessao,
  calcularDataEstimadaFimPrazoNulidade,
  isPrazoManifestacao,
  isExigenciaAtiva,
  isTaxaConcessao,
  verificarParadaSobrestamento,
  calcularTimelineArquivamento,
  calcularTimelineCompleta,
  calcularNumeroRenovacao
} from '../timelineUtils';

interface TimelineUnificadaMobileProps {
  etapas: EtapaTimeline[];
  estimativasDb?: {
    mediaSemIntervencoes?: number | null;
    mediaComOposicao?: number | null;
    mediaComSobrestamento?: number | null;
    mediaComExigencia?: number | null;
  } | null;
  processo?: any;
}

// Função para verificar se é um processo não publicado (mesmo código da versão desktop)
const isProcessoNaoPublicado = (etapas: EtapaTimeline[], processo?: any): boolean => {
  // PRIMEIRA VERIFICAÇÃO: Se não há etapas, é processo não publicado
  if (!etapas || etapas.length === 0) return true;
  
  // SEGUNDA VERIFICAÇÃO: Verificar se o processo tem despachos
  // Se o processo original tem dataDeposito mas não tem despachos, é processo não publicado
  if (processo?.dataDeposito && (!processo.Despacho || processo.Despacho.length === 0)) {
    return true;
  }
  
  // TERCEIRA VERIFICAÇÃO: Se há apenas protocolo, é processo não publicado
  if (etapas.length === 1 && getTipoEtapa(etapas[0]) === 'protocolo') return true;
  
  // QUARTA VERIFICAÇÃO: Se não há nenhuma etapa de publicação real (não estimada), é processo não publicado
  const temPublicacaoReal = etapas.some(etapa => {
    // Excluir publicações estimadas criadas pelo sistema
    if (etapa.idOriginal === 'PUBLICACAO_ESTIMADA_01') return false;
    
    return getTipoEtapa(etapa) === 'publicacao' || 
           etapa.idOriginal === 'PUBLICACAO_01' ||
           etapa.idOriginal === 'PUBLICACAO_COM_OPOSICAO_01' ||
           (etapa.etapaDefinicao.nomeOriginal.toLowerCase().includes('publicação') && 
            etapa.status === 'CONCLUIDA'); // Só considerar publicações concluídas
  });
  
  return !temPublicacaoReal;
};

// Função para verificar e adicionar etapa de análise da nulidade
const adicionarAnaliseNulidade = (etapas: EtapaTimeline[]): EtapaTimeline[] => {
  // Verificar se existem as etapas necessárias
  const etapaProcessoNulidade = etapas.find(etapa => 
    etapa.idOriginal === 'PROCESSO_NULIDADE_INSTAURADO_01'
  );
  
  const etapaPrazoManifestacao = etapas.find(etapa => 
    etapa.idOriginal === 'PRAZO_MANIFESTACAO_NULIDADE_01'
  );
  
  // Se não existem ambas as etapas, retornar as etapas originais
  if (!etapaProcessoNulidade || !etapaPrazoManifestacao) {
    return etapas;
  }
  
  // Verificar se já existe a etapa de análise da nulidade
  const jaTemAnaliseNulidade = etapas.some(etapa => 
    etapa.idOriginal === 'ANALISE_NULIDADE_ESTIMADA_01'
  );
  
  if (jaTemAnaliseNulidade) {
    return etapas;
  }
  
  // Calcular data de análise da nulidade (50 meses após prazo de manifestação)
  const dataPrazoManifestacao = etapaPrazoManifestacao.dataInicio || etapaPrazoManifestacao.dataFim;
  
  if (!dataPrazoManifestacao) {
    return etapas;
  }
  
  const dataAnaliseNulidade = new Date(dataPrazoManifestacao);
  dataAnaliseNulidade.setMonth(dataAnaliseNulidade.getMonth() + 50);
  
  // Criar etapa de análise da nulidade
  const analiseNulidadeEstimada: EtapaTimeline = {
    idOriginal: 'ANALISE_NULIDADE_ESTIMADA_01',
    etapaDefinicao: {
      nomeOriginal: 'Análise da nulidade',
      statusSimples: 'Aguardando Análise da Nulidade',
      tipo: 'ESTIMATIVA'
    },
    status: 'PROXIMA_ESTIMADA',
    dataInicio: dataAnaliseNulidade,
    dataFim: null,
    observacaoGerada: 'Estimativa de análise da nulidade (50 meses após prazo de manifestação)'
  };
  
  // Encontrar posição para inserir (após o prazo de manifestação)
  const indicePrazoManifestacao = etapas.findIndex(etapa => 
    etapa.idOriginal === 'PRAZO_MANIFESTACAO_NULIDADE_01'
  );
  
  // Criar nova lista de etapas com a análise da nulidade inserida
  const novasEtapas = [...etapas];
  novasEtapas.splice(indicePrazoManifestacao + 1, 0, analiseNulidadeEstimada);
  
  return novasEtapas;
};

// Função para criar etapas de processo não publicado (versão mobile)
const criarEtapasNaoPublicadas = (etapas: EtapaTimeline[], estimativasDb?: any): EtapaTimeline[] => {
  // Encontrar a etapa de protocolo
  const etapaProtocolo = etapas.find(etapa => getTipoEtapa(etapa) === 'protocolo');
  
  if (!etapaProtocolo) return [];
  
  const dataProtocolo = etapaProtocolo.dataInicio || etapaProtocolo.dataFim;
  if (!dataProtocolo) return [etapaProtocolo];

  const etapasEstimadas: EtapaTimeline[] = [];
  
  // 1. Protocolo (real)
  etapasEstimadas.push(etapaProtocolo);

  // 2. Publicação (estimada)
  const publicacaoEstimada: EtapaTimeline = {
    idOriginal: 'PUBLICACAO_ESTIMADA_01',
    etapaDefinicao: {
      nomeOriginal: 'Publicação',
      statusSimples: 'Aguardando Publicação',
      tipo: 'ESTIMATIVA'
    },
    status: 'PROXIMA_ESTIMADA',
    dataInicio: null,
    dataFim: null
  };
  etapasEstimadas.push(publicacaoEstimada);

  // 3. Fim da fase de oposição (estimado)
  const fimOposicaoEstimado: EtapaTimeline = {
    idOriginal: 'FIM_OPOSICAO_ESTIMADO_01',
    etapaDefinicao: {
      nomeOriginal: 'Fim da fase de oposição',
      statusSimples: 'Aguardando Fim da Oposição',
      tipo: 'ESTIMATIVA'
    },
    status: 'PROXIMA_ESTIMADA',
    dataInicio: null,
    dataFim: null
  };
  etapasEstimadas.push(fimOposicaoEstimado);

  // 4. Análise de mérito (estimada)
  let dataEstimadaAnalise = new Date(dataProtocolo);
  
  // Usar estimativas de mérito se disponíveis
  if (estimativasDb?.mediaSemIntervencoes) {
    dataEstimadaAnalise.setDate(dataEstimadaAnalise.getDate() + estimativasDb.mediaSemIntervencoes);
  } else {
    // Fallback: 450 dias (média aproximada)
    dataEstimadaAnalise.setDate(dataEstimadaAnalise.getDate() + 450);
  }

  const analiseEstimada: EtapaTimeline = {
    idOriginal: 'ANALISE_MERITO_ESTIMADA_01',
    etapaDefinicao: {
      nomeOriginal: 'Análise de mérito',
      statusSimples: 'Aguardando Análise de Mérito',
      tipo: 'ESTIMATIVA'
    },
    status: 'PROXIMA_ESTIMADA',
    dataInicio: dataEstimadaAnalise,
    dataFim: null
  };
  etapasEstimadas.push(analiseEstimada);

  return etapasEstimadas;
};

const TimelineUnificadaMobile: React.FC<TimelineUnificadaMobileProps> = ({ etapas, estimativasDb, processo }) => {
  // const [showDebugModal, setShowDebugModal] = useState(false);

  // Filtrar etapas inválidas (como "Em Andamento" quando há outras etapas válidas)
  const etapasLimpas = useMemo(() => filtrarEtapasLimpas(etapas), [etapas]);

  // Usar a MESMA lógica de filtros da versão desktop
  const etapasParaExibir = useMemo(() => {
    if (!etapasLimpas || etapasLimpas.length === 0) return [];

    // Aplicar lógica de análise da nulidade antes de processar as etapas
    const etapasComAnaliseNulidade = adicionarAnaliseNulidade(etapasLimpas);

    // CASO ESPECIAL: Processo não publicado
    if (isProcessoNaoPublicado(etapasComAnaliseNulidade, processo)) {
      // Montar etapas estimadas (protocolo + estimadas) igual ao desktop
      const etapaProtocolo = etapasComAnaliseNulidade.find(e => getTipoEtapa(e) === 'protocolo');
      if (!etapaProtocolo) return [];
      const dataProtocolo = etapaProtocolo.dataInicio || etapaProtocolo.dataFim;
      const publicacaoEstimada = {
        idOriginal: 'PUBLICACAO_ESTIMADA_01',
        etapaDefinicao: {
          nomeOriginal: 'Publicação',
          statusSimples: 'Aguardando Publicação',
          tipo: 'ESTIMATIVA'
        },
        status: 'PROXIMA_ESTIMADA' as const,
        dataInicio: dataProtocolo ? new Date(new Date(dataProtocolo).getTime() + 1000 * 60 * 60 * 24 * 17) : undefined,
        dataFim: null,
        observacaoGerada: undefined
      };
      const fimOposicaoEstimado = {
        idOriginal: 'FIM_OPOSICAO_ESTIMADO_01',
        etapaDefinicao: {
          nomeOriginal: 'Fim da fase de oposição',
          statusSimples: 'Aguardando Fim da Oposição',
          tipo: 'ESTIMATIVA'
        },
        status: 'PROXIMA_ESTIMADA' as const,
        dataInicio: publicacaoEstimada.dataInicio ? new Date(new Date(publicacaoEstimada.dataInicio).getTime() + 1000 * 60 * 60 * 24 * 60) : undefined,
        dataFim: null,
        observacaoGerada: undefined
      };
      const analiseMeritoEstimado = {
        idOriginal: 'ANALISE_MERITO_ESTIMADA_01',
        etapaDefinicao: {
          nomeOriginal: 'Análise de mérito',
          statusSimples: 'Aguardando Análise de Mérito',
          tipo: 'ESTIMATIVA'
        },
        status: 'PROXIMA_ESTIMADA' as const,
        dataInicio: dataProtocolo ? new Date(new Date(dataProtocolo).getTime() + 1000 * 60 * 60 * 24 * 600) : undefined,
        dataFim: null,
        observacaoGerada: undefined
      };
      const etapasEstimadas = [
        etapaProtocolo,
        publicacaoEstimada,
        fimOposicaoEstimado,
        analiseMeritoEstimado
      ];
      const estimativasDbFormatado = {
        dataPublicacaoEstimada: publicacaoEstimada.dataInicio ? publicacaoEstimada.dataInicio.toISOString() : undefined,
        dataFimOposicaoEstimado: fimOposicaoEstimado.dataInicio ? fimOposicaoEstimado.dataInicio.toISOString() : undefined,
        dataAnaliseMeritoEstimada: analiseMeritoEstimado.dataInicio ? analiseMeritoEstimado.dataInicio.toISOString() : undefined
      };
      const { marcadores } = calcularTimelineCompleta(etapasEstimadas, {
        tipo: 'normal',
        etapas: etapasEstimadas,
        estimativasDb: estimativasDbFormatado,
        usarEscalaLogaritmica: false
      });
      return marcadores;
    }

    // Verificar tipo de timeline - IGUAL à versão desktop
    const etapaArquivamento = etapasComAnaliseNulidade.find(etapa => getTipoEtapa(etapa) === 'arquivamento');
    const etapaIndeferimento = etapasComAnaliseNulidade.find(etapa => getTipoEtapa(etapa) === 'indeferimento');
    const etapaDeferimento = etapasComAnaliseNulidade.find(etapa => getTipoEtapa(etapa) === 'deferimento');
    const etapaConcessao = etapasComAnaliseNulidade.find(etapa => getTipoEtapa(etapa) === 'concessao');

    // CASO ESPECIAL: Timeline de arquivamento
    if (etapaArquivamento) {
      const etapaProtocolo = etapasComAnaliseNulidade.find(etapa => getTipoEtapa(etapa) === 'protocolo');
      return [etapaProtocolo, etapaArquivamento].filter(Boolean) as EtapaTimeline[];
    }

    // CASO ESPECIAL: Timeline de indeferimento
    if (etapaIndeferimento) {
      const indiceIndeferimento = etapasComAnaliseNulidade.findIndex(etapa => etapa.idOriginal === etapaIndeferimento.idOriginal);
      return etapasComAnaliseNulidade.slice(indiceIndeferimento);
    }

    // CASO ESPECIAL: Timeline de concessão (registro concedido)
    if (etapaConcessao) {
      // Encontrar o protocolo para incluir na timeline de concessão
      const etapaProtocolo = etapasComAnaliseNulidade.find(etapa => 
        etapa.idOriginal === 'PROTOCOLO_01' || 
        etapa.etapaDefinicao.nomeOriginal.toLowerCase().includes('protocolo')
      );
      
      // Encontrar a renovação para garantir que apareça
      const etapaRenovacao = etapasComAnaliseNulidade.find(etapa => 
        etapa.idOriginal === 'RENOVACAO_01' || 
        etapa.etapaDefinicao.nomeOriginal.toLowerCase().includes('renovação')
      );
      
      const indiceConcessao = etapasComAnaliseNulidade.findIndex(etapa => 
        etapa.idOriginal === 'CONCESSAO_REGISTRO_01' || 
        etapa.etapaDefinicao.nomeOriginal.toLowerCase().includes('concessão')
      );
      
      // Construir etapas para timeline de concessão
      let etapasConcessao: EtapaTimeline[] = [];
      
      // 1. Adicionar protocolo se existir
      if (etapaProtocolo) {
        etapasConcessao.push(etapaProtocolo);
      }
      
      // 2. Adicionar etapas a partir da concessão
      if (indiceConcessao !== -1) {
        const etapasApartirConcessao = etapasComAnaliseNulidade.slice(indiceConcessao);
        etapasConcessao.push(...etapasApartirConcessao);
      }
      
      // 3. Garantir que renovação está incluída (se não estiver já)
      if (etapaRenovacao && !etapasConcessao.some(e => e.idOriginal === etapaRenovacao.idOriginal)) {
        etapasConcessao.push(etapaRenovacao);
      }
      
      // Remover duplicatas mantendo a ordem
      return etapasConcessao.filter((etapa, index, array) => 
        array.findIndex(e => e.idOriginal === etapa.idOriginal) === index
      );
    }

    // CASO ESPECIAL: Timeline de deferimento (pedido deferido mas sem concessão ainda)
    if (etapaDeferimento && !etapaConcessao) {
      const indiceDeferimento = etapasComAnaliseNulidade.findIndex(etapa => 
        etapa.idOriginal === 'DEFERIMENTO_PEDIDO_01' || 
        etapa.idOriginal === 'DEFERIMENTO_POS_RECURSO_01'
      );
      
      // Se não encontrou pelo ID, tentar pelo nome
      const indiceDeferimentoPorNome = indiceDeferimento === -1 ? 
        etapasComAnaliseNulidade.findIndex(etapa => 
          etapa.etapaDefinicao.nomeOriginal.toLowerCase().includes('deferimento')
        ) : indiceDeferimento;
      
      // Se encontrou o deferimento, usar a partir dele
      if (indiceDeferimentoPorNome !== -1) {
        let etapasApartirDeferimento = [...etapasComAnaliseNulidade.slice(indiceDeferimentoPorNome)];
        
        // Verificar se já existe concessão real nas etapas
        const temConcessaoReal = etapasApartirDeferimento.some(e => 
          e.idOriginal === 'CONCESSAO_REGISTRO_01'
        );
        
        // Se não há concessão real, criar concessão estimada
        if (!temConcessaoReal) {
          const dataEstimadaConcessao = calcularDataEstimadaConcessao(etapasComAnaliseNulidade);
          
          if (dataEstimadaConcessao) {
            // Criar etapa de concessão estimada
            const concessaoEstimada: EtapaTimeline = {
              idOriginal: 'CONCESSAO_ESTIMADA_01',
              etapaDefinicao: {
                nomeOriginal: 'Concessão',
                statusSimples: 'Aguardando Concessão',
                tipo: 'ESTADO_GERAL'
              },
              status: 'PROXIMA_ESTIMADA',
              dataInicio: dataEstimadaConcessao,
              dataFim: null,
              observacaoGerada: 'Estimativa de concessão baseada no prazo de pagamento da taxa'
            };
            
            // Criar etapa de fim do prazo de nulidade estimado (180 dias após a concessão)
            const dataFimPrazoNulidade = calcularDataEstimadaFimPrazoNulidade(dataEstimadaConcessao);
            const fimPrazoNulidadeEstimado: EtapaTimeline = {
              idOriginal: 'FIM_PRAZO_NULIDADE_ESTIMADO_01',
              etapaDefinicao: {
                nomeOriginal: 'Prazo de Nulidade',
                statusSimples: 'Prazo de Nulidade',
                tipo: 'PRAZO'
              },
              status: 'PROXIMA_ESTIMADA',
              dataInicio: dataFimPrazoNulidade,
              dataFim: null,
              observacaoGerada: 'Fim do prazo de 180 dias para pedido de nulidade após a concessão'
            };
            
            // Adicionar as etapas estimadas ao final
            etapasApartirDeferimento.push(concessaoEstimada);
            etapasApartirDeferimento.push(fimPrazoNulidadeEstimado);
          }
        }
        
        return etapasApartirDeferimento;
      }
    }

    // CASO NORMAL: Timeline padrão
    return etapasComAnaliseNulidade;
  }, [etapasLimpas, estimativasDb, processo]);

  // Calcular a altura da barra de progresso
  const progressHeight = useMemo(() => {
    if (!etapasParaExibir || etapasParaExibir.length === 0) return '0%';

    // Separar etapas normais das especiais
    const etapasNormais = etapasParaExibir.filter(etapa => {
      const tipoEtapa = getTipoEtapa(etapa);
      const isRecursoExpirado = isRecursoExpiradoSemApresentacao(etapa, etapasLimpas);
      return !(tipoEtapa === 'arquivamento' || isRecursoExpirado);
    });

    const etapasEspeciais = etapasParaExibir.filter(etapa => {
      const tipoEtapa = getTipoEtapa(etapa);
      const isRecursoExpirado = isRecursoExpiradoSemApresentacao(etapa, etapasLimpas);
      return (tipoEtapa === 'arquivamento' || isRecursoExpirado);
    });

    // Verificar se há etapas especiais alcançadas
    const temEspecialAlcancada = etapasEspeciais.some(etapa => 
      calcularStatusMarcador(etapa, etapasLimpas).isAlcancada
    );

    // Se há etapas especiais alcançadas, a barra vai até 100% (igual à versão desktop)
    if (temEspecialAlcancada) {
      return '100%';
    }

    // Se só há etapas especiais, usar lógica simples
    if (etapasNormais.length === 0) {
      return '0%';
    }

    // Verificar parada no sobrestamento (apenas para etapas normais)
    const marcadoresSobrestamento = etapasNormais.map((etapa, index) => ({
      ...etapa,
      posicao: (index / (etapasNormais.length - 1 || 1)) * 100,
      posicaoPixels: 0,
      isAlcancada: calcularStatusMarcador(etapa, etapasLimpas).isAlcancada,
      isPrazoPendente: calcularStatusMarcador(etapa, etapasLimpas).isPrazoPendente,
      isSobrestamento: etapa.idOriginal === 'SOBRESTAMENTO_01' || etapa.etapaDefinicao.nomeOriginal.toLowerCase().includes('sobrestamento'),
      isUltimaAnalise: false, 
      isProtocolo: getTipoEtapa(etapa) === 'protocolo',
      isArquivamento: false,
      isIndeferimento: getTipoEtapa(etapa) === 'indeferimento',
      isDeferimento: getTipoEtapa(etapa) === 'deferimento',
      isConcessao: getTipoEtapa(etapa) === 'concessao',
      isRecursoExpirado: false
    }));

    const { deveParar, progressWidth: progressSobrestamentoPos } = verificarParadaSobrestamento(marcadoresSobrestamento);

    if (deveParar) {
      const indiceSobrestamento = etapasNormais.findIndex(e => 
        e.idOriginal === 'SOBRESTAMENTO_01' || 
        e.etapaDefinicao.nomeOriginal.toLowerCase().includes('sobrestamento')
      );
      
      if (indiceSobrestamento !== -1) {
        const alturaPercentual = (indiceSobrestamento / (etapasNormais.length - 1)) * 100;
        return `${Math.max(0, Math.min(100, alturaPercentual))}%`;
      }
    }

    // Encontrar a última etapa normal alcançada
    let ultimaEtapaNormalAlcancadaIndex = -1;
    for (let i = etapasNormais.length - 1; i >= 0; i--) {
      if (calcularStatusMarcador(etapasNormais[i], etapasLimpas).isAlcancada) {
        ultimaEtapaNormalAlcancadaIndex = i;
        break;
      }
    }

    if (ultimaEtapaNormalAlcancadaIndex === -1) {
      return '0%';
    }

    if (etapasNormais.length === 1) {
      const etapaAlcancada = calcularStatusMarcador(etapasNormais[0], etapasLimpas).isAlcancada;
      return etapaAlcancada ? '100%' : '0%';
    }

    // Calcular a altura baseada nas etapas normais
    const alturaPercentual = (ultimaEtapaNormalAlcancadaIndex / (etapasNormais.length - 1)) * 100;
    return `${Math.max(0, Math.min(100, alturaPercentual))}%`;
  }, [etapasParaExibir, etapasLimpas]);

  // Reordenar etapas para exibição: marcadores especiais vão para o final
  const etapasOrdenadas = useMemo(() => {
    // Na versão mobile, vamos manter a mesma lógica da desktop:
    // Os marcadores especiais ficam no final da timeline, mas integrados (não separados)
    
    const etapasNormais: EtapaTimeline[] = [];
    const etapasEspeciais: EtapaTimeline[] = [];

    etapasParaExibir.forEach(etapa => {
      const tipoEtapa = getTipoEtapa(etapa);
      const isRecursoExpirado = isRecursoExpiradoSemApresentacao(etapa, etapasLimpas);
      
      // Arquivamento e recurso expirado vão para o final
      if (tipoEtapa === 'arquivamento' || isRecursoExpirado) {
        etapasEspeciais.push(etapa);
      } else {
        etapasNormais.push(etapa);
      }
    });

    // Ordenar etapas especiais: recurso expirado primeiro, depois arquivamento
    etapasEspeciais.sort((a, b) => {
      const aIsRecurso = isRecursoExpiradoSemApresentacao(a, etapasLimpas);
      const bIsRecurso = isRecursoExpiradoSemApresentacao(b, etapasLimpas);
      
      if (aIsRecurso && !bIsRecurso) return -1; // Recurso antes do arquivamento
      if (!aIsRecurso && bIsRecurso) return 1;  // Arquivamento após recurso
      return 0;
    });

    // Retornar todas as etapas em sequência (sem separação visual)
    return [...etapasNormais, ...etapasEspeciais];
  }, [etapasParaExibir, etapasLimpas]);

  return (
    <div className={styles.timelineContainer}>
      <div className={styles.linhaBaseMobile} />
      <div 
        className={styles.linhaProgressoMobile} 
        style={{ height: `calc(${progressHeight} - 20px)` }}
      />

      {/* Botão de Debug - removido */}
      {/* {process.env.NODE_ENV === 'development' && (
        <button
          onClick={(e) => {
            e.stopPropagation();
            setShowDebugModal(true);
          }}
          className={styles.debugButton}
          title="Ver etapas da timeline (Debug)"
        >
          🔍
        </button>
      )} */}

      {/* Modal de Debug - removido */}
      {/* {showDebugModal && createPortal(
        <div className={styles.debugModal} onClick={() => setShowDebugModal(false)}>
          <div className={styles.debugModalContent} onClick={(e) => e.stopPropagation()}>
            <div className={styles.debugModalHeader}>
              <h3>Etapas da Timeline (Debug)</h3>
              <div className={styles.debugModalActions}>
                <button 
                  onClick={() => copiarJSON(etapasLimpas)}
                  className={styles.debugModalCopy}
                  title="Copiar JSON"
                >
                  📋
                </button>
                <button 
                  onClick={() => setShowDebugModal(false)}
                  className={styles.debugModalClose}
                >
                  ✕
                </button>
              </div>
            </div>
            <div className={styles.debugModalBody}>
              <pre className={styles.debugJson}>
                {JSON.stringify(etapasLimpas, (key, value) => {
                  // Formatar datas para melhor legibilidade
                  if (value instanceof Date) {
                    return value.toISOString();
                  }
                  return value;
                }, 2)}
              </pre>
            </div>
          </div>
        </div>,
        document.body
      )} */}

      {etapasOrdenadas.map((etapa, index) => {
        const dataFormatada = formatarDataExibicaoMobile(etapa, etapasLimpas);
        const { isAlcancada, isPrazoPendente } = calcularStatusMarcador(etapa, etapasLimpas);
        const isSobrestamento = etapa.idOriginal === 'SOBRESTAMENTO_01' || etapa.etapaDefinicao.nomeOriginal.toLowerCase().includes('sobrestamento');
        const isAnaliseMetito = etapa.etapaDefinicao.nomeOriginal.toLowerCase().includes('análise de mérito');
        const isRecursoExpirado = isRecursoExpiradoSemApresentacao(etapa, etapasLimpas);
        const temSobrestamentoNaTimelineLocal = temSobrestamento(etapasLimpas);
        const { isTaxaConcessao: isEtapaTaxaConcessao, textoFormatado: textoTaxaFormatado } = formatarEtapaTaxaConcessao(etapa);
        
        // Identificar tipos de marcadores para classes CSS especiais
        const tipoEtapa = getTipoEtapa(etapa);
        const isProtocolo = tipoEtapa === 'protocolo';
        const isArquivamento = tipoEtapa === 'arquivamento';
        const isIndeferimento = tipoEtapa === 'indeferimento';
        const isDeferimento = tipoEtapa === 'deferimento';
        const isConcessao = tipoEtapa === 'concessao';
        
        // Identificar marcadores estimados de processo não publicado
        const isPublicacaoEstimada = etapa.idOriginal === 'PUBLICACAO_ESTIMADA_01';
        const isFimOposicaoEstimado = etapa.idOriginal === 'FIM_OPOSICAO_ESTIMADO_01';
        const isAnaliseEstimada = etapa.idOriginal === 'ANALISE_MERITO_ESTIMADA_01';
        
        // Identificar análise da nulidade
        const isAnaliseNulidade = etapa.idOriginal === 'ANALISE_NULIDADE_ESTIMADA_01';
        
        return (
          <div 
            key={`${etapa.idOriginal}-${index}`} 
            className={`
              ${styles.etapaContainer}
              ${isProtocolo ? styles.etapaProtocolo : ''}
              ${isArquivamento ? styles.etapaArquivamento : ''}
              ${isIndeferimento ? styles.etapaIndeferimento : ''}
              ${isDeferimento ? styles.etapaDeferimento : ''}
              ${isConcessao ? styles.etapaConcessao : ''}
              ${isRecursoExpirado ? styles.etapaRecursoExpirado : ''}
              ${isPublicacaoEstimada ? styles.etapaPublicacaoEstimada : ''}
              ${isFimOposicaoEstimado ? styles.etapaFimOposicaoEstimado : ''}
              ${isAnaliseEstimada ? styles.etapaAnaliseEstimada : ''}
              ${isAnaliseNulidade ? styles.etapaAnaliseNulidade : ''}
            `}
          >
            {/* Data à Esquerda - OCULTAR para análise da nulidade */} 
            {dataFormatada && !isAnaliseNulidade && (
              <div className={`
                ${styles.dataEsquerdaMobile}
                ${isArquivamento || isRecursoExpirado ? styles.dataEspecialFinal : ''}
                ${temSobrestamentoNaTimelineLocal && isAnaliseMetito ? styles.dataOpaca : ''} 
                ${dataFormatada.tipo === 'previsao' ? styles.dataPrevisaoMobile : ''}
              `}>
                {/* Ajuste para exibir linha1 e linha2 para o tipo 'estimativa' */}
                {dataFormatada.tipo === 'estimativa' && dataFormatada.linha1 && dataFormatada.linha2 ? (
                  <>
                    <div className={styles.dataEstimativaLinha1Mobile}>{dataFormatada.linha1}</div>
                    <div className={styles.dataEstimativaLinha2Mobile}>{dataFormatada.linha2}</div>
                  </>
                ) : (
                  dataFormatada.texto
                )} 
              </div>
            )}
            
            {/* Marcador (Central) */}
            <div
              className={`
                ${styles.marcador}
                ${isAlcancada ? styles.marcadorAlcancado : ''}
                ${isPrazoPendente ? styles.marcadorPrazoPendente : ''}
                ${isSobrestamento ? styles.marcadorSobrestamento : ''}
                ${temSobrestamentoNaTimelineLocal && isAnaliseMetito ? styles.marcadorOpaco : ''} 
                ${isRecursoExpirado ? styles.marcadorRecursoExpirado : ''}
                ${isPrazoManifestacao(etapa) ? styles.marcadorPrazoManifestacao : ''}
                ${isExigenciaAtiva(etapa) ? styles.marcadorExigenciaAtiva : ''}
                ${isTaxaConcessao(etapa) ? styles.marcadorTaxaConcessao : ''}
                ${isPublicacaoEstimada ? styles.marcadorPublicacaoEstimada : ''}
                ${isFimOposicaoEstimado ? styles.marcadorFimOposicaoEstimado : ''}
                ${isAnaliseEstimada ? styles.marcadorAnaliseEstimada : ''}
                ${isAnaliseNulidade ? styles.marcadorAnaliseNulidade : ''}
              `}
            >
              {isAlcancada && !isSobrestamento && !(isAnaliseMetito && temSobrestamentoNaTimelineLocal) && (
                <span className={styles.checkIcon}>✓</span>
              )}
            </div>
            
            {/* Conteúdo à Direita (Título e Descrição/statusSimples) */}
            <div className={`
              ${styles.conteudo} 
              ${temSobrestamentoNaTimelineLocal && isAnaliseMetito ? styles.conteudoOpaco : ''}
              ${isArquivamento || isRecursoExpirado ? styles.conteudoEspecialFinal : ''}
            `}>
              <div className={`${isEtapaTaxaConcessao ? styles.nomeEtapaTaxaConcessao : styles.nomeEtapa} ${temSobrestamentoNaTimelineLocal && isAnaliseMetito ? styles.nomeEtapaOpaco : ''}`}>
                {isEtapaTaxaConcessao && textoTaxaFormatado ? (
                  <>
                    <div>{textoTaxaFormatado.principal}</div>
                    <div className={styles.subtextoTaxaConcessao}>{textoTaxaFormatado.subtexto}</div>
                  </>
                ) : etapa.etapaDefinicao.nomeOriginal === "Instauração de Nulidade" ? (
                  <>
                    <div>Instauração</div>
                    <div>de Nulidade</div>
                  </>
                ) : (
                  calcularNumeroRenovacao(etapasLimpas, etapa)
                )}
              </div>
              {/* Descrição / statusSimples */} 
              {etapa.etapaDefinicao.statusSimples && !isEtapaTaxaConcessao && (
                 <div className={`${styles.statusSimplesMobile} ${temSobrestamentoNaTimelineLocal && isAnaliseMetito ? styles.statusSimplesOpaco : ''}`}>{etapa.etapaDefinicao.statusSimples}</div>
              )}

            </div>
          </div>
        );
      })}
    </div>
  );
};

export default TimelineUnificadaMobile; 