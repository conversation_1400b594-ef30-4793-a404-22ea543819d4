import { format, addDays } from 'date-fns';
import { ptBR } from 'date-fns/locale';

interface DetalhesDespacho {
  nome: string | null;
}

interface RPI {
  dataPublicacao: string;
  numero: string;
}

interface Despacho {
  codigo: string;
  nome: string | null;
  DetalhesDespacho: DetalhesDespacho | null;
  RPI: RPI | null;
}

interface IndeferimentoTimelineProps {
  dataIndeferimento: Date;
  despachos: Despacho[];
}

interface ProgressoInfo {
  etapaAtual: number; // 0: Ind->Prazo, 1: Prazo->Análise
  progressoNaEtapaAtual: number; // 0 a 1
}

function calcularSeMarcadorCompleto(index: number, progInfo: ProgressoInfo): boolean {
  return index < progInfo.etapaAtual || 
         (index === progInfo.etapaAtual && progInfo.progressoNaEtapaAtual === 1);
}

function calcularProgressoEntreDatas(hojeMs: number, inicioMs: number | null, fimMs: number | null): number {
  if (inicioMs === null || fimMs === null || fimMs <= inicioMs) {
    return 0; 
  }
  const duracaoEtapa = fimMs - inicioMs;
  const tempoDecorrido = hojeMs - inicioMs;
  return Math.max(0, Math.min(1, tempoDecorrido / duracaoEtapa));
}

export default function IndeferimentoTimeline({
  dataIndeferimento,
  despachos,
}: IndeferimentoTimelineProps) {
  const hoje = new Date();
  const hojeMs = hoje.getTime();

  const dataIndeferimentoMs = dataIndeferimento.getTime();
  const dataPrazoRecurso = addDays(dataIndeferimento, 60);
  const dataPrazoRecursoMs = dataPrazoRecurso.getTime();
  
  const despachoNotificacaoRecurso = despachos.find(despacho => 
    despacho.nome?.toLowerCase().includes("notificação de recurso") &&
    despacho.DetalhesDespacho?.nome?.toLowerCase().includes("recurso contra indeferimento") &&
    despacho.RPI?.dataPublicacao &&
    new Date(despacho.RPI.dataPublicacao).getTime() >= dataIndeferimentoMs &&
    new Date(despacho.RPI.dataPublicacao).getTime() <= dataPrazoRecursoMs
  );

  const prazoPerdido = hojeMs > dataPrazoRecursoMs && !despachoNotificacaoRecurso;

  const despachoDecisaoRecurso = despachos.find(despacho => 
    (despacho.nome?.toLowerCase().includes("recurso provido") ||
     despacho.nome?.toLowerCase().includes("recurso não provido") ||
     despacho.nome?.toLowerCase().includes("recurso prejudicado")) &&
    despacho.RPI?.dataPublicacao &&
    new Date(despacho.RPI.dataPublicacao).getTime() >= dataPrazoRecursoMs
  );
  
  const dataDecisaoRecurso = despachoDecisaoRecurso?.RPI?.dataPublicacao
    ? new Date(despachoDecisaoRecurso.RPI.dataPublicacao)
    : null;
  const dataDecisaoRecursoMs = dataDecisaoRecurso?.getTime() ?? null;
  
  const dataEstimadaDecisao = !dataDecisaoRecurso && !prazoPerdido 
    ? addDays(dataPrazoRecurso, 530)
    : null;
  const dataEstimadaDecisaoMs = dataEstimadaDecisao?.getTime() ?? null;

  const calcularProgresso = (): ProgressoInfo => {
    if (hojeMs < dataPrazoRecursoMs) {
      return { 
        etapaAtual: 0, 
        progressoNaEtapaAtual: calcularProgressoEntreDatas(hojeMs, dataIndeferimentoMs, dataPrazoRecursoMs)
      };
    }
    
    if (prazoPerdido) {
      return { etapaAtual: 1, progressoNaEtapaAtual: 1 };
    }
    
    const dataFimEtapaAnalise = dataDecisaoRecursoMs ?? dataEstimadaDecisaoMs;
    if (dataFimEtapaAnalise && hojeMs < dataFimEtapaAnalise) {
      return { 
        etapaAtual: 1, 
        progressoNaEtapaAtual: calcularProgressoEntreDatas(hojeMs, dataPrazoRecursoMs, dataFimEtapaAnalise)
      };
    }
    
    return { etapaAtual: 2, progressoNaEtapaAtual: 1 };
  };
  
  const progressoInfo = calcularProgresso();

  const TimelineMarker = ({ 
    data, 
    titulo, 
    descricao, 
    isAlert = false,
    isEstimated = false,
    subtext = null,
    index, 
    isUltimoMarcador
  }: { 
    data: Date | null, 
    titulo: string, 
    descricao: string,
    isAlert?: boolean,
    isEstimated?: boolean,
    subtext?: string | null,
    index: number,
    isUltimoMarcador: boolean
  }) => {
    const isMarkerCompleted = calcularSeMarcadorCompleto(index, progressoInfo);

    const showBlueLine = index < progressoInfo.etapaAtual || (index === progressoInfo.etapaAtual && progressoInfo.progressoNaEtapaAtual > 0);
    const blueLineHeightPercent = index < progressoInfo.etapaAtual ? 100 : progressoInfo.progressoNaEtapaAtual * 100;

    return (
      <div className="flex relative">
        <div className="w-24 flex-shrink-0 mr-4 text-right">
          {data && (
            <span className={`text-sm font-medium ${isAlert ? 'text-red-600' : 'text-[#A3A3A3]'}`}>
              {format(data, "dd/MM/yyyy")}
              {isEstimated && <em className="text-[10px] not-italic"> (est.)</em>}
            </span>
          )}
        </div>

        <div className="flex flex-col items-center w-5 relative">
          <div className={`w-5 h-5 rounded-full shadow-md flex items-center justify-center ${ 
            isMarkerCompleted 
              ? isAlert
                ? "bg-red-600 border-2 border-red-600"
                : "bg-[#4597B5] border-2 border-[#4597B5]" 
              : isAlert
                ? "border-2 border-red-500 bg-white" 
                : "border-2 border-[#C3C3C3] bg-white"
            } relative z-10`}>
            {isMarkerCompleted && !isAlert && (
              <svg className="w-3.5 h-3.5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M5 13l4 4L19 7" />
              </svg>
            )}
             {isMarkerCompleted && isAlert && (
               <svg className="w-3.5 h-3.5 text-white" fill="none" stroke="currentColor" strokeWidth={3} viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
                </svg>
            )}
          </div>

          {!isUltimoMarcador && (
            <div className="absolute top-5 left-1/2 transform -translate-x-1/2 w-1 h-full"> 
              <div className="absolute top-0 left-0 w-full h-full bg-[#C3C3C3]"></div>
              {showBlueLine && (
                <div
                  className={`absolute top-0 left-0 w-full ${isAlert && index === progressoInfo.etapaAtual ? 'bg-red-600' : 'bg-[#4597B5]'} transition-height duration-300 ease-in-out`}
                  style={{ height: `${blueLineHeightPercent}%` }}
                ></div>
              )}
            </div>
          )}
        </div>

        <div className="ml-4 flex-grow pb-6">
          <h3 className={`text-base font-semibold leading-tight ${isAlert ? "text-red-600" : "text-black"}`}>
            {titulo}
          </h3>
          <p className="text-sm text-gray-500 mt-0.5">{descricao}</p>
          {subtext && (
            <span className="text-xs text-gray-500 italic mt-0.5 block">{subtext}</span>
          )}
        </div>
      </div>
    );
  };
  
  const ultimoIndiceVisivel = prazoPerdido ? 1 : 2;

  return (
    <div className="pt-4 pb-2 px-2">
        <TimelineMarker
          index={0}
          data={dataIndeferimento}
          titulo="Indeferimento"
          descricao="Data em que o INPI indeferiu o pedido de registro."
          isUltimoMarcador={false}
        />
        
        <TimelineMarker
          index={1}
          data={dataPrazoRecurso}
          titulo={prazoPerdido ? "Prazo expirado" : "Prazo para recurso"}
          descricao={prazoPerdido 
            ? "O prazo para recurso expirou sem notificação." 
            : "Prazo para apresentar recurso contra o indeferimento."} 
          isAlert={prazoPerdido} 
          subtext={!prazoPerdido && despachoNotificacaoRecurso ? "(Recurso protocolado)" : null}
          isUltimoMarcador={ultimoIndiceVisivel === 1}
        />
        
        {!prazoPerdido && (
          <TimelineMarker
            index={2}
            data={dataDecisaoRecurso ?? dataEstimadaDecisao}
            titulo="Análise do recurso"
            descricao={dataDecisaoRecurso 
              ? `Recurso ${despachoDecisaoRecurso?.nome?.toLowerCase().includes("provido") ? "Provido" : "Não Provido/Prejudicado"}` 
              : "Previsão para análise do recurso pelo INPI."}
            isEstimated={!dataDecisaoRecurso}
            isUltimoMarcador={ultimoIndiceVisivel === 2}
          />
        )}
    </div>
  );
} 