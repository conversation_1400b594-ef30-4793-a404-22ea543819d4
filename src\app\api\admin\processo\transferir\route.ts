import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getAdminFromRequest, requireAdminRole } from '@/lib/adminAuth';

export async function POST(request: NextRequest) {
  try {
    // Verificar autenticação e role MASTER
    const user = await getAdminFromRequest(request);
    if (!user || !requireAdminRole(['MASTER'])(user)) {
      return NextResponse.json(
        { error: 'Acesso negado. Apenas usuários MASTER podem acessar esta funcionalidade.' },
        { status: 403 }
      );
    }
    const body = await request.json();
    const { processoId, clienteOrigemId, clienteDestinoId } = body;

    // Validar entrada
    if (!processoId || !clienteOrigemId || !clienteDestinoId) {
      return NextResponse.json(
        { error: 'ProcessoId, clienteOrigemId e clienteDestinoId são obrigatórios' },
        { status: 400 }
      );
    }

    if (clienteOrigemId === clienteDestinoId) {
      return NextResponse.json(
        { error: 'Cliente de origem e destino devem ser diferentes' },
        { status: 400 }
      );
    }

    // Buscar o processo atual
    const processo = await prisma.processo.findUnique({
      where: { id: processoId },
      include: {
        Cliente: {
          select: { id: true, nome: true, identificador: true }
        },
        Marca: {
          select: { nome: true }
        }
      }
    });

    if (!processo) {
      return NextResponse.json(
        { error: 'Processo não encontrado' },
        { status: 404 }
      );
    }

    // Verificar se o processo pertence ao cliente de origem
    if (processo.clienteId !== clienteOrigemId) {
      return NextResponse.json(
        { error: 'Processo não pertence ao cliente de origem especificado' },
        { status: 400 }
      );
    }

    // Buscar cliente de destino
    const clienteDestino = await prisma.cliente.findUnique({
      where: { id: clienteDestinoId },
      select: { id: true, nome: true, identificador: true }
    });

    if (!clienteDestino) {
      return NextResponse.json(
        { error: 'Cliente de destino não encontrado' },
        { status: 404 }
      );
    }

    // Realizar a transferência
    const processoAtualizado = await prisma.processo.update({
      where: { id: processoId },
      data: { clienteId: clienteDestinoId },
      include: {
        Cliente: {
          select: { id: true, nome: true, identificador: true }
        },
        Marca: {
          select: { nome: true }
        }
      }
    });

    console.log('🔄 Processo transferido:', {
      processoNumero: processo.numero,
      clienteOrigem: processo.Cliente?.nome,
      clienteDestino: clienteDestino.nome
    });

    return NextResponse.json({
      message: 'Processo transferido com sucesso',
      processo: {
        id: processoAtualizado.id,
        numero: processoAtualizado.numero,
        marca: processoAtualizado.Marca?.nome
      },
      transferencia: {
        clienteOrigem: {
          id: processo.Cliente?.id,
          nome: processo.Cliente?.nome,
          identificador: processo.Cliente?.identificador
        },
        clienteDestino: {
          id: clienteDestino.id,
          nome: clienteDestino.nome,
          identificador: clienteDestino.identificador
        }
      }
    });

  } catch (error) {
    console.error('Erro ao transferir processo:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
} 