import { format, addYears, addDays } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import React, { useMemo } from 'react';
import StatusVigorIcon from '@/components/icons/StatusVigorIcon';
import StatusIndeferidoIcon from '@/components/icons/StatusIndeferidoIcon';
import StatusArquivadoIcon from '@/components/icons/StatusArquivadoIcon';
import StatusDeferidoIcon from '@/components/icons/StatusDeferidoIcon';
import StatusPublicacaoIcon from '@/components/icons/StatusPublicacaoIcon';
import { gerarTimelineSimplificada } from '@/lib/timelineLogica';

// --- Interfaces duplicadas de ProcessoCard.tsx ---
// Idealmente, estas interfaces deveriam vir de um arquivo de tipos compartilhado
interface NCL {
  codigo: string | null;
  especificacao: string | null;
}

interface Marca {
  nome: string | null;
  NCL: NCL[];
  apresentacao: string | null;
  natureza: string | null;
}

interface DetalhesDespacho {
  nome: string | null;
}

interface RPI {
  dataPublicacao: string;
  numero: string;
}

interface Despacho {
  codigo: string;
  nome: string | null;
  DetalhesDespacho: DetalhesDespacho | null;
  RPI: RPI | null;
  ProtocoloDespacho?: {
    codigoServico?: string;
  }[];
}

interface Processo {
  id: string;
  numero: string;
  dataDeposito: string | null;
  dataPublicacaoRPI: string | null;
  dataConcessao?: string | null;
  dataVigencia?: string | null;
  dataMeritoEstimada: string | null;
  dataOposicao: string | null;
  Marca: Marca | null;
  Despacho: Despacho[];
  oposicao: boolean;
  Cliente?: { crmStageId?: number };
  taxaConcessaoPaga?: boolean;
  logoUrl: string | null;
}
// --- Fim das interfaces duplicadas ---

// Interface para o valor de retorno do hook
interface UseProcessoStatusReturn {
    statusText: string;
    cardBgColorClass: string;
    sidebarBgColorClass: string;
    sidebarIconComponent: React.FC | null;
    showSidebar: boolean;
    logoImage: string;
    nomeMarca: string;
    linkInpi: string;
    codigoNCL: string | null;
    showOposicaoBadge: boolean;
    etapasTimeline: any[]; // Array de etapas da timeline unificada
    dataMeritoEstimadaFormatada: string | null;
    useSpacingForTimeline: boolean;
    rawDespachos: Despacho[]; // Passando despachos brutos para timelines que precisam
}

// Interface para as estimativas de mérito
interface EstimativasMerito {
  mediaSemIntervencoes?: number | null;
  mediaComOposicao?: number | null;
  mediaComSobrestamento?: number | null;
  mediaComExigencia?: number | null;
}

export const useProcessoStatus = (
  processo: Processo | null | undefined, 
  estimativasMerito?: EstimativasMerito | null
): UseProcessoStatusReturn => {
  return useMemo(() => {
    // Verificação de segurança - se processo for null/undefined, retorna valores padrão
    if (!processo) {
      return {
        statusText: "Processo não encontrado",
        cardBgColorClass: "bg-gray-100",
        sidebarBgColorClass: "bg-gray-400",
        sidebarIconComponent: null,
        showSidebar: false,
        logoImage: "IMAGEM_NAO_IDENTIFICADA",
        nomeMarca: "Processo não encontrado",
        linkInpi: "#",
        codigoNCL: null,
        showOposicaoBadge: false,
        etapasTimeline: [],
        dataMeritoEstimadaFormatada: null,
        useSpacingForTimeline: false,
        rawDespachos: []
      };
    }

    // --- Lógica movida de ProcessoCard ---
    const hoje = new Date();
    const dataDeposito = processo.dataDeposito ? new Date(processo.dataDeposito) : null;
    const dataPublicacao = processo.dataPublicacaoRPI ? new Date(processo.dataPublicacaoRPI) : null;
    const dataFimOposicao = processo.dataOposicao ? new Date(processo.dataOposicao) : null;
    const dataMeritoEstimada = processo.dataMeritoEstimada ? new Date(processo.dataMeritoEstimada) : null;
    const despachoConcessao = processo.Despacho.find(despacho => despacho.nome?.toLowerCase().includes("concessão de registro"));
    const dataConcessao = processo.dataConcessao ? new Date(processo.dataConcessao) : (despachoConcessao?.RPI?.dataPublicacao ? new Date(despachoConcessao.RPI.dataPublicacao) : null);
    const temConcessaoRegistro = processo.Despacho.some((despacho) => despacho.nome?.toLowerCase().includes("concessão de registro"));
    const temNulidadeAposConcessao = dataConcessao ? processo.Despacho.some(despacho => { if (despacho.nome?.toLowerCase().includes("requerimento provido (nulo o registro)") && despacho.RPI?.dataPublicacao) { const dataDespacho = new Date(despacho.RPI.dataPublicacao); return dataDespacho > dataConcessao; } return false; }) : false;
    const registroEmVigorLegado = temConcessaoRegistro && dataConcessao && !temNulidadeAposConcessao;
    const primeiraRenovacaoConfirmada = registroEmVigorLegado ? processo.Despacho.some(despacho => { const nomeDespacho = despacho.nome?.toLowerCase() || ''; const dataPubDespacho = despacho.RPI?.dataPublicacao ? new Date(despacho.RPI.dataPublicacao) : null; if (nomeDespacho.includes("deferimento da petição") && dataPubDespacho && dataPubDespacho > dataConcessao!) { const protocolos = despacho.ProtocoloDespacho as any[]; if (protocolos && protocolos.length > 0) { return protocolos.some(protocolo => protocolo?.codigoServico === "3745"); } } return false; }) : false;
    const dataPrimeiraRenovacao = dataConcessao ? addYears(dataConcessao, 10) : null;
    const dataSegundaRenovacao = dataConcessao ? addYears(dataConcessao, 20) : null;
    const dataFimVigenciaConcessao = primeiraRenovacaoConfirmada ? dataSegundaRenovacao : dataPrimeiraRenovacao;
    const dataVigencia = processo.dataVigencia ? new Date(processo.dataVigencia) : null;
    const registroEmVigorGenerico = !registroEmVigorLegado && dataDeposito && dataConcessao && dataVigencia && dataVigencia > hoje;
    const dataFimStatus = registroEmVigorLegado ? dataFimVigenciaConcessao : (registroEmVigorGenerico ? dataVigencia : null);
    const taxaConcessaoPaga = processo.Cliente?.crmStageId === 280246 || processo.taxaConcessaoPaga === true;
    
    const textoArquivamento = "arquivamento definitivo de pedido de registro";
    const despachoArquivamento = processo.Despacho.find(despacho => 
      despacho.nome?.toLowerCase().includes(textoArquivamento)
    );
    const temArquivamentoDefinitivo = !!despachoArquivamento;
    const dataArquivamento = despachoArquivamento?.RPI?.dataPublicacao 
      ? new Date(despachoArquivamento.RPI.dataPublicacao)
      : null;
    
    const textosDeferimento = ["deferimento do pedido", "registrado"];
    const despachoDeferimentoBase = processo.Despacho.find(d => textosDeferimento.some(t => d.nome?.toLowerCase().includes(t)));
    const dataDeferimentoBase = despachoDeferimentoBase?.RPI?.dataPublicacao ? new Date(despachoDeferimentoBase.RPI.dataPublicacao) : null;
    
    const despachoRecursoProvidoDef = processo.Despacho.find(d => d.nome?.toLowerCase().includes("recurso provido (decisão reformada para: deferimento)"));
    const dataRecursoProvidoDef = despachoRecursoProvidoDef?.RPI?.dataPublicacao ? new Date(despachoRecursoProvidoDef.RPI.dataPublicacao) : null;
    const temRecursoProvidoParaDeferimento = !!despachoRecursoProvidoDef;
  
    const temDeferimento = !!despachoDeferimentoBase || temRecursoProvidoParaDeferimento;
    const dataDeferimento = dataRecursoProvidoDef ?? dataDeferimentoBase;
    
    const temConcessaoOuArquivamento = processo.Despacho.some((despacho) => despacho.nome?.toLowerCase().includes("concessão") || despacho.nome?.toLowerCase().includes(textoArquivamento));
    const temDeferimentoSemConcessao = !temArquivamentoDefinitivo && temDeferimento && !temConcessaoOuArquivamento;
    
    const textosIndeferimento = ["indeferimento do pedido"];
    const despachoIndeferimentoBase = processo.Despacho.find(d => 
      textosIndeferimento.some(t => d.nome?.toLowerCase().includes(t)) || 
      (d.codigo === "IPAS024" && d.nome?.toLowerCase().includes("indeferimento"))
    );
    const temIndeferimentoBase = !!despachoIndeferimentoBase;
    
    const temIndeferimento = !temArquivamentoDefinitivo && temIndeferimentoBase && !temRecursoProvidoParaDeferimento;
    const dataIndeferimento = despachoIndeferimentoBase?.RPI?.dataPublicacao ? new Date(despachoIndeferimentoBase.RPI.dataPublicacao) : null;
    const temNotificacaoRecursoPosIndeferimento = dataIndeferimento ? processo.Despacho.some(despacho => { if (despacho.nome?.toLowerCase().includes("notificação de recurso") && despacho.RPI?.dataPublicacao) { const dataRecurso = new Date(despacho.RPI.dataPublicacao); return dataRecurso > dataIndeferimento; } return false; }) : false;
    
    const textoDespachoPublicacao = "publicação de pedido de registro para oposição";
    const temDespachoPublicacao = processo.Despacho.some(despacho => despacho.nome?.toLowerCase().includes(textoDespachoPublicacao));
    const temDespachoMerito = processo.Despacho.some(despacho => { const nomeDespacho = despacho.nome?.toLowerCase() || ''; const ehDeferimento = textosDeferimento.some(texto => nomeDespacho.includes(texto)); const ehIndeferimento = textosIndeferimento.some(texto => nomeDespacho.includes(texto)); return ehDeferimento || ehIndeferimento; });
    const estaEmFasePublicacao = !temArquivamentoDefinitivo && !temIndeferimento && !temDeferimentoSemConcessao && temDespachoPublicacao && !temDespachoMerito; 
    const temNotificacaoOposicao = processo.Despacho.some(despacho => despacho.nome === "Notificação de oposição");
    
    const dataPrazoOrdinario = dataDeferimento ? addDays(dataDeferimento, 60) : null;
    const dataPrazoExtraordinario = dataPrazoOrdinario ? addDays(dataPrazoOrdinario, 30) : null;
    
    const taxaNaoPagaAposPrazo = 
      temDeferimentoSemConcessao && 
      dataPrazoExtraordinario && 
      hoje >= dataPrazoExtraordinario && 
      !taxaConcessaoPaga;
    
    // --- Funções auxiliares movidas ---
    const getLogoImage = (): string => {
      if (processo.Marca?.apresentacao?.toLowerCase() === 'nominativa') {
        return "/api/private-assets/processos/nominativa";
      } else if (processo.numero) {
        // Remove caracteres não numéricos do número do processo
        const numeroLimpo = processo.numero.replace(/\D/g, "");
        return `https://api-v3-rgsys.registrese.app.br/api/protocolo/logo/${numeroLimpo}`;
      } else {
        return "IMAGEM_NAO_IDENTIFICADA";
      }
    };

    const getStatusText = (): string => {
        // NOVA LÓGICA: Priorizar última etapa CONCLUÍDA ao invés da atual
        
        // VERIFICAÇÃO ESPECIAL: Recurso expirado sem apresentação
        const etapaPrazoRecurso = etapasTimeline.find(etapa => 
          etapa.etapaDefinicao.nomeOriginal.toLowerCase().includes('recurso') &&
          etapa.status === 'ATUAL'
        );
        
        if (etapaPrazoRecurso) {
          // Verificar se o prazo expirou (tem dataFim e é anterior a hoje)
          const prazoExpirou = etapaPrazoRecurso.dataFim && new Date(etapaPrazoRecurso.dataFim) < hoje;
          
          // Verificar se não há etapa de recurso apresentado nas etapas concluídas
          const temRecursoApresentado = etapasTimeline.some(etapa => 
            etapa.idOriginal === 'RECURSO_INDEFERIMENTO_APRESENTADO_01' && 
            etapa.status === 'CONCLUIDA'
          );
          
          if (prazoExpirou && !temRecursoApresentado) {
            return "Pedido indeferido, sem apresentação de recurso.";
          }
        }
        
        // Hierarquia de prioridade para etapas concluídas (maior número = maior prioridade)
        const prioridadeEtapas: { [key: string]: number } = {
          // Etapas finais/decisivas (alta prioridade)
          'CONCESSAO_REGISTRO_01': 100,
          'ARQUIVAMENTO_01': 95,
          'DESISTENCIA_HOMOLOGADA_01': 95,
          'SOBRESTAMENTO_01': 95,
          'INDEFERIMENTO_PEDIDO_01': 90,
          'ARQUIVAMENTO_FALTA_PAGAMENTO_TAXA_01': 90,
          'ARQUIVAMENTO_POS_RECURSO_NEGADO_01': 90,
          'ARQUIVAMENTO_RECURSO_NAO_APRESENTADO_01': 90,
          'REGISTRO_ANULADO_01': 85,
          'RECURSO_NEGADO_01': 85,
          
          // Etapas de decisão/deferimento (média-alta prioridade)
          'DEFERIMENTO_PEDIDO_01': 80,
          'DEFERIMENTO_POS_RECURSO_01': 80,
          'RECURSO_PROVIDO_TRANSICAO_DEFERIMENTO_01': 75,
          'TAXA_CONCESSAO_PAGA_01': 70,
          
          // Etapas de processo (média prioridade)
          'RECURSO_INDEFERIMENTO_APRESENTADO_01': 60,
          'MANIFESTACAO_OPOSICAO_APRESENTADA_01': 55,
          'MANIFESTACAO_NULIDADE_APRESENTADA_01': 55,
          
          // Etapas intermediárias (baixa prioridade)
          'PUBLICACAO_01': 30,
          'PUBLICACAO_COM_OPOSICAO_01': 30,
          'PRAZO_OPOSICAO_SEM_OPOSICAO_01': 20,
          'PRAZO_OPOSICAO_COM_OPOSICAO_01': 20,
          'PROTOCOLO_01': 10,
          'PRAZO_RECURSO_INDEFERIMENTO_01': 60,
          'ANALISE_RECURSO_INDEFERIMENTO_01': 60,
          'ANALISE_RECURSO_01': 60,
          'ANALISE_DO_RECURSO_01': 60,
          'PRAZO_PAGAMENTO_TAXA_CONCESSAO_ORDINARIO_01': 60
        };
        
        // Primeiro, tentar pegar a última etapa concluída com hierarquia de prioridade
        const etapasConcluidas = etapasTimeline.filter(etapa => etapa.status === 'CONCLUIDA');
        
        const ultimaEtapaConcluida = etapasConcluidas.length > 0 ? 
          etapasConcluidas
            .sort((a, b) => {
              // Primeiro critério: prioridade da etapa
              const prioridadeA = prioridadeEtapas[a.idOriginal] || 0;
              const prioridadeB = prioridadeEtapas[b.idOriginal] || 0;
              
              if (prioridadeA !== prioridadeB) {
                return prioridadeB - prioridadeA; // Maior prioridade primeiro
              }
              
              // Segundo critério: data mais recente (dentro da mesma prioridade)
              const dataA = a.dataFim || a.dataInicio || new Date(0);
              const dataB = b.dataFim || b.dataInicio || new Date(0);
              return dataB.getTime() - dataA.getTime();
            })[0] : null;
        
        // Se não há etapa concluída, usar a etapa atual como fallback
        const etapaAtual = etapasTimeline.find(etapa => etapa.status === 'ATUAL');
        
        // Etapas ATUAIS que representam melhor o momento do processo
        const etapasAtuaisRelevantes = [
          'ANALISE_MERITO_SEM_OPOSICAO_01',
          'ANALISE_MERITO_COM_OPOSICAO_SEM_MANIFESTACAO_01', 
          'ANALISE_MERITO_COM_OPOSICAO_COM_MANIFESTACAO_01',
          'ANALISE_MERITO_COM_EXIGENCIA_01',
          'INDICADOR_EXIGENCIA_ATIVA_01',
          'PRAZO_RECURSO_INDEFERIMENTO_01',
          'ANALISE_RECURSO_INDEFERIMENTO_01',
          'ANALISE_RECURSO_01',
          'ANALISE_DO_RECURSO_01',
          'PRAZO_PAGAMENTO_TAXA_CONCESSAO_ORDINARIO_01',
          'PRAZO_PAGAMENTO_TAXA_CONCESSAO_EXTRAORDINARIO_01',
          'PRAZO_MANIFESTACAO_OPOSICAO_01',
          'PRAZO_MANIFESTACAO_NULIDADE_01',
          'SOBRESTAMENTO_01'
        ];
        
        // Etapas ATUAIS que são estados finais/definitivos (sempre têm prioridade)
        const etapasAtuaisFinais = [
          'ARQUIVAMENTO_01',
          'CONCESSAO_REGISTRO_01',
          'INDEFERIMENTO_PEDIDO_01',
          'ARQUIVAMENTO_FALTA_PAGAMENTO_TAXA_01',
          'ARQUIVAMENTO_POS_RECURSO_NEGADO_01',
          'ARQUIVAMENTO_RECURSO_NAO_APRESENTADO_01',
          'DESISTENCIA_HOMOLOGADA_01',
          'REGISTRO_ANULADO_01'
        ];
        
        // Decidir qual etapa usar
        let etapaParaStatus = ultimaEtapaConcluida;
        let motivoEscolha = "última concluída";
        
        if (etapaAtual && ultimaEtapaConcluida) {
          const etapaAtualEhRelevante = etapasAtuaisRelevantes.includes(etapaAtual.idOriginal);
          const etapaAtualEhFinal = etapasAtuaisFinais.includes(etapaAtual.idOriginal);
          const ultimaConclulidaPrioridade = prioridadeEtapas[ultimaEtapaConcluida.idOriginal] || 0;
          const etapaAtualEhAnalise = etapaAtual.idOriginal.includes('ANALISE_MERITO') || etapaAtual.idOriginal.includes('ANALISE_RECURSO');
          
          // Verificar se há uma etapa concluída de muito alta prioridade (≥95)
          const ultimaConclulidaPrioridadeAlta = ultimaConclulidaPrioridade >= 95;
          
          if (etapaAtualEhFinal) {
            // Para etapas atuais finais, verificar se há causa de alta prioridade
            if (ultimaConclulidaPrioridadeAlta) {
              // Usar a etapa concluída de alta prioridade pois ela explica a causa da etapa atual
              etapaParaStatus = ultimaEtapaConcluida;
              motivoEscolha = "última concluída (alta prioridade - causa da etapa atual)";
            } else {
              // Usar a etapa atual final
              etapaParaStatus = etapaAtual;
              motivoEscolha = "etapa atual final (prioridade absoluta)";
            }
          } else if (ultimaConclulidaPrioridadeAlta) {
            // Se há etapa concluída de alta prioridade, verificar se é sobrestamento
            if (ultimaEtapaConcluida.idOriginal === 'SOBRESTAMENTO_01') {
              // Para sobrestamento, verificar se há etapas significativas posteriores
              const temEtapasPosterioresSobrestamento = etapasConcluidas.some(etapa => {
                const prioridadeEtapa = prioridadeEtapas[etapa.idOriginal] || 0;
                // Considera etapa posterior se tem prioridade ≥ 70 (decisões importantes)
                return prioridadeEtapa >= 70 && etapa !== ultimaEtapaConcluida;
              });
              
              if (temEtapasPosterioresSobrestamento) {
                // Se há etapas posteriores significativas, usar a etapa atual
                etapaParaStatus = etapaAtual;
                motivoEscolha = "etapa atual (sobrestamento superado por etapas posteriores)";
              } else {
                // Se não há etapas posteriores significativas, manter sobrestamento
                etapaParaStatus = ultimaEtapaConcluida;
                motivoEscolha = "sobrestamento (ainda ativo)";
              }
            } else {
              // Para outras etapas de alta prioridade (não sobrestamento)
              etapaParaStatus = ultimaEtapaConcluida;
              motivoEscolha = "última concluída (alta prioridade - sobrepõe etapa atual)";
            }
          } else if ((etapaAtualEhRelevante && ultimaConclulidaPrioridade < 70) || etapaAtualEhAnalise) {
            etapaParaStatus = etapaAtual;
            motivoEscolha = etapaAtualEhAnalise ? "análise (sempre relevante)" : "etapa atual (mais relevante)";
          }
        } else if (etapaAtual && !ultimaEtapaConcluida) {
          // Se não há etapa concluída, usar a atual
          etapaParaStatus = etapaAtual;
          motivoEscolha = "etapa atual (fallback)";
        }
        
        // Se encontrou uma etapa, usar seu status
        if (etapaParaStatus) {
          // Casos especiais que precisam de informação dinâmica
          if (etapaParaStatus.idOriginal === 'CONCESSAO_REGISTRO_01' && dataFimStatus) {
            return `Pedido em vigor até ${format(dataFimStatus, "dd/MM/yyyy")}.`;
          }
          
          // Para casos de renovação, incluir data de vigência
          if (etapaParaStatus.idOriginal.includes('RENOVACAO_CONCLUIDA') && dataFimStatus) {
            return `Pedido renovado, em vigor até ${format(dataFimStatus, "dd/MM/yyyy")}.`;
          }
          
          // Para casos de nulidade com registro em vigor, incluir data
          if ((etapaParaStatus.idOriginal === 'PROCESSO_NULIDADE_INSTAURADO_01' || 
               etapaParaStatus.idOriginal === 'CONCESSAO_MANTIDA_POS_NULIDADE_01') && dataFimStatus) {
            return `Pedido em vigor até ${format(dataFimStatus, "dd/MM/yyyy")}. ${etapaParaStatus.etapaDefinicao.statusDetalhado.includes('nulidade') ? 'Aguardando análise do pedido de anulação.' : ''}`;
          }
          
          // Para outros casos que mencionam [DATA_DINAMICA], substituir se tiver data
          let statusFinal = etapaParaStatus.etapaDefinicao.statusSimples;
          if (etapaParaStatus.etapaDefinicao.statusDetalhado.includes('[DATA_DINAMICA]') && dataFimStatus) {
            statusFinal = etapaParaStatus.etapaDefinicao.statusDetalhado.replace('[DATA_DINAMICA]', format(dataFimStatus, "dd/MM/yyyy"));
          } else {
            // Para a maioria dos casos, usar o statusSimples da etapa
            statusFinal = etapaParaStatus.etapaDefinicao.statusSimples;
          }
          
          return statusFinal;
        }
        
        // FALLBACK: Lógica original caso não encontre etapas adequadas
        if ((registroEmVigorLegado || registroEmVigorGenerico) && dataFimStatus) {
          return `Pedido em vigor até ${format(dataFimStatus, "dd/MM/yyyy")}.`;
        }
        if (temArquivamentoDefinitivo) {
          return "Pedido arquivado.";
        }
        if (temIndeferimento) {
          if (temNotificacaoRecursoPosIndeferimento) {
            return "Aguardando a análise do recurso contra indeferimento.";
          }
          return "Pedido negado.";
        }
        if (temDeferimentoSemConcessao) {
          if (taxaNaoPagaAposPrazo) {
            return "Taxa de concessão não foi paga.";
          }
          return "Pedido aprovado.";
        }
        if (estaEmFasePublicacao) { 
           if (dataFimOposicao && hoje > dataFimOposicao) {
             return "Aguardando pela análise do examinador do INPI.";
           } else if (dataPublicacao && hoje > dataPublicacao) {
             return "Aguardando o fim do prazo de oposição.";
           }
        }
        
        // Verificar se é um processo não publicado (protocolado mas sem despachos)
        if (dataDeposito && processo.Despacho.length === 0) {
          return "Protocolo realizado. Aguardando publicação.";
        }
        
        return "Aguardando pela análise do examinador do INPI.";
      };

    // --- Gerar etapas da timeline unificada ---
    const etapasTimeline = gerarTimelineSimplificada(processo as any, estimativasMerito);

    // --- Determinação de Cores e Ícones baseada nas ETAPAS ---
    let cardBgColorClass = "bg-white";
    let sidebarBgColorClass = "bg-white";
    let sidebarIconComponent: React.FC | null = null;
    let showSidebar = false;

    // Verificar tipos de etapas na timeline para determinar cores
    const temEtapaArquivamento = etapasTimeline.some(etapa => 
      etapa.idOriginal === 'ARQUIVAMENTO_01' || 
      etapa.etapaDefinicao.nomeOriginal.toLowerCase().includes('arquivamento')
    );
    
    const temEtapaConcessao = etapasTimeline.some(etapa => 
      etapa.idOriginal === 'CONCESSAO_REGISTRO_01' || 
      etapa.etapaDefinicao.nomeOriginal.toLowerCase().includes('concessão')
    );
    
    const temEtapaIndeferimento = etapasTimeline.some(etapa => 
      etapa.idOriginal === 'INDEFERIMENTO_01' || 
      etapa.etapaDefinicao.nomeOriginal.toLowerCase().includes('indeferimento')
    );
    
    const temEtapaDeferimento = etapasTimeline.some(etapa => 
      etapa.idOriginal === 'DEFERIMENTO_01' || 
      etapa.etapaDefinicao.nomeOriginal.toLowerCase().includes('deferimento')
    );
    
    const temEtapaPublicacao = etapasTimeline.some(etapa => 
      etapa.idOriginal === 'PUBLICACAO_OPOSICAO_01' || 
      etapa.etapaDefinicao.nomeOriginal.toLowerCase().includes('publicação')
    );

    // Verificar se há registro em vigor (concessão concluída)
    const registroEmVigor = etapasTimeline.some(etapa => 
      (etapa.idOriginal === 'CONCESSAO_REGISTRO_01'  &&
      etapa.status === 'CONCLUIDA'
    ) || registroEmVigorLegado || registroEmVigorGenerico);

    // Aplicar cores baseadas na prioridade das etapas
    if (registroEmVigor) {
      cardBgColorClass = "bg-fundoVigor";
      sidebarBgColorClass = "bg-[#4597B5]";
      sidebarIconComponent = StatusVigorIcon;
    } else if (temEtapaIndeferimento) {
      cardBgColorClass = "bg-fundoIndeferido";
      sidebarBgColorClass = "bg-[#E0E000]";
      sidebarIconComponent = StatusIndeferidoIcon;
    } else if (temEtapaArquivamento) {
      cardBgColorClass = "bg-fundoArquivado";
      sidebarBgColorClass = "bg-[#F04B4B]";
      sidebarIconComponent = StatusArquivadoIcon;
    } else if (temEtapaDeferimento) {
      cardBgColorClass = "bg-fundoDeferido";
      sidebarBgColorClass = "bg-[#45B063]";
      sidebarIconComponent = StatusDeferidoIcon;
    } else if (temEtapaPublicacao) {
      cardBgColorClass = "bg-white";
      sidebarBgColorClass = "bg-[#E1E1E1]";
      sidebarIconComponent = StatusPublicacaoIcon;
    }

    showSidebar = registroEmVigor || temEtapaIndeferimento || temEtapaArquivamento || temEtapaDeferimento || temEtapaPublicacao;

    // --- Valores para retorno ---
    let dataMeritoEstimadaFormatada: string | null = null;
    if (dataMeritoEstimada) {
      dataMeritoEstimadaFormatada = format(dataMeritoEstimada, "MMM, yyyy", {
        locale: ptBR,
      }).replace(".", "").replace(/^\w/, c => c.toUpperCase());
    }

    const logoImage = getLogoImage();
    const nomeMarca = processo.Marca?.nome || "Sem nome";
    const codigoNCL = processo.Marca?.NCL?.[0]?.codigo || null;
    const linkInpi = `https://busca.inpi.gov.br/pePI/servlet/MarcasServletController?Action=searchMarca&tipoPesquisa=BY_NUM_PROC&Source=OAMI&NumPedido=${processo.numero.replace(
      /\D/g,
      ""
    )}`;
    const statusText = getStatusText();
    const showOposicaoBadge = processo.oposicao;

    // --- A timeline não deve ocupar espaço quando for apenas texto de previsão ---
    const useSpacingForTimeline = false; // Simplificado, pois não temos mais componentes específicos

    // Retorna o objeto
    return {
      statusText,
      cardBgColorClass,
      sidebarBgColorClass,
      sidebarIconComponent,
      showSidebar,
      logoImage,
      nomeMarca,
      linkInpi,
      codigoNCL,
      showOposicaoBadge,
      etapasTimeline,
      dataMeritoEstimadaFormatada,
      useSpacingForTimeline,
      rawDespachos: processo.Despacho
    };
  }, [processo, estimativasMerito]); // memo dependendo apenas do processo e das estimativas
};