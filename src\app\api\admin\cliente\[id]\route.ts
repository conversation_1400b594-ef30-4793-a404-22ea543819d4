import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getAdminFromRequest, requireAdminRole } from '@/lib/adminAuth';

// GET - Buscar dados completos do cliente
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Verificar autenticação e role MASTER
    const user = await getAdminFromRequest(request);
    if (!user || !requireAdminRole(['MASTER'])(user)) {
      return NextResponse.json(
        { error: 'Acesso negado. Apenas usuários MASTER podem acessar esta funcionalidade.' },
        { status: 403 }
      );
    }
    const resolvedParams = await params;
    const clienteId = parseInt(resolvedParams.id);

    if (isNaN(clienteId)) {
      return NextResponse.json(
        { error: 'ID do cliente inválido' },
        { status: 400 }
      );
    }

    const cliente = await prisma.cliente.findUnique({
      where: {
        id: clienteId,
      },
      include: {
        ContatoCliente: true,
      },
    });

    if (!cliente) {
      return NextResponse.json(
        { error: 'Cliente não encontrado' },
        { status: 404 }
      );
    }

    return NextResponse.json({ cliente });

  } catch (error) {
    console.error('Erro ao buscar cliente:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

// PUT - Atualizar dados do cliente
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Verificar autenticação e role MASTER
    const user = await getAdminFromRequest(request);
    if (!user || !requireAdminRole(['MASTER'])(user)) {
      return NextResponse.json(
        { error: 'Acesso negado. Apenas usuários MASTER podem acessar esta funcionalidade.' },
        { status: 403 }
      );
    }
    const resolvedParams = await params;
    const clienteId = parseInt(resolvedParams.id);

    if (isNaN(clienteId)) {
      return NextResponse.json(
        { error: 'ID do cliente inválido' },
        { status: 400 }
      );
    }

    const body = await request.json();
    const { nome, tipoDeDocumento, numeroDocumento, nomeDaMarca, metodoComunicacao, contatos } = body;

    // Validações básicas
    if (!nome || nome.trim() === '') {
      return NextResponse.json(
        { error: 'Nome é obrigatório' },
        { status: 400 }
      );
    }

    // Atualizar o cliente usando uma transação
    const clienteAtualizado = await prisma.$transaction(async (tx) => {
      // Atualizar dados básicos do cliente
      const cliente = await tx.cliente.update({
        where: { id: clienteId },
        data: {
          nome: nome.trim(),
          tipoDeDocumento,
          numeroDocumento,
          nomeDaMarca,
          metodoComunicacao,
        },
      });

      // Se foram fornecidos contatos, atualizar/criar
      if (contatos && Array.isArray(contatos)) {
        // Remover contatos existentes
        await tx.contatoCliente.deleteMany({
          where: { clienteId },
        });

        // Criar novos contatos (apenas os que têm pelo menos um campo preenchido)
        const contatosValidos = contatos.filter(contato => 
          contato.email || contato.telefone || contato.telefoneSegundario || contato.endereco || contato.cidade || contato.estado || contato.cep
        );

        if (contatosValidos.length > 0) {
          await tx.contatoCliente.createMany({
            data: contatosValidos.map(contato => ({
              clienteId,
              email: contato.email || null,
              telefone: contato.telefone || null,
              telefoneSegundario: contato.telefoneSegundario || null,
              endereco: contato.endereco || null,
              cidade: contato.cidade || null,
              estado: contato.estado || null,
              cep: contato.cep || null,
            })),
          });
        }
      }

      // Buscar cliente atualizado com contatos
      return await tx.cliente.findUnique({
        where: { id: clienteId },
        include: {
          ContatoCliente: true,
        },
      });
    });

    return NextResponse.json({ 
      message: 'Cliente atualizado com sucesso',
      cliente: clienteAtualizado 
    });

  } catch (error) {
    console.error('Erro ao atualizar cliente:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
} 