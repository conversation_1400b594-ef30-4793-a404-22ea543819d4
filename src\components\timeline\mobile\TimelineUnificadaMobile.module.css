.timelineContainer {
  display: flex;
  flex-direction: column;
  /* padding: 10px 0; */ /* Removido para que os marcadores possam ir até as bordas */
  padding: 0;
  position: relative; 
}

/* Linha base vertical contínua */
.linhaBaseMobile {
  position: absolute;
  left: 89px; /* Alinhado ao centro do marcador, considerando margin-left de 80px no .etapaContainer + 10px (metade do marcador) - 1px (metade da linha) */
  top: 10px; /* Espaço para a metade superior do primeiro marcador */
  bottom: 10px; /* Espaço para a metade inferior do último marcador */
  width: 2px;
  background-color: #C3C3C3;
  z-index: 0; 
}

/* Linha de progresso vertical contínua */
.linhaProgressoMobile {
  position: absolute;
  left: 86.5px; /* Alinhado ao centro do marcador, considerando margin-left de 80px no .etapaContainer + 10px (metade do marcador) - 3.5px (metade da linha) */
  top: 10px; /* Deve começar alinhado com a linhaBaseMobile */
  width: 7px; 
  background-color: #4597B5;
  z-index: 1; 
  height: 0%; /* Altura inicial, será sobrescrita pelo style */
  transition: height 0.3s ease;
  transform-origin: top;
}

.etapaContainer {
  display: flex;
  align-items: flex-start; /* Alinha data, marcador, conteúdo pelo topo */
  position: relative; /* Para posicionamento absoluto da data */
  padding-bottom: 20px;
  margin-left: 80px; /* Espaço para data à esquerda + gap */
}

.etapaContainer:last-child {
  padding-bottom: 0;
}

.dataEsquerdaMobile {
  position: absolute;
  width: 70px; /* Largura da coluna da data */
  left: -80px; /* (Largura da data + gap) negativo para posicionar à esquerda */
  top: 2px; /* Ajuste fino para alinhar verticalmente com o nome da etapa */
  text-align: right;
  font-size: 12px;
  color: #666666;
  line-height: 1.3;
}

/* Marcador */
.marcador {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: white;
  border: 2px solid #C3C3C3;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  /* margin-right: 12px; */ /* Removido, o .conteudo terá margin-left */
  z-index: 2; 
  position: relative; 
}

.marcadorAlcancado {
  background-color: #4597B5;
  border-color: #4597B5;
}

.marcadorSobrestamento {
  background-color: #848484;
  border-color: #848484;
  position: relative;
}

.marcadorSobrestamento::before {
  content: '';
  position: absolute;
  width: 8px;
  height: 8px;
  background-color: white;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border-radius: 0;
}

/* Marcador de prazo pendente (data não alcançada) */
.marcadorPrazoPendente {
  background-color: white;
  border-color: #E0E000;
}

/* Marcador de recurso expirado sem apresentação */
.marcadorRecursoExpirado {
  background-color: white;
  border-color: #FF0000;
  border-width: 2px;
}

/* Marcador opaco (análise de mérito quando há sobrestamento) */
.marcadorOpaco {
  opacity: 0.5;
}

.checkIcon {
  color: white;
  font-size: 10px;
  font-weight: bold;
}

/* Conteúdo */
.conteudo {
  flex: 1;
  padding-top: 0; /* Mantido, pode ser ajustado */
  margin-left: 12px; /* Espaço entre marcador e conteúdo à direita */
}

.nomeEtapa {
  font-size: 14px;
  font-weight: 600;
  color: #000000;
  /* margin-bottom: 4px; */ /* Removido, .statusSimplesMobile terá margin-top */
  line-height: 1.3;
}

.statusSimplesMobile {
  font-size: 11px;
  color: #555555; /* Cor um pouco mais escura para diferenciar da data */
  margin-top: 2px;
  line-height: 1.3;
}

.data {
  /* Este estilo não será mais usado para a data principal, que está em .dataEsquerdaMobile */
  /* Pode ser removido ou reaproveitado se houver outras datas no .conteudo */
  display: none; /* Ocultar por enquanto */
}

.dataPrevisao {
  color: #45B063;
  font-style: italic;
}

/* Estilo para datas de previsão (Ex: Análise de Mérito) na versão mobile */
.dataPrevisaoMobile {
  color: #45B063 !important; /* Verde, igual ao desktop */
  /* Outros estilos como font-weight podem ser adicionados se necessário */
}

.dataEstimativa {
  color: #A3A3A3;
  font-size: 12px;
  font-weight: 500;
}

/* Estilos para as linhas da data de concessão estimada (mobile) */
.dataEstimativaLinha1Mobile {
  font-size: 11px; /* Um pouco menor para caber */
  font-weight: 500; /* Similar ao desktop dataEstimativaLabel */
  line-height: 1.2;
  color: #A3A3A3;
}

.dataEstimativaLinha2Mobile {
  font-size: 12px; /* Tamanho padrão da data */
  font-weight: 500; /* Similar ao desktop dataEstimativaValor */
  line-height: 1.2;
  color: #666666; /* Cor padrão da data mobile */
}

.statusAtual {
  font-size: 11px;
  color: #4597B5;
  font-weight: 500;
  background-color: #E8F4F8;
  padding: 2px 8px;
  border-radius: 4px;
  display: inline-block;
  margin-top: 4px;
}

/* Estilos especiais para Taxa de Concessão */
.nomeEtapaTaxaConcessao {
  font-size: 14px;
  font-weight: 600;
  color: #FF0000 !important;
  margin-bottom: 4px;
  line-height: 1.3;
}

.subtextoTaxaConcessao {
  color: #969696;
  font-size: 14px;
  font-style: italic;
  font-weight: normal;
  margin-top: 2px;
  text-align: left;
}

/* Marcador de prazo de manifestação de nulidade */
.marcadorPrazoManifestacao {
  background-color: white;
  border-color: #e6d72c;
}

/* Marcador de exigência ativa */
.marcadorExigenciaAtiva {
  background-color: white;
  border-color: #e6d72c;
}

/* Marcador de taxa de concessão */
.marcadorTaxaConcessao {
  background-color: white;
  border-color: #e6d72c;
}

/* Conteúdo opaco (análise de mérito quando há sobrestamento) */
.conteudoOpaco {
  opacity: 0.5;
}

/* ==================== CLASSES ESPECIAIS PARA TIPOS DE MARCADORES ==================== */


/* Etapas que ficam no FINAL da timeline vertical (equivalente ao final da linha horizontal) */
.etapaArquivamento {
  /* Arquivamento vai para o final da timeline vertical */
  order: 999;
}

.etapaRecursoExpirado {
  /* Recurso expirado vai para o final da timeline vertical */
  order: 998;
}

/* ==================== ESTILOS ESPECIAIS PARA ELEMENTOS NO FINAL ==================== */

/* Data especial para marcadores do final */
.dataEspecialFinal {
  /* Na timeline vertical, a data fica à esquerda mas pode ter alinhamento especial */
  font-style: italic;
  color: #FF0000; /* Cor vermelha para destacar */
}

/* Para recurso expirado especificamente */
.etapaRecursoExpirado .dataEsquerdaMobile {
  color: #FF0000;
  font-weight: 600;
}

.etapaRecursoExpirado .nomeEtapa {
  color: #FF0000;
}

/* Para arquivamento especificamente */
.etapaArquivamento .dataEsquerdaMobile {
  color: #666666;
}

.etapaArquivamento .nomeEtapa {
  color: #000000;
}

/* Nome da etapa opaco */
.nomeEtapaOpaco {
  opacity: 0.5;
  font-size: 13px; /* Similar ao desktop */
}

/* Status simples opaco */
.statusSimplesOpaco {
  opacity: 0.5;
}

/* Data opaca */
.dataOpaca {
  opacity: 0.5;
}

/* Marcadores de processo não publicado (mobile) */
.etapaPublicacaoEstimada .nomeEtapa {
  color: #666666; /* Cor mais sutil para estimativa */
}

.etapaFimOposicaoEstimado .nomeEtapa {
  color: #666666; /* Cor mais sutil para estimativa */
}

.etapaAnaliseEstimada .nomeEtapa {
  color: #666666; /* Cor mais sutil para estimativa */
}

/* Marcadores de publicação estimada, fim de oposição estimado e análise estimada */
.marcadorPublicacaoEstimada {
  background-color: white;
  border-color: #A3A3A3; /* Cor mais sutil para estimativa */
}

.marcadorFimOposicaoEstimado {
  background-color: white;
  border-color: #A3A3A3; /* Cor mais sutil para estimativa */
}

.marcadorAnaliseEstimada {
  background-color: white;
  border-color: #A3A3A3; /* Cor mais sutil para estimativa */
}

/* Estilos específicos para textos de previsão de processo não publicado (mobile) */
.etapaPublicacaoEstimada .dataEsquerdaMobile,
.etapaFimOposicaoEstimado .dataEsquerdaMobile {
  font-size: 12px;
  font-weight: 500;
  color: #45B063;
}

.etapaPublicacaoEstimada .dataEstimativaLinha1Mobile,
.etapaFimOposicaoEstimado .dataEstimativaLinha1Mobile,
.etapaPublicacaoEstimada .dataEstimativaLinha2Mobile,
.etapaFimOposicaoEstimado .dataEstimativaLinha2Mobile {
  color: #45B063;
  font-size: 12px;
  font-weight: 500;
}

/* Ajuste para o último marcador de análise de mérito (mobile) */
.etapaAnaliseEstimada {
  margin-top: 10%; /* Posiciona 10% antes do fim da linha */
}

/* Ajuste da barra de progresso para processo não publicado (mobile) */
.timelineContainer:has(.etapaPublicacaoEstimada) .linhaProgressoMobile {
  transition: height 0.5s ease-in-out;
  background: linear-gradient(180deg, #4597B5 0%, #4597B5 100%);
  background-size: 100% 200%;
  animation: progressAnimationMobile 2s linear infinite;
}

@keyframes progressAnimationMobile {
  0% {
    background-position: 0 100%;
  }
  100% {
    background-position: 0 -100%;
  }
} 