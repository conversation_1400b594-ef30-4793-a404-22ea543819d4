const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function clearAdminSessions() {
  try {
    console.log('🧹 Limpando todas as sessões admin...');
    
    const deletedSessions = await prisma.adminSession.deleteMany();
    
    console.log(`✅ ${deletedSessions.count} sessões removidas`);
    console.log('💡 Agora você pode fazer login novamente com o cookie corrigido');
    
  } catch (error) {
    console.error('❌ Erro ao limpar sessões:', error);
  } finally {
    await prisma.$disconnect();
  }
}

clearAdminSessions(); 