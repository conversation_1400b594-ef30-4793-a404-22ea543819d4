export type TipoEtapa = 
  | 'ACONTECIMENTO' 
  | 'PRAZO' 
  | 'ACAO_NECESSARIA'
  | 'ESTADO_GERAL'; // Para etapas como Sobrestamento, Arquivamento

export interface EtapaDefinicao {
  id: string; 
  nomeOriginal: string; 
  tipo: TipoEtapa;
  
  statusSimples: string; 
  statusDetalhado: string; 
  
  proximaEtapaNomeReferencia?: string; 
  prazoProximaEtapaDescricao?: string; 
  
  tagsObservacao?: string[]; 
}

export const definicoesEtapas: EtapaDefinicao[] = [
  {
    id: "PROTOCOLO_01",
    nomeOriginal: "Protocolo",
    tipo: "ACONTECIMENTO",
    statusSimples: "Protocolado",
    statusDetalhado: "Protocolado, aguardando a publicação.",
    proximaEtapaNomeReferencia: "Publicação",
    prazoProximaEtapaDescricao: "20 a 30 dias",
  },
  {
    id: "PUBLICACAO_01",
    nomeOriginal: "Publicação",
    tipo: "ACONTECIMENTO",
    statusSimples: "Publicado",
    statusDetalhado: "Publicado, aguardando o fim do prazo de oposição.",
    proximaEtapaNomeReferencia: "Prazo para oposição",
    prazoProximaEtapaDescricao:
      "60 dias, contados a partir da publicação do pedido de registro",
  },
  {
    id: "PUBLICACAO_COM_OPOSICAO_01", // ID Diferente para a variação
    nomeOriginal: "Publicação", // Mesmo nome original, mas contexto diferente
    tipo: "ACONTECIMENTO",
    statusSimples: "Publicado (com oposição)",
    statusDetalhado:
      'Publicado, aguardando o fim do prazo de oposição. (Com a TAG "Pedido com oposição")',
    proximaEtapaNomeReferencia: "Prazo para oposição",
    prazoProximaEtapaDescricao:
      "60 dias, contados a partir da publicação do pedido de registro",
    tagsObservacao: ["Pedido com oposição"],
  },
  {
    id: "PRAZO_OPOSICAO_COM_OPOSICAO_01",
    nomeOriginal: "Prazo para oposição",
    tipo: "PRAZO",
    statusSimples: "Aguardando análise (com oposição)", // Este status pode ser revisto depois, pois se há oposição, o próximo é manifestar.
    statusDetalhado:
      'Prazo para terceiros se oporem ao pedido de registro. (Com a TAG "Pedido com oposição")',
    proximaEtapaNomeReferencia: "Prazo para manifestação sobre Oposição", // Aponta para a nova etapa de prazo
    prazoProximaEtapaDescricao:
      "60 dias, contados a partir da notificação da oposição", // Prazo para a *próxima* etapa (manifestação)
    tagsObservacao: ["Pedido com oposição"],
  },
  {
    id: "PRAZO_MANIFESTACAO_OPOSICAO_01",
    nomeOriginal: "Prazo para manifestação",
    tipo: "PRAZO",
    statusSimples: "Aguardando análise (com oposição)",
    statusDetalhado: "Aguardando pela análise do examinador do INPI.",
    proximaEtapaNomeReferencia: "Análise de mérito", // Após manifestar (ou não), vai para análise
    prazoProximaEtapaDescricao:
      "Calculado pela data de depósito do pedido, com base nas 4 ultimas RPIs", // Para a análise de mérito
    tagsObservacao: ["Pedido com oposição"],
  },
  {
    id: "MANIFESTACAO_OPOSICAO_APRESENTADA_01",
    nomeOriginal: "Manifestação", // Mantém o nome original para referência ao .md
    tipo: "ACONTECIMENTO",
    statusSimples: "Manifestação apresentada",
    statusDetalhado:
      "Manifestação à oposição foi apresentada. Aguardando análise de mérito.",
    proximaEtapaNomeReferencia: "Análise de mérito",
    prazoProximaEtapaDescricao:
      "Calculado pela data de depósito do pedido, com base nas 4 ultimas RPIs", // Este prazo é para a próxima etapa (Análise de Mérito)
    tagsObservacao: ["Pedido com oposição", "Manifestação Realizada"],
  },
  {
    id: "PRAZO_OPOSICAO_SEM_OPOSICAO_01",
    nomeOriginal: "Prazo para oposição",
    tipo: "PRAZO",
    statusSimples: "Aguardando análise",
    statusDetalhado: "Aguardando pela análise do examinador do INPI.",
    proximaEtapaNomeReferencia: "Análise de mérito",
    prazoProximaEtapaDescricao:
      "Calculado pela data de depósito do pedido, com base nas 4 ultimas RPIs",
  },
  {
    id: "INDICADOR_EXIGENCIA_ATIVA_01",
    nomeOriginal: "⚠️ Exigência", // Nome interno para referência
    tipo: "ACAO_NECESSARIA",
    statusSimples: "Cumprir exigência", // Nome para exibição
    statusDetalhado:
      "Aguardando que a exigência feita pelo INPI seja respondida.",
    // proximaEtapaNomeReferencia: undefined, // Não é sequencial no fluxo principal
    prazoProximaEtapaDescricao:
      "Prazo: 60 dias corridos a partir da publicação da exigência de mérito.",
    tagsObservacao: ["Requer Atenção Imediata"],
  },
  {
    id: "SOBRESTAMENTO_01",
    nomeOriginal: "Sobrestamento",
    tipo: "ESTADO_GERAL",
    statusSimples: "Sobrestado",
    statusDetalhado:
      "Análise do pedido foi suspensa até que outro(s) processo(s) conflitante(s) seja(m) julgado(s).",
    proximaEtapaNomeReferencia: "ANALISE_MERITO_SEM_OPOSICAO_01",
    prazoProximaEtapaDescricao: "Sem prazo estimado",
  },
  {
    id: "EXIGENCIA_NAO_CUMPRIDA_01",
    nomeOriginal: "Exigência Não Cumprida",
    tipo: "ACONTECIMENTO",
    statusSimples: "Exigência não cumprida",
    statusDetalhado:
      "A exigência não foi cumprida dentro do prazo legal, resultando no arquivamento do pedido.",
    proximaEtapaNomeReferencia: "Arquivamento",
    prazoProximaEtapaDescricao: "N/A",
  },
  {
    id: "ARQUIVAMENTO_01",
    nomeOriginal: "Arquivamento",
    tipo: "ESTADO_GERAL",
    statusSimples: "Arquivado",
    statusDetalhado: "Pedido arquivado.",
    proximaEtapaNomeReferencia: "X",
    prazoProximaEtapaDescricao: "nan",
  },
  {
    id: "ANALISE_MERITO_SEM_OPOSICAO_01",
    nomeOriginal: "Análise de mérito",
    tipo: "ESTADO_GERAL",
    statusSimples: "Aguardando análise de mérito",
    statusDetalhado:
      "Pedido aguardando análise de mérito pelo INPI (sem oposição).",
    proximaEtapaNomeReferencia: "Decisão do INPI", // Genérico
    prazoProximaEtapaDescricao:
      "TODO: Definir lógica de estimativa de prazo para análise de mérito.",
  },
  {
    id: "ANALISE_MERITO_COM_OPOSICAO_SEM_MANIFESTACAO_01",
    nomeOriginal: "Análise de mérito",
    tipo: "ESTADO_GERAL",
    statusSimples: "Aguardando análise de mérito (com oposição)",
    statusDetalhado:
      "Pedido aguardando análise de mérito pelo INPI (com oposição, sem manifestação do titular).",
    proximaEtapaNomeReferencia: "Decisão do INPI", // Genérico
    prazoProximaEtapaDescricao:
      "TODO: Definir lógica de estimativa de prazo para análise de mérito.",
    tagsObservacao: ["Pedido com oposição"],
  },
  {
    id: "ANALISE_MERITO_COM_OPOSICAO_COM_MANIFESTACAO_01",
    nomeOriginal: "Análise de mérito",
    tipo: "ESTADO_GERAL",
    statusSimples: "Aguardando análise de mérito (com oposição)",
    statusDetalhado:
      "Pedido aguardando análise de mérito pelo INPI (com oposição, manifestação do titular apresentada).",
    proximaEtapaNomeReferencia: "Decisão do INPI", // Genérico
    prazoProximaEtapaDescricao:
      "TODO: Definir lógica de estimativa de prazo para análise de mérito.",
    tagsObservacao: ["Pedido com oposição", "Manifestação Realizada"],
  },
  {
    id: "ANALISE_MERITO_COM_EXIGENCIA_01",
    nomeOriginal: "Análise de mérito",
    tipo: "ESTADO_GERAL",
    statusSimples: "Aguardando análise de mérito (com exigência)",
    statusDetalhado:
      "Pedido aguardando análise de mérito pelo INPI (com exigência de mérito ativa).",
    proximaEtapaNomeReferencia: "Decisão do INPI", // Genérico
    prazoProximaEtapaDescricao:
      "Estimativa baseada na média de processos com exigência de mérito.",
    tagsObservacao: ["Exigência de Mérito"],
  },
  // Novas etapas de Mérito e Concessão
  {
    id: "DEFERIMENTO_PEDIDO_01",
    nomeOriginal: "Deferimento do Pedido",
    tipo: "ACONTECIMENTO",
    statusSimples: "Deferido",
    statusDetalhado:
      "Pedido aprovado, aguardando pagamento da taxa de concessão.",
    proximaEtapaNomeReferencia:
      "Prazo para Pagamento da Taxa de Concessão (Ordinário)",
    prazoProximaEtapaDescricao:
      "60 dias corridos a partir da data do despacho de deferimento.",
  },
  {
    id: "PRAZO_PAGAMENTO_TAXA_CONCESSAO_ORDINARIO_01",
    nomeOriginal: "Prazo para Pagamento da Taxa de Concessão (Ordinário)",
    tipo: "PRAZO",
    statusSimples: "Deferido",
    statusDetalhado:
      "Pedido aprovado, aguardando pagamento da taxa de concessão.",
    proximaEtapaNomeReferencia:
      "Prazo para Pagamento da Taxa de Concessão (Extraordinário)", // Ou Concessão, se pago
    prazoProximaEtapaDescricao:
      "30 dias corridos após o término do prazo ordinário (com taxa adicional).",
  },
  {
    id: "PRAZO_PAGAMENTO_TAXA_CONCESSAO_EXTRAORDINARIO_01",
    nomeOriginal: "Prazo para Pagamento da Taxa de Concessão (Extraordinário)",
    tipo: "PRAZO",
    statusSimples: "Deferido",
    statusDetalhado:
      "Pedido aprovado, aguardando pagamento da taxa de concessão em prazo extraordinário.",
    proximaEtapaNomeReferencia: "Arquivamento por Falta de Pagamento da Taxa", // Se não pago
    prazoProximaEtapaDescricao:
      "Ocorre se a taxa não for paga dentro do prazo extraordinário.",
  },
  {
    id: "TAXA_CONCESSAO_PAGA_01", // Etapa intermediária para indicar pagamento
    nomeOriginal: "Taxa de Concessão Paga",
    tipo: "ACONTECIMENTO",
    statusSimples: "Deferido",
    statusDetalhado: "Pedido aprovado, aguardando publicação da concessão.",
    proximaEtapaNomeReferencia: "Concessão do Registro",
    prazoProximaEtapaDescricao: "Aguardando despacho de concessão na RPI.",
  },
  {
    id: "CONCESSAO_REGISTRO_01",
    nomeOriginal: "Concessão",
    tipo: "ACONTECIMENTO",
    statusSimples: "Em vigor",
    statusDetalhado: "Pedido em vigor até [DATA_DINAMICA].",
    // proximaEtapaNomeReferencia: 'VIGENCIA_REGISTRO_01', // Exemplo para futura etapa de vigência
    // prazoProximaEtapaDescricao: '10 anos a partir da data de concessão.',
  },
  {
    id: "CONCESSAO_ESTIMADA_01",
    nomeOriginal: "Concessão do Registro",
    tipo: "ESTADO_GERAL",
    statusSimples: "Aguardando Concessão",
    statusDetalhado:
      "Estimativa de concessão do registro após pagamento da taxa.",
    proximaEtapaNomeReferencia: "FIM_PRAZO_NULIDADE_01",
    prazoProximaEtapaDescricao:
      "45 dias após o fim do prazo para pagamento da taxa de concessão, ajustado para a próxima terça-feira útil.",
    tagsObservacao: ["Estimativa", "Concessão Prevista"],
  },
  {
    id: "ARQUIVAMENTO_FALTA_PAGAMENTO_TAXA_01",
    nomeOriginal: "Arquivamento",
    tipo: "ESTADO_GERAL", // Similar ao Arquivamento geral
    statusSimples: "Arquivado (falta de pagamento)",
    statusDetalhado:
      "Pedido arquivado por falta de pagamento da taxa de concessão.",
    proximaEtapaNomeReferencia: "X",
    prazoProximaEtapaDescricao: "nan",
  },
  // Novas Etapas para Indeferimento e Recurso
  {
    id: "INDEFERIMENTO_PEDIDO_01",
    nomeOriginal: "Indeferimento", // Corresponde ao nome do despacho
    tipo: "ACONTECIMENTO",
    statusSimples: "Indeferido (em fase de recurso)",
    statusDetalhado: "Pedido recusado, aguardando análise do recurso.",
    proximaEtapaNomeReferencia: "PRAZO_RECURSO_INDEFERIMENTO_01",
    prazoProximaEtapaDescricao:
      "Prazo para recurso: 60 dias a partir da publicação do indeferimento.",
  },
  {
    id: "PRAZO_RECURSO_INDEFERIMENTO_01",
    nomeOriginal: "Recurso",
    tipo: "ACAO_NECESSARIA",
    statusSimples: "Indeferido (em fase de recurso)",
    statusDetalhado: "Pedido recusado, no prazo para apresentação de recurso.",
    // proximaEtapaNomeReferencia: 'ANALISE_RECURSO_01', // Exemplo para futura etapa de análise de recurso
    // prazoProximaEtapaDescricao: 'Variável', // Para a próxima etapa, se houver
    tagsObservacao: ["Requer Atenção Imediata"],
  },
  // Novas Etapas para Recurso Apresentado e Análise de Recurso
  {
    id: "RECURSO_INDEFERIMENTO_APRESENTADO_01",
    nomeOriginal: "Recurso Publicado",
    tipo: "ACONTECIMENTO",
    statusSimples: "Recurso Publicado",
    statusDetalhado: "Recurso contra o indeferimento foi publicado na RPI.",
    proximaEtapaNomeReferencia: "ANALISE_RECURSO_INDEFERIMENTO_01",
    prazoProximaEtapaDescricao: "Aguardando análise do recurso.", // Este é o lead time para a próxima etapa
  },
  {
    id: "ANALISE_RECURSO_INDEFERIMENTO_01",
    nomeOriginal: "Análise do Recurso",
    tipo: "ESTADO_GERAL", // Ou PRAZO, se preferir focar na estimativa
    statusSimples: "Indeferido (recurso em andamento)", // Conforme solicitado
    statusDetalhado: "Pedido recusado, aguardando análise do recurso.", // Conforme solicitado
    // proximaEtapaNomeReferencia: 'DECISAO_RECURSO_01', // Exemplo para futura etapa de decisão do recurso
    prazoProximaEtapaDescricao:
      "Estimativa de decisão: ~600 dias (TODO: buscar do BD)",
  },
  // Novas Etapas para Decisão de Recurso
  {
    id: "RECURSO_PROVIDO_TRANSICAO_DEFERIMENTO_01",
    nomeOriginal: "Recurso Provido (Trânsito para Deferimento)",
    tipo: "ACONTECIMENTO",
    statusSimples: "Recurso Aceito",
    statusDetalhado:
      "Decisão de indeferimento reformada após recurso. Processo encaminhado para deferimento.",
    proximaEtapaNomeReferencia: "DEFERIMENTO_POS_RECURSO_01",
    prazoProximaEtapaDescricao: "Imediato",
  },
  {
    id: "DEFERIMENTO_POS_RECURSO_01",
    nomeOriginal: "Deferimento do Pedido (Pós-Recurso)",
    tipo: "ACONTECIMENTO", // Será concluído e levará ao prazo de pagamento
    statusSimples: "Deferido (após recurso)",
    statusDetalhado:
      "Decisão reformada para deferimento, após apresentação de recurso. Aguardando pagamento da taxa de concessão.",
    proximaEtapaNomeReferencia: "PRAZO_PAGAMENTO_TAXA_CONCESSAO_ORDINARIO_01",
    prazoProximaEtapaDescricao:
      "60 dias corridos a partir da data do despacho de recurso provido.",
  },
  {
    id: "RECURSO_NEGADO_01",
    nomeOriginal: "Recurso Não Provido",
    tipo: "ACONTECIMENTO",
    statusSimples: "Recurso Negado",
    statusDetalhado:
      "Recurso contra o indeferimento não foi aceito. Decisão de indeferimento mantida pelo INPI.",
    proximaEtapaNomeReferencia: "ARQUIVAMENTO_POS_RECURSO_NEGADO_01",
    prazoProximaEtapaDescricao: "Imediato",
  },
  {
    id: "ARQUIVAMENTO_POS_RECURSO_NEGADO_01",
    nomeOriginal: "Arquivamento (Pós Recurso Negado)",
    tipo: "ESTADO_GERAL",
    statusSimples: "Arquivado (Recurso Negado)",
    statusDetalhado:
      "Pedido arquivado após negativa do recurso contra o indeferimento. Este é um estado final para o processo.",
    proximaEtapaNomeReferencia: "X",
    prazoProximaEtapaDescricao: "nan",
  },
  // Nova Etapa para Arquivamento por Não Apresentação de Recurso
  {
    id: "ARQUIVAMENTO_RECURSO_NAO_APRESENTADO_01",
    nomeOriginal: "Arquivamento",
    tipo: "ESTADO_GERAL",
    statusSimples: "Arquivado (sem recurso)",
    statusDetalhado:
      "Pedido arquivado pois o prazo para recurso contra o indeferimento expirou sem que um recurso fosse apresentado.",
    proximaEtapaNomeReferencia: "X", // Estado final
    prazoProximaEtapaDescricao: "nan",
  },
  // Nova Etapa Intermediária para Aguardar Arquivamento
  {
    id: "AGUARDANDO_ARQUIVAMENTO_SEM_RECURSO_01",
    nomeOriginal: "Recurso ⭕️",
    tipo: "ESTADO_GERAL",
    statusSimples: "Prazo Rec. Expirado",
    statusDetalhado:
      "Prazo para recurso contra indeferimento expirou. Aguardando formalização do arquivamento pelo INPI.",
    proximaEtapaNomeReferencia: "ARQUIVAMENTO_RECURSO_NAO_APRESENTADO_01", // Ou ARQUIVAMENTO_01 se o despacho for genérico
    prazoProximaEtapaDescricao:
      "Estimativa: 20-30 dias para despacho de arquivamento.",
  },
  // --- Etapas de Nulidade e Renovação ---
  {
    id: "FIM_PRAZO_NULIDADE_01",
    nomeOriginal: "Prazo para Nulidade",
    tipo: "ACONTECIMENTO",
    statusSimples: "Prazo Nulidade Encerrado",
    statusDetalhado:
      "Prazo de 180 dias para instauração de processo de nulidade administrativa. Após esta data, não é mais possível instaurar nulidade.",
    proximaEtapaNomeReferencia: "RENOVACAO_01",
    prazoProximaEtapaDescricao:
      "180 dias contados da data de publicação da concessão.",
  },
  {
    id: "PROCESSO_NULIDADE_INSTAURADO_01",
    nomeOriginal: "Instauração de Nulidade",
    tipo: "ACONTECIMENTO",
    statusSimples: "Nulidade Instaurada",
    statusDetalhado:
      "Processo administrativo de nulidade foi instaurado contra o registro.",
    proximaEtapaNomeReferencia: "PRAZO_MANIFESTACAO_NULIDADE_01",
    prazoProximaEtapaDescricao:
      "60 dias para apresentar manifestação/contestação à nulidade.",
    tagsObservacao: ["Processo de Nulidade", "Requer Atenção"],
  },
  {
    id: "PRAZO_MANIFESTACAO_NULIDADE_01",
    nomeOriginal: "Prazo para manifestação",
    tipo: "ACAO_NECESSARIA",
    statusSimples: "Prazo Manifestação Nulidade",
    statusDetalhado:
      "Aguardando manifestação do titular sobre o processo de nulidade instaurado.",
    proximaEtapaNomeReferencia: undefined,
    prazoProximaEtapaDescricao: "Aguardando decisão do INPI sobre a nulidade.",
    tagsObservacao: ["Processo de Nulidade", "Requer Atenção Imediata"],
  },
  {
    id: "MANIFESTACAO_NULIDADE_APRESENTADA_01",
    nomeOriginal: "Manifestação sobre Nulidade Apresentada",
    tipo: "ACONTECIMENTO",
    statusSimples: "Manifestação Nulidade Apresentada",
    statusDetalhado:
      "Manifestação sobre o processo de nulidade foi apresentada. Aguardando decisão do INPI.",
    proximaEtapaNomeReferencia: undefined,
    prazoProximaEtapaDescricao: "Aguardando decisão do INPI sobre a nulidade.",
    tagsObservacao: ["Processo de Nulidade", "Manifestação Realizada"],
  },
  {
    id: "CONCESSAO_MANTIDA_POS_NULIDADE_01",
    nomeOriginal: "Concessão Mantida",
    tipo: "ACONTECIMENTO",
    statusSimples: "Concessão Mantida",
    statusDetalhado:
      "Decisão do INPI manteve a concessão do registro após análise do processo de nulidade.",
    proximaEtapaNomeReferencia: "RENOVACAO_01",
    prazoProximaEtapaDescricao: "Próxima etapa é a renovação do registro.",
    tagsObservacao: ["Processo de Nulidade Resolvido"],
  },
  {
    id: "REGISTRO_ANULADO_01",
    nomeOriginal: "Registro Anulado",
    tipo: "ACONTECIMENTO", // Pode levar a um estado final ou a novo recurso
    statusSimples: "Registro Anulado",
    statusDetalhado: "Registro da marca foi anulado pelo INPI.",
    proximaEtapaNomeReferencia: "PRAZO_MANIFESTACAO_NULIDADE_01", // Para o "segundo recurso igual"
    prazoProximaEtapaDescricao:
      "Prazo para novo recurso/manifestação contra anulação: 60 dias.",
    tagsObservacao: ["Processo de Nulidade", "Registro em Risco"],
  },
  {
    id: "RECURSO_NULIDADE_NEGADO_01", // Se o recurso contra anulação falhar
    nomeOriginal: "Recurso de Nulidade Negado",
    tipo: "ACONTECIMENTO",
    statusSimples: "Recurso Nulidade Negado",
    statusDetalhado:
      "Recurso contra a anulação do registro foi negado. Anulação mantida.",
    proximaEtapaNomeReferencia: "ARQUIVAMENTO_01", // Ou uma etapa específica de arquivamento pós-nulidade
    prazoProximaEtapaDescricao:
      "Estimativa de 30 dias para despacho de arquivamento.",
    tagsObservacao: ["Processo de Nulidade", "Anulação Confirmada"],
  },
  {
    id: "RENOVACAO_01",
    nomeOriginal: "Renovação",
    tipo: "PRAZO", // É um prazo futuro importante
    statusSimples: "Aguardando Renovação",
    statusDetalhado: "Registro concedido, aguardando o período de renovação.",
    // proximaEtapaNomeReferencia: ainda não definido, pode ser um ciclo de vida do registro
    prazoProximaEtapaDescricao:
      "A renovação ocorre a cada 10 anos a partir da data de concessão.",
    tagsObservacao: ["Vigência"],
  },
  // --- Nova Etapa para Desistência Homologada ---
  {
    id: "DESISTENCIA_HOMOLOGADA_01",
    nomeOriginal: "Desistência Homologada",
    tipo: "ACONTECIMENTO",
    statusSimples: "Desistência Homologada",
    statusDetalhado:
      "Pedido de desistência total do registro de marca foi homologado pelo INPI.",
    proximaEtapaNomeReferencia: "ARQUIVAMENTO_01", // Leva diretamente ao arquivamento
    prazoProximaEtapaDescricao: "Imediato", // O arquivamento é uma consequência direta
  },
  // --- Novas Etapas para Exigência em Petição Pós-Concessão ---
  {
    id: "EXIGENCIA_PETICAO_CUMPRIDA_01",
    nomeOriginal: "Exigência",
    tipo: "ACONTECIMENTO",
    statusSimples: "Exigência Cumprida",
    statusDetalhado:
      "A exigência em petição (feita após a concessão) foi cumprida.",
    proximaEtapaNomeReferencia: undefined, // Não altera o fluxo principal da concessão (nulidade/renovação)
    prazoProximaEtapaDescricao: "N/A",
    tagsObservacao: ["Pós-Concessão", "Exigência Resolvida"],
  },
  {
    id: "EXIGENCIA_PETICAO_NAO_CUMPRIDA_01",
    nomeOriginal: "Exigência em Petição Não Cumprida (Pós-Concessão)",
    tipo: "ACONTECIMENTO",
    statusSimples: "Exigência Não Cumprida",
    statusDetalhado:
      "A exigência em petição (feita após a concessão) não foi cumprida.",
    proximaEtapaNomeReferencia: undefined, // O impacto disso no registro precisa ser avaliado
    prazoProximaEtapaDescricao: "N/A",
    tagsObservacao: [
      "Pós-Concessão",
      "Exigência Pendente",
      "Risco ao Registro",
    ],
  },
  // --- Novas Etapas para Renovações Concluídas ---
  {
    id: "RENOVACAO_CONCLUIDA_ORDINARIA_01", // criar etapas para renovações subsequentes (prazo ordinário e extraordinário)
    nomeOriginal: "1ª Renovação",
    tipo: "ACONTECIMENTO",
    statusSimples: "Renovação Paga (Ordinário)",
    statusDetalhado: "Prorrogação do registro paga em prazo ordinário.",
    proximaEtapaNomeReferencia: "RENOVACAO_01", // A próxima é uma nova janela de renovação
    prazoProximaEtapaDescricao: "10 anos a partir da data desta renovação.",
    tagsObservacao: ["Manutenção do Registro", "Renovado"],
  },
  {
    id: "RENOVACAO_CONCLUIDA_EXTRAORDINARIA_01",
    nomeOriginal: "1ª Renovação",
    tipo: "ACONTECIMENTO",
    statusSimples: "Renovação Paga (Extraordinário)",
    statusDetalhado: "Prorrogação do registro paga em prazo extraordinário.",
    proximaEtapaNomeReferencia: "RENOVACAO_01", // A próxima é uma nova janela de renovação
    prazoProximaEtapaDescricao: "10 anos a partir da data desta renovação.",
    tagsObservacao: ["Manutenção do Registro", "Renovado"],
  },
  // Fallback para lógica em desenvolvimento
  {
    id: "LOGICA_EM_DESENVOLVIMENTO_01",
    nomeOriginal: "Em Andamento",
    tipo: "ESTADO_GERAL",
    statusSimples: "Em Andamento",
    statusDetalhado:
      "A lógica para determinar a etapa atual deste processo ainda está em desenvolvimento ou o estado do processo é inesperado.",
  },
  // ... Continuaremos adicionando mais etapas aqui baseadas no seu .md
];
