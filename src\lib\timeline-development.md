# Desenvolvimento da Timeline de Processos de Marca

Este documento serve como referência para o desenvolvimento da timeline simplificada de processos de registro de marca.

## Estrutura Básica

O arquivo `timelineConfigSimplificado.ts` contém:

1. **Tipos e Interfaces**:
   - `EventoProcesso`: Enum de eventos possíveis (deposito, publicacao, etc.)
   - `InfoEvento`: Dados de um evento (ocorreu, data, despachos)
   - `EtapaTimeline`: Representação de uma etapa na timeline
   - `RegraTimeline`: Definição de uma regra para determinar etapas

2. **Funções Principais**:
   - `extrairEventosProcesso`: Extrai todos os eventos de um processo
   - `gerarTimeline`: Gera a timeline completa de um processo
   
3. **Array `regrasTimeline`**: Contém todas as definições de etapas

## Customizações Implementadas

1. **Status Simplificado e Detalhado**:
   - Adicionados campos `statusSimples` e `statusDetalhado` à interface `EtapaTimeline`
   - Implementada função `gerarStatus` na interface `RegraTimeline`
   - Função `gerarTimeline` atualizada para incluir os status nas etapas

2. **Detecção de Eventos por Diferentes Critérios**:
   - Adicionadas interfaces para `Despacho` e `ProtocoloDespacho` alinhadas com o schema do banco
   - Funções para detectar eventos por:
     - Nome do despacho (`ocorreuDespacho`)
     - Código do despacho (`ocorreuDespachoCodigo`)
     - Código de serviço no protocolo (`temProtocoloComCodigoServico`)
   - Melhorias para detecção de datas:
     - Data por nome de despacho (`dataMaisRecenteDespacho`)
     - Data por código de serviço (`dataMaisRecenteProtocoloComCodigoServico`)
   - Combinação de múltiplos critérios para eventos (ex: oposição detectada por despacho ou por protocolo)

## Etapas Implementadas

1. **Protocolo** (`PROTOCOLO`)
   - Quando: Existe depósito mas não publicação
   - Próxima etapa: Publicação (estimativa de 23 dias, ajustada para terças-feiras)

2. **Publicação** (`PUBLICACAO_PEDIDO`) - [ATUALIZADO]
   - Quando: Existe despacho de publicação (qualquer um dos seguintes):
     - "Publicação de pedido de registro para oposição (exame formal concluído)"
     - "Republicação de pedido (por perda da prioridade)"
     - "Republicação de pedido"
     - "Publicação de pedido de registro para oposição (exame formal de designação concluído)"
   - Status:
     - Simples: "Publicado" ou "Publicado (com oposição)" se houver protocolo com código 332
     - Detalhado: "Publicado, aguardando o fim do prazo de oposição."
   - Próxima etapa: Prazo para oposição (60 dias)

3. **Prazo para Oposição** (`PRAZO_OPOSICAO`)
   - Quando: Após publicação, dentro do prazo de 60 dias
   - Próximas etapas possíveis:
     - Se houver oposição: Manifestação (60 dias após publicação da oposição)
     - Se não houver oposição: Análise de mérito sem oposição

4. **Análise de Mérito (Sem Oposição)** (`ANALISE_MERITO_SEM_OPOSICAO`)
   - Quando: Passou o prazo de oposição e não houve oposição
   - Próximas etapas possíveis: Deferimento ou Indeferimento

5. **Deferimento** (`DEFERIMENTO`)
   - Quando: Existe despacho de deferimento
   - Próxima etapa: Taxa de Concessão (Prazo ordinário) - 60 dias

## Próximas Etapas a Implementar

1. **Manifestação à Oposição** (`MANIFESTACAO_OPOSICAO`)
2. **Taxa de Concessão (Prazo ordinário)** (`PRAZO_TAXA_CONCESSAO_ORDINARIO`)
3. **Taxa de Concessão (Prazo extraordinário)** (`PRAZO_TAXA_CONCESSAO_EXTRAORDINARIO`)
4. **Concessão** (`CONCESSAO`)
5. **Prazo para nulidade** (`PRAZO_NULIDADE`)
6. **Indeferimento** (`INDEFERIMENTO`)
7. **Recurso** (`RECURSO`)
8. **Análise do recurso** (`ANALISE_RECURSO`)
9. **Recurso Aceito/Negado** (`RECURSO_ACEITO`/`RECURSO_NEGADO`)
10. **Renovação** (`RENOVACAO_PROCESSO`)
11. **Sobrestamento** (`SOBRESTAMENTO`) 
12. **Caducidade** (`CADUCIDADE`)

## Customizações Pendentes

1. **Marcação de Ação Necessária**: Destacar etapas que exigem ação do cliente
2. **Cores Indicativas**: Associar cores às etapas para visualização
3. **Prioridades de Exibição**: Definir importância relativa das etapas
4. **Etapas Agrupáveis**: Indicar quais etapas podem ser agrupadas na visualização 