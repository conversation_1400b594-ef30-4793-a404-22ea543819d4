import React from 'react';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';

// Reutiliza a interface Despacho (ou importa se estiver em tipos compartilhados)
// Interface RPI e DetalhesDespacho também são necessárias implicitamente
interface RPI {
  dataPublicacao: string;
  numero: string;
}

interface DetalhesDespacho {
  nome: string | null;
}

interface Despacho {
  codigo: string;
  nome: string | null;
  DetalhesDespacho: DetalhesDespacho | null;
  RPI: RPI | null;
  // Adicionando um campo genérico para complemento, ajuste se necessário
  textoComplementar?: string | null; 
}

// Adiciona a função auxiliar para links (igual a de Citacoes)
const linkifyProcessNumbers = (text: string): React.ReactNode[] => {
  if (!text) return [text];
  // Ajusta a regex para pegar exatamente 9 dígitos usando word boundaries (\b)
  const processNumberRegex = /\b(\d{9})\b/g;
  const parts: React.ReactNode[] = [];
  let lastIndex = 0;
  let match;

  while ((match = processNumberRegex.exec(text)) !== null) {
    const index = match.index;
    const number = match[0];
    if (index > lastIndex) {
      parts.push(text.substring(lastIndex, index));
    }
    parts.push(
      <a
        key={`${number}-${index}`}
        href={`/processo/${number}`}
        target="_blank"
        rel="noopener noreferrer"
        // Remove text-blue-600, usa text-current e mantém hover:underline
        className="text-current underline font-extrabold hover:text-blue-600"
      >
        {number}
      </a>
    );
    lastIndex = index + number.length;
  }
  if (lastIndex < text.length) {
    parts.push(text.substring(lastIndex));
  }
  return parts.length > 0 ? parts : [text];
};

interface ProcessoDetalhesDespachosProps {
  despachos: Despacho[] | null | undefined;
}

// Helper para formatar data (pode ser movido para utils)
const formatDate = (dateString: string | null | undefined): string => {
  if (!dateString) return '--';
  try {
    return format(new Date(dateString), 'dd/MM/yyyy', { locale: ptBR });
  } catch (e) {
    return 'Inválida';
  }
};

const ProcessoDetalhesDespachos: React.FC<ProcessoDetalhesDespachosProps> = ({ despachos }) => {
  if (!despachos || despachos.length === 0) {
    return (
      <div className="mb-6">
        <h4 className="text-base font-semibold mb-2 uppercase text-center text-white py-1" style={{ backgroundColor: 'var(--color-headerTabela)' }}>Despachos</h4>
        <p className="text-sm text-gray-600 italic text-center py-4 bg-gray-50">Nenhum despacho encontrado.</p>
      </div>
    );
  }

  // Ordenar despachos: mais recentes primeiro
  const despachosOrdenados = [...despachos].sort((a, b) => {
    const dataA = a.RPI?.dataPublicacao ? new Date(a.RPI.dataPublicacao).getTime() : 0;
    const dataB = b.RPI?.dataPublicacao ? new Date(b.RPI.dataPublicacao).getTime() : 0;
    return dataB - dataA; // Ordem decrescente
  });

  // Determina o texto do complemento
  const getComplementoText = (despacho: Despacho): string => {
    // Prioriza DetalhesDespacho.nome se existir
    if (despacho.DetalhesDespacho?.nome) {
        return despacho.DetalhesDespacho.nome;
    }
    // Usa um campo complemento genérico se existir
    if (despacho.textoComplementar) {
        return despacho.textoComplementar;
    }
    // Fallback baseado no nome (ajuste regras conforme necessidade)
    if (despacho.nome?.toLowerCase().includes('sem direito')) {
        return 'Sem direito ao uso exclusivo dos elementos nominativos.';
    }
    return 'Sem complemento';
  };

  return (
    <div className="mb-6 overflow-x-auto">
      {" "}
      {/* Permite scroll horizontal se necessário */}
      <h4
        className="text-base font-semibold mb-0 uppercase text-center text-white py-1"
        style={{ backgroundColor: "var(--color-headerTabela)" }}
      >
        Despachos
      </h4>
      <table
        className="min-w-full text-sm"
        style={{ borderBottom: "2px solid var(--color-bordaTabela)" }}
      >
        <thead
          className="text-gray-700"
          style={{ backgroundColor: "var(--color-linhaTabela)" }}
        >
          <tr>
            <th className="px-3 py-2 text-center font-semibold tracking-wider w-[10%] text-black text-xs">
              RPI
            </th>
            <th className="px-3 py-2 text-center font-semibold tracking-wider w-[15%] text-black text-xs">
              Data
            </th>
            <th className="px-3 py-2 text-left font-semibold tracking-wider w-[10%] text-black text-xs">
              Despacho
            </th>
            <th className="px-3 py-2 text-left font-semibold tracking-wider w-[30%] text-black text-xs">
              Descrição
            </th>
            <th className="px-3 py-2 text-left font-semibold tracking-wider w-[35%] text-black text-xs">
              Complemento
            </th>
          </tr>
        </thead>
        <tbody>
          {despachosOrdenados.map((despacho, index) => (
            <tr
              key={`${despacho.RPI?.numero}-${despacho.codigo}-${index}`}
              style={{
                backgroundColor:
                  index % 2 === 0 ? "white" : "var(--color-linhaTabela)",
              }}
            >
              <td className="px-3 py-2 whitespace-nowrap text-black font-semibold text-xs text-center">
                {despacho.RPI?.numero || "--"}
              </td>
              <td className="px-3 py-2 whitespace-nowrap text-black font-semibold text-xs text-center">
                {formatDate(despacho.RPI?.dataPublicacao)}
              </td>
              <td className="px-3 py-2 whitespace-nowrap text-black font-semibold text-xs text-left">
                {despacho.codigo || "--"}
              </td>
              <td className="px-3 py-2 text-black font-semibold text-xs text-left">{despacho.nome || "--"}</td>
              {/* Usa linkifyProcessNumbers para o complemento */}
              <td className="px-3 py-2 text-black font-semibold text-xs text-left whitespace-normal">
                {linkifyProcessNumbers(getComplementoText(despacho))}
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default ProcessoDetalhesDespachos; 