/**
 * Sistema de Sessão Persistente
 * Combina LocalStorage + Cookies para máxima persistência
 */

export interface ClienteSession {
  clienteId: number;
  identificador: string;
  nome: string;
  email?: string;
  telefone?: string;
  loginAt: string;
  expiresAt: string;
  autoLogin: boolean;
}

const SESSION_KEY = 'rgse_cliente_session';
const FALLBACK_KEY = 'rgse_session_backup';

/**
 * Salva a sessão em LocalStorage + Cookie
 */
export function saveClienteSession(
  clienteData: Omit<ClienteSession, 'loginAt' | 'expiresAt' | 'autoLogin'>
): void {
  try {
    const now = new Date();
    const expiresAt = new Date(now.getTime() + (365 * 24 * 60 * 60 * 1000)); // 1 ano
    
    const session: ClienteSession = {
      ...clienteData,
      loginAt: now.toISOString(),
      expiresAt: expiresAt.toISOString(),
      autoLogin: true
    };

    // Salvar no localStorage (principal)
    localStorage.setItem(SESSION_KEY, JSON.stringify(session));
    
    // Salvar backup no localStorage com chave diferente
    localStorage.setItem(FALLBACK_KEY, JSON.stringify({
      ...session,
      savedAt: now.toISOString()
    }));

    // Salvar identificador no localStorage simples (para casos extremos)
    localStorage.setItem('rgse_last_identificador', clienteData.identificador);
    localStorage.setItem('rgse_last_cliente_id', clienteData.clienteId.toString());

    console.log('✅ Sessão persistente salva:', {
      clienteId: session.clienteId,
      identificador: session.identificador,
      expiresAt: session.expiresAt
    });

  } catch (error) {
    console.error('❌ Erro ao salvar sessão persistente:', error);
  }
}

/**
 * Recupera a sessão do LocalStorage
 */
export function getClienteSession(): ClienteSession | null {
  try {
    // Tentar recuperar da sessão principal
    const sessionData = localStorage.getItem(SESSION_KEY);
    if (sessionData) {
      const session: ClienteSession = JSON.parse(sessionData);
      
      // Verificar se não expirou
      if (new Date(session.expiresAt) > new Date()) {
        console.log('✅ Sessão persistente recuperada:', {
          clienteId: session.clienteId,
          identificador: session.identificador,
          loginAt: session.loginAt
        });
        return session;
      } else {
        console.log('⚠️ Sessão principal expirada, tentando backup...');
      }
    }

    // Tentar recuperar do backup
    const backupData = localStorage.getItem(FALLBACK_KEY);
    if (backupData) {
      const backup = JSON.parse(backupData);
      if (new Date(backup.expiresAt) > new Date()) {
        console.log('✅ Sessão backup recuperada');
        // Restaurar como sessão principal
        saveClienteSession(backup);
        return backup;
      }
    }

    console.log('ℹ️ Nenhuma sessão persistente válida encontrada');
    return null;

  } catch (error) {
    console.error('❌ Erro ao recuperar sessão persistente:', error);
    return null;
  }
}

/**
 * Atualiza a sessão existente (renovar expiração)
 */
export function refreshClienteSession(): boolean {
  try {
    const currentSession = getClienteSession();
    if (!currentSession) {
      return false;
    }

    // Renovar expiração
    const now = new Date();
    const newExpiresAt = new Date(now.getTime() + (365 * 24 * 60 * 60 * 1000));
    
    const updatedSession: ClienteSession = {
      ...currentSession,
      expiresAt: newExpiresAt.toISOString()
    };

    localStorage.setItem(SESSION_KEY, JSON.stringify(updatedSession));
    localStorage.setItem(FALLBACK_KEY, JSON.stringify({
      ...updatedSession,
      savedAt: now.toISOString()
    }));

    console.log('🔄 Sessão persistente renovada');
    return true;

  } catch (error) {
    console.error('❌ Erro ao renovar sessão:', error);
    return false;
  }
}

/**
 * Remove a sessão persistente (logout explícito)
 */
export function clearClienteSession(): void {
  try {
    localStorage.removeItem(SESSION_KEY);
    localStorage.removeItem(FALLBACK_KEY);
    localStorage.removeItem('rgse_last_identificador');
    localStorage.removeItem('rgse_last_cliente_id');
    
    console.log('🗑️ Sessão persistente removida');
  } catch (error) {
    console.error('❌ Erro ao limpar sessão:', error);
  }
}

/**
 * Verifica se há sessão ativa e válida
 */
export function hasValidSession(): boolean {
  const session = getClienteSession();
  return session !== null;
}

/**
 * Obtém dados básicos da sessão para uso rápido
 */
export function getSessionInfo(): { clienteId: number; identificador: string } | null {
  try {
    // Método rápido - buscar do localStorage simples primeiro
    const clienteId = localStorage.getItem('rgse_last_cliente_id');
    const identificador = localStorage.getItem('rgse_last_identificador');
    
    if (clienteId && identificador) {
      return {
        clienteId: parseInt(clienteId),
        identificador
      };
    }

    // Fallback para sessão completa
    const session = getClienteSession();
    return session ? {
      clienteId: session.clienteId,
      identificador: session.identificador
    } : null;

  } catch {
    return null;
  }
}

/**
 * Verifica se deve fazer auto-login
 */
export function shouldAutoLogin(): boolean {
  try {
    const session = getClienteSession();
    return session?.autoLogin === true;
  } catch {
    return false;
  }
} 