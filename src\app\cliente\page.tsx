'use client';
import { useState, useEffect, useCallback, useRef } from 'react';
import { useRouter } from 'next/navigation';
import ProcessoCard from '@/components/ProcessoCard';
import { addDays } from 'date-fns';
import { useEstimativasMerito } from '@/hooks/useEstimativasMerito';
import { clearClienteSession } from '@/lib/persistentSession';
import ClienteHeader from '@/components/ClienteHeader/ClienteHeader';

// Interfaces
interface ContatoCliente {
  email: string | null;
  telefone: string | null;
  endereco: string | null;
}

interface Cliente {
  id: number;
  nome: string | null;
  identificador: string | null;
  numeroDocumento: string | null;
  ContatoCliente: ContatoCliente[];
}

interface Processo {
  id: string;
  numero: string;
  dataDeposito: string | null;
  dataPublicacaoRPI: string | null;
  dataMeritoEstimada: string | null;
  dataOposicao: string | null;
  dataConcessao: string | null;
  dataVigencia: string | null;
  Marca: {
    nome: string | null;
    apresentacao: string | null;
    natureza: string | null;
    NCL: {
      codigo: string | null;
      especificacao: string | null;
    }[];
  } | null;
  Despacho: {
    codigo: string;
    nome: string | null;
    RPI: RPI | null;
    DetalhesDespacho: {
      nome: string | null;
    } | null;
    ProtocoloDespacho?: {
      codigoServico?: string;
    }[];
  }[];
  oposicao: boolean;
  logoUrl: string | null;
  Cliente?: { crmStageId?: number };
  taxaConcessaoPaga?: boolean;
}

// Interface RPI para uso interno (se não for importada)
interface RPI {
  dataPublicacao: string;
  numero: string;
}

interface PaginationInfo {
  currentPage: number;
  totalPages: number;
  totalProcessos: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  pageSize: number;
}

// Função para obter a saudação baseada no horário
const obterSaudacao = () => {
  const hora = new Date().getHours();
  
  if (hora >= 5 && hora < 12) {
    return "Bom dia";
  } else if (hora >= 12 && hora < 18) {
    return "Boa tarde";
  } else {
    return "Boa noite";
  }
};

export default function Cliente() {
  const router = useRouter();
  
  // Estados
  const [cliente, setCliente] = useState<Cliente | null>(null);
  const [processos, setProcessos] = useState<Processo[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [loadingMore, setLoadingMore] = useState<boolean>(false);
  const [erro, setErro] = useState<string>('');
  const [paginationInfo, setPaginationInfo] = useState<PaginationInfo | null>(null);
  const [saudacao, setSaudacao] = useState<string>("");
  
  // Ref para o observer
  const observerRef = useRef<IntersectionObserver | null>(null);
  const loadMoreRef = useRef<HTMLDivElement | null>(null);

  // Hook para buscar estimativas de mérito
  const { estimativas: estimativasMerito, loading: loadingEstimativas } = useEstimativasMerito();

  // Função para ordenar processos por prioridade
  const sortProcessosByPriority = useCallback((processos: Processo[]) => {
    return processos.sort((a: Processo, b: Processo) => {
        const getPrioridade = (processo: Processo) => {
          const hoje = new Date();
          const dataDeposito = processo.dataDeposito ? new Date(processo.dataDeposito) : null;
          const despachoConcessao = processo.Despacho.find(despacho => despacho.nome?.toLowerCase().includes("concessão de registro"));
          const dataConcessao = processo.dataConcessao ? new Date(processo.dataConcessao) : (despachoConcessao?.RPI?.dataPublicacao ? new Date(despachoConcessao.RPI.dataPublicacao) : null);
          const temConcessaoRegistro = processo.Despacho.some(despacho => despacho.nome?.toLowerCase().includes("concessão de registro"));
          const temNulidadeAposConcessao = dataConcessao ? processo.Despacho.some(despacho => { if (despacho.nome?.toLowerCase().includes("requerimento provido (nulo o registro)") && despacho.RPI?.dataPublicacao) { const dataDespacho = new Date(despacho.RPI.dataPublicacao); return dataDespacho > dataConcessao; } return false; }) : false;
          const registroEmVigor = temConcessaoRegistro && dataConcessao && !temNulidadeAposConcessao;
          const dataVigencia = processo.dataVigencia ? new Date(processo.dataVigencia) : null;
          const registroEmVigorGenerico = !registroEmVigor && dataDeposito && dataConcessao && dataVigencia && dataVigencia > hoje;

          const textoArquivamento = "arquivamento definitivo de pedido de registro";
          const temArquivamentoDefinitivo = processo.Despacho.some(despacho => despacho.nome?.toLowerCase().includes(textoArquivamento));

          const textosDeferimento = ["deferimento do pedido", "registrado"];
          const despachoDeferimentoBase = processo.Despacho.find(d => textosDeferimento.some(t => d.nome?.toLowerCase().includes(t)));
          const despachoRecursoProvidoDef = processo.Despacho.find(d => d.nome?.toLowerCase().includes("recurso provido (decisão reformada para: deferimento)"));
          const temDeferimento = !!despachoDeferimentoBase || !!despachoRecursoProvidoDef;
          const temConcessaoOuArquivamento = processo.Despacho.some(despacho => despacho.nome?.toLowerCase().includes("concessão") || despacho.nome?.toLowerCase().includes(textoArquivamento));
          const temDeferimentoSemConcessao = !temArquivamentoDefinitivo && temDeferimento && !temConcessaoOuArquivamento;

          const textosIndeferimento = ["indeferimento do pedido"];
          const despachoIndeferimentoBase = processo.Despacho.find(d => 
            textosIndeferimento.some(t => d.nome?.toLowerCase().includes(t)) || 
            (d.codigo === "IPAS024" && d.nome?.toLowerCase().includes("indeferimento"))
          );
          const temIndeferimentoBase = !!despachoIndeferimentoBase;
          const temIndeferimento = !temArquivamentoDefinitivo && temIndeferimentoBase && !despachoRecursoProvidoDef;

          const textoDespachoPublicacao = "publicação de pedido de registro para oposição";
          const temDespachoPublicacao = processo.Despacho.some(despacho => despacho.nome?.toLowerCase().includes(textoDespachoPublicacao));
          const temDespachoMerito = processo.Despacho.some(despacho => { const nomeDespacho = despacho.nome?.toLowerCase() || ''; const ehDeferimento = textosDeferimento.some(texto => nomeDespacho.includes(texto)); const ehIndeferimento = textosIndeferimento.some(texto => nomeDespacho.includes(texto)); return ehDeferimento || ehIndeferimento; });
          const estaEmFasePublicacao = !temArquivamentoDefinitivo && !temIndeferimento && !temDeferimentoSemConcessao && temDespachoPublicacao && !temDespachoMerito;

          const dataDeferimento = despachoRecursoProvidoDef?.RPI?.dataPublicacao
            ? new Date(despachoRecursoProvidoDef.RPI.dataPublicacao)
            : (despachoDeferimentoBase?.RPI?.dataPublicacao ? new Date(despachoDeferimentoBase.RPI.dataPublicacao) : null);
          const dataPrazoOrdinario = dataDeferimento ? addDays(dataDeferimento, 60) : null;
          const dataPrazoExtraordinario = dataPrazoOrdinario ? addDays(dataPrazoOrdinario, 30) : null;
          const taxaConcessaoPaga = processo.Cliente?.crmStageId === 280246 || processo.taxaConcessaoPaga === true;
          const taxaNaoPagaAposPrazo = temDeferimentoSemConcessao && dataPrazoExtraordinario && hoje >= dataPrazoExtraordinario && !taxaConcessaoPaga;
          
          // Nova prioridade máxima para Indeferimento após Publicação (e não resolvido/arquivado)
          const indeferidoAposPublicacao = temIndeferimentoBase && temDespachoPublicacao && !despachoRecursoProvidoDef && !temArquivamentoDefinitivo;
          if (indeferidoAposPublicacao) return 2;
          
          // Ajustar as prioridades subsequentes
          if (estaEmFasePublicacao) return 1; // Era 1
          if (temDeferimentoSemConcessao && !taxaNaoPagaAposPrazo) return 3; // Era 2
          if (registroEmVigor || registroEmVigorGenerico) return 4; // Era 3
          if (temIndeferimento) return 5; // Era 4 (Agora só pega os indeferimentos que não se encaixam na prioridade 1)
          if (taxaNaoPagaAposPrazo || temArquivamentoDefinitivo) return 6; // Era 5
          return 7; // Era 6
        };

        return getPrioridade(a) - getPrioridade(b);
      });
  }, []);

  // Novo useEffect para buscar dados do cliente pela API /me
  useEffect(() => {
    const fetchClienteData = async () => {
      setLoading(true);
      setErro('');
      try {
        const response = await fetch('/api/cliente/me');
        if (!response.ok) {
          // Se não autorizado (401) ou outro erro, redireciona para login
          if (response.status === 401) {
             console.error('Não autorizado. Redirecionando para login.');
             router.push('/'); // Redireciona se não autenticado
             return;
          }
          throw new Error('Falha ao buscar dados do cliente');
        }
        
        const data = await response.json();
        const clienteData = data.cliente as Cliente; // Faz o cast para o tipo Cliente
        
        if (clienteData && clienteData.identificador) {
            setCliente(clienteData);
            // Busca a primeira página de processos APÓS obter os dados do cliente
            fetchProcessos(clienteData.identificador, 1, true);
        } else {
            throw new Error('Dados do cliente inválidos recebidos da API');
        }

      } catch (error: any) {
        console.error('Erro ao buscar dados do cliente via API /me:', error);
        setErro(error.message || 'Erro ao carregar dados do cliente. Tente logar novamente.');
        setLoading(false); // Para de carregar em caso de erro
      }
    };

    fetchClienteData();
    
  }, [router]); // A dependência é apenas o router agora
  
  // Função para buscar os processos do cliente
  const fetchProcessos = async (identificador: string, page: number = 1, isFirstLoad: boolean = false) => {
    try {
      if (!isFirstLoad) {
        setLoadingMore(true);
      }

      const response = await fetch(`/api/cliente/processos?identificador=${identificador}&page=${page}`);
      
      if (!response.ok) {
        throw new Error('Erro ao buscar processos');
      }
      
      const data = await response.json();
      
      // Atualizar informações de paginação
      setPaginationInfo({
        currentPage: data.currentPage,
        totalPages: data.totalPages,
        totalProcessos: data.totalProcessos,
        hasNextPage: data.hasNextPage,
        hasPreviousPage: data.hasPreviousPage,
        pageSize: data.pageSize,
      });

      if (isFirstLoad) {
        // Primeira carga: substituir todos os processos
        const sortedProcessos = sortProcessosByPriority(data.processos || []);
        setProcessos(sortedProcessos);
      } else {
        // Carregar mais: adicionar aos processos existentes
        setProcessos(prevProcessos => {
          const allProcessos = [...prevProcessos, ...(data.processos || [])];
          return sortProcessosByPriority(allProcessos);
        });
      }
      
    } catch (error) {
      console.error('Erro ao buscar processos:', error);
      if (isFirstLoad) {
      setErro('Não foi possível carregar seus processos. Tente novamente mais tarde.');
      }
    } finally {
      if (isFirstLoad) {
      setLoading(false);
      } else {
        setLoadingMore(false);
      }
    }
  };

  // Função para carregar mais processos
  const loadMoreProcessos = useCallback(() => {
    if (cliente && paginationInfo && paginationInfo.hasNextPage && !loadingMore) {
      fetchProcessos(cliente.identificador!, paginationInfo.currentPage + 1, false);
    }
  }, [cliente, paginationInfo, loadingMore]);

  // Configurar intersection observer para infinite scroll
  useEffect(() => {
    if (observerRef.current) {
      observerRef.current.disconnect();
    }

    observerRef.current = new IntersectionObserver(
      (entries) => {
        const target = entries[0];
        if (target.isIntersecting) {
          loadMoreProcessos();
        }
      },
      {
        threshold: 0.1,
        rootMargin: '100px',
      }
    );

    if (loadMoreRef.current) {
      observerRef.current.observe(loadMoreRef.current);
    }

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [loadMoreProcessos]);

  const handleLogout = async () => {
    try {
      // ✅ NOVO: Limpar sessão persistente
      clearClienteSession();
      
      // Chamar API de logout para limpar cookie do servidor
      await fetch('/api/auth/logout', {
        method: 'POST',
      });
      
      console.log('🚪 Logout realizado - sessão persistente removida');
      
      // Redirecionar para login
      router.push('/');
      
    } catch (error) {
      console.error('Erro no logout:', error);
      // Mesmo com erro na API, limpar localStorage e redirecionar
    router.push('/');
    }
  };

  // Atualizar a saudação a cada minuto
  useEffect(() => {
    const atualizarSaudacao = () => {
      setSaudacao(obterSaudacao());
    };

    // Atualizar inicialmente
    atualizarSaudacao();

    // Atualizar a cada minuto
    const intervalo = setInterval(atualizarSaudacao, 60000);

    return () => clearInterval(intervalo);
  }, []);

  if (loading) {
    return (
      <div className="fixed inset-0 flex items-center justify-center bg-white bg-opacity-80">
        <div className="text-center">
          <div className="mb-4">
            <svg className="animate-spin h-12 w-12 mx-auto text-green-500" viewBox="0 0 24 24">
              <circle
                className="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                strokeWidth="4"
              ></circle>
              <path
                className="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              ></path>
            </svg>
          </div>
          <p className="text-gray-800 font-medium">Carregando seus dados...</p>
        </div>
      </div>
    );
  }

  if (erro) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100 px-4">
        <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full">
          <div className="text-red-600 mb-4 text-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h2 className="text-xl font-bold text-gray-800 mb-2 text-center">Erro ao carregar dados</h2>
          <p className="text-center text-gray-600 mb-6">{erro}</p>
          <button
            onClick={() => window.location.reload()}
            className="w-full bg-green-500 hover:bg-green-600 text-white font-bold py-2 px-4 rounded-md transition duration-300 ease-in-out"
          >
            Tentar novamente
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#F5F5F5]">
      <ClienteHeader cliente={cliente} handleLogout={handleLogout} />

      {/* Conteúdo Principal */}
      <main className="max-w-7xl mx-auto px-2  sm:px-6 lg:px-8 mt-28">
        {/* Cabeçalho da página */}
        <div className="mb-8 text-center">
          <h1 className="text-3xl font-bold text-gray-800 mb-2">
            {saudacao}! Seja bem-vindo(a)
          </h1>
          <p className="text-gray-600">
            Monitore o andamento dos seus processos de registro.
          </p>
          {paginationInfo && (
            <p className="text-sm text-gray-500 mt-2">
              Mostrando {processos.length} de {paginationInfo.totalProcessos}{" "}
              processos
            </p>
          )}
        </div>

        {/* Lista de processos */}
        <div className="space-y-6">
          {processos.length > 0 ? (
            processos.map((processo) => (
              <ProcessoCard
                key={processo.id}
                processo={processo}
                estimativasMerito={estimativasMerito}
              />
            ))
          ) : (
            <div className="bg-white p-6 rounded-lg shadow-md text-center">
              <p className="text-gray-600">Nenhum processo encontrado.</p>
            </div>
          )}

          {/* Loading indicator para infinite scroll */}
          {paginationInfo?.hasNextPage && (
            <div ref={loadMoreRef} className="flex justify-center py-8">
              {loadingMore ? (
                <div className="text-center">
                  <div className="mb-2">
                    <svg
                      className="animate-spin h-8 w-8 mx-auto text-green-500"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      ></circle>
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                  </div>
                  <p className="text-gray-500 text-sm">
                    Carregando mais processos...
                  </p>
                </div>
              ) : (
                <div className="text-center">
                  <p className="text-gray-400 text-sm">
                    Role para carregar mais processos
                  </p>
                </div>
              )}
            </div>
          )}
        </div>
        <span className="hidden sm:block text-[#A3A3A3] mt-8 text-sm text-center ">
          Os prazos acima são calculados com base nas últimas publicações do
          INPI e poderão variar conforme a demanda do órgão.
        </span>
        <span className="block sm:hidden text-center font-bold text-gray-900 text-sm m-8">
          Esta é uma visualização adaptada para celular. Para ver todos os
          detalhes do(s) seu(s) processo(s), acesse esta página por um
          computador.
        </span>
      </main>
    </div>
  );
} 