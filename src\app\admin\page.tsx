'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { FiLogOut, FiUsers, FiDownload, FiCheck, FiX, FiEye, FiSearch } from 'react-icons/fi';
import { useRouter } from 'next/navigation';
import Breadcrumb from '@/components/Breadcrumb';

interface SessaoLogin {
  id: string;
  clienteId: number;
  nome: string;
  autoLoginUrl: string | null;
  identificador: string;
  loginAt: string;
  fezDownload: boolean;
}

interface LoginsData {
  totalLoginsHoje: number;
  sessões: SessaoLogin[];
}

interface AdminUser {
  id: string;
  email: string;
  nome: string;
  role: 'MASTER' | 'COMUM';
}

const AdminDashboardPage: React.FC = () => {
  const [data, setData] = useState<LoginsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [showDetails, setShowDetails] = useState(false);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);
  const [user, setUser] = useState<AdminUser | null>(null);
  const [authLoading, setAuthLoading] = useState(true);
  const router = useRouter();

  const fetchLoginsHoje = async () => {
    try {
      const response = await fetch('/api/admin/logins-hoje');
      if (response.ok) {
        const newData = await response.json();
        setData(newData);
        setLastUpdate(new Date());
      } else {
        console.error('Erro ao buscar logins de hoje:', response.status);
      }
    } catch (error) {
      console.error('Erro ao buscar logins de hoje:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // Verificar autenticação
    const checkAuth = async () => {
      try {
        const response = await fetch('/api/admin/auth/me');
        if (response.ok) {
          const userData = await response.json();
          setUser(userData.user);
        } else {
          router.push('/admin/login');
        }
      } catch (error) {
        console.error('Erro ao verificar autenticação:', error);
        router.push('/admin/login');
      } finally {
        setAuthLoading(false);
      }
    };

    checkAuth();
  }, [router]);

  useEffect(() => {
    if (!authLoading && user) {
      fetchLoginsHoje();
      
      // Atualizar dados a cada 30 segundos
      const interval = setInterval(fetchLoginsHoje, 30000);
      
      return () => clearInterval(interval);
    }
  }, [authLoading, user]);

  const handleLogout = async () => {
    try {
      await fetch('/api/admin/auth/logout', { method: 'POST' });
      router.push('/admin/login');
    } catch (error) {
      console.error('Erro no logout:', error);
    }
  };

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString('pt-BR', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (authLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Carregando...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-green-500 shadow-md">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-2 flex justify-between items-center">
          <div>
            <Image src="/logo.svg" alt="Logo Registre.se" width={150} height={40} className="h-16 w-auto" priority />
          </div>
          <button
            onClick={handleLogout}
            title="Sair"
            className="flex items-center justify-center h-10 w-10 rounded-full bg-white text-green-500 hover:bg-green-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-green-500 focus:ring-white cursor-pointer"
            aria-label="Sair"
          >
            <FiLogOut className="w-6 h-6" />
          </button>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-6 py-8">
        <Breadcrumb items={[
          { label: 'Admin' }
        ]} />
        
        {/* Header */}
        <div className="mb-8">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                Dashboard Administrativo
              </h1>
              <p className="text-gray-600">
                Monitoramento simplificado de logins diários
              </p>
            </div>
            
            {lastUpdate && (
              <div className="text-right">
                <div className="text-sm text-gray-500">Última atualização</div>
                <div className="text-sm font-medium text-gray-700">
                  {lastUpdate.toLocaleTimeString('pt-BR')}
                </div>
                <div className="flex items-center mt-1">
                  <div className="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></div>
                  <span className="text-xs text-green-600">Atualização automática ativa</span>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Card Principal - Logins de Hoje */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold text-gray-900">
                {loading ? '...' : data?.totalLoginsHoje || 0}
              </h2>
              <p className="text-gray-600">Logins únicos hoje</p>
            </div>
            <div className="flex space-x-3">
              <button
                onClick={() => setShowDetails(!showDetails)}
                className="flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
              >
                <FiEye className="w-4 h-4 mr-2" />
                {showDetails ? 'Ocultar Detalhes' : 'Ver Detalhes'}
              </button>
              <Link
                href="/admin/busca"
                className="flex items-center px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors"
              >
                <FiSearch className="w-4 h-4 mr-2" />
                Buscar Processos
              </Link>
              <Link
                href="/admin/clientes"
                className="flex items-center px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
              >
                <FiUsers className="w-4 h-4 mr-2" />
                Clientes
              </Link>
            </div>
          </div>
        </div>

        {/* Detalhes dos Logins */}
        {showDetails && data && (
          <div className="bg-white rounded-lg shadow-md">
            <div className="border-b border-gray-200 px-6 py-4">
              <h3 className="text-lg font-semibold text-gray-900">
                Detalhes dos Logins de Hoje ({data.sessões.length} sessões)
              </h3>
            </div>
            
            {loading ? (
              <div className="p-8 text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
                <p className="mt-4 text-gray-600">Carregando...</p>
              </div>
            ) : data.sessões.length === 0 ? (
              <div className="p-8 text-center text-gray-500">
                Nenhum login registrado hoje
              </div>
            ) : (
              <div className="divide-y divide-gray-200 max-h-96 overflow-y-auto">
                {data.sessões.map((sessao) => (
                  <div key={sessao.id} className="px-6 py-4 hover:bg-gray-50">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-4">
                          <div className="flex-shrink-0">
                            <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center">
                              <span className="text-sm font-medium text-blue-600">
                                {sessao.nome?.charAt(0)?.toUpperCase() || 'U'}
                              </span>
                            </div>
                          </div>
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium text-gray-900">
                              {sessao.nome || 'Nome não informado'}
                            </p>
                            <p className="text-xs text-gray-500">
                              ID: {sessao.identificador} • Login: {formatTime(sessao.loginAt)}
                            </p>
                            {sessao.autoLoginUrl && (
                              <div className="mt-1">
                                <a
                                  href={sessao.autoLoginUrl}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="text-xs text-blue-600 hover:text-blue-800 truncate block max-w-md"
                                  title={sessao.autoLoginUrl}
                                >
                                  {sessao.autoLoginUrl}
                                </a>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        {sessao.fezDownload ? (
                          <div className="flex items-center text-green-600">
                            <FiCheck className="w-4 h-4 mr-1" />
                            <span className="text-xs">Download</span>
                          </div>
                        ) : (
                          <div className="flex items-center text-gray-400">
                            <FiX className="w-4 h-4 mr-1" />
                            <span className="text-xs">Sem download</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Footer */}
        <div className="mt-12 text-center text-sm text-gray-500">
          <p>
            Sistema de Dashboard Administrativo Simplificado - RGSE
          </p>
          <p className="mt-1">
            🚀 Monitoramento em tempo real de logins diários
          </p>
        </div>
      </div>
    </div>
  );
};

export default AdminDashboardPage; 