'use client';

import { useRouter } from 'next/navigation';
import { clearClienteSession } from '@/lib/persistentSession';

interface LogoutButtonProps {
  className?: string;
  children?: React.ReactNode;
  variant?: 'button' | 'link';
}

export default function LogoutButton({ 
  className = '', 
  children = 'Sair',
  variant = 'button'
}: LogoutButtonProps) {
  const router = useRouter();

  const handleLogout = async () => {
    try {
      // Limpar sessão persistente do localStorage
      clearClienteSession();
      
      // Chamar API de logout para limpar cookie do servidor
      await fetch('/api/auth/logout', {
        method: 'POST',
      });
      
      console.log('🚪 Logout realizado - sessão persistente removida');
      
      // Redirecionar para login
      router.push('/');
      
    } catch (error) {
      console.error('Erro no logout:', error);
      // Mesmo com erro na API, limpar localStorage e redirecionar
      router.push('/');
    }
  };

  if (variant === 'link') {
    return (
      <button
        onClick={handleLogout}
        className={`text-blue-600 hover:text-blue-800 underline ${className}`}
      >
        {children}
      </button>
    );
  }

  return (
    <button
      onClick={handleLogout}
      className={`bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-md transition-colors ${className}`}
    >
      {children}
    </button>
  );
} 