import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface UserPreferencesState {
  // Termos e condições
  hasAcceptedTerms: boolean;
  acceptTerms: () => void;
  
  // Manter conectado
  keepConnected: boolean;
  setKeepConnected: (keepConnected: boolean) => void;
  
  // Identificador (últimos 8 números do telefone)
  identifier: string;
  setIdentifier: (identifier: string) => void;
  
  // Reset
  resetPreferences: () => void;
}

// Criando a store com persistência local
export const useUserPreferences = create<UserPreferencesState>()(
  persist(
    (set) => ({
      // Estado inicial
      hasAcceptedTerms: false,
      keepConnected: false,
      identifier: '',
      
      // Ações
      acceptTerms: () => set({ hasAcceptedTerms: true }),
      
      setKeepConnected: (keepConnected: boolean) => 
        set({ keepConnected }),
        
      setIdentifier: (identifier: string) => 
        set({ identifier }),
        
      resetPreferences: () => set({
        hasAcceptedTerms: false,
        keepConnected: false,
        identifier: '',
      }),
    }),
    {
      name: 'user-preferences-storage', // nome usado para o localStorage/sessionStorage
      partialize: (state) => ({
        hasAcceptedTerms: state.hasAcceptedTerms,
        keepConnected: state.keepConnected,
        identifier: state.identifier,
      }),
    }
  )
); 