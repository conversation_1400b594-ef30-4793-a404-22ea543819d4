import crypto from 'crypto';

const ALGORITHM = 'aes-256-cbc';
// Usar chave fixa para debug - mesmo valor usado no teste
const SECRET_KEY = "emriodepiranhajacarenadadecostas";

// Garante que a chave tenha 32 bytes
const KEY = crypto.createHash('sha256').update(SECRET_KEY).digest();

// Log para debug - verificar se a chave está sendo gerada igual
console.log('🔑 AutoLogin Key (hex):', KEY.toString('hex'));

export interface CredenciaisLogin {
  identificador: string;
  senha: string;
  timestamp: number; // Para expiração opcional
}

/**
 * Converte base64 para base64url (URL-safe)
 */
function base64ToBase64url(base64: string): string {
  return base64.replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');
}

/**
 * Encripta as credenciais do cliente para usar no link
 */
export function encryptCredentials(identificador: string, senha: string): string {
  try {
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipheriv(ALGORITHM, KEY, iv);
    
    const payload: CredenciaisLogin = {
      identificador,
      senha,
      timestamp: Date.now()
    };
    
    console.log('🔍 Encrypt - Payload:', payload);
    
    let encrypted = cipher.update(JSON.stringify(payload), 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    // Combina IV + dados encriptados e codifica em base64url (URL-safe)
    const combined = iv.toString('hex') + ':' + encrypted;
    console.log('🔍 Encrypt - Combined:', combined.substring(0, 50) + '...');
    
    const base64 = Buffer.from(combined).toString('base64');
    console.log('🔍 Encrypt - Base64:', base64.substring(0, 50) + '...');
    
    // Converter para base64url (URL-safe)
    const result = base64ToBase64url(base64);
    console.log('🔍 Encrypt - Base64url final:', result.substring(0, 50) + '...');
    
    return result;
    
  } catch (error) {
    console.error('Erro ao encriptar credenciais:', error);
    throw new Error('Erro na criptografia');
  }
}

/**
 * Converte base64url para base64 padrão
 */
function base64urlToBase64(base64url: string): string {
  // Substituir caracteres URL-safe
  let base64 = base64url.replace(/-/g, '+').replace(/_/g, '/');
  
  // Adicionar padding necessário
  while (base64.length % 4) {
    base64 += '=';
  }
  
  return base64;
}

/**
 * Descriptografa as credenciais do link
 */
export function decryptCredentials(encryptedData: string): CredenciaisLogin | null {
  try {
    console.log('🔍 Debug - Token original:', encryptedData.substring(0, 50) + '...');
    
    // Converter base64url para base64 padrão
    const base64 = base64urlToBase64(encryptedData);
    console.log('🔍 Debug - Base64 convertido:', base64.substring(0, 50) + '...');
    
    // Decodificar base64
    const combined = Buffer.from(base64, 'base64').toString();
    console.log('🔍 Debug - Combined data:', combined.substring(0, 50) + '...');
    
    // Separar IV e dados encriptados
    const [ivHex, encrypted] = combined.split(':');
    console.log('🔍 Debug - IV hex:', ivHex);
    console.log('🔍 Debug - Encrypted part:', encrypted?.substring(0, 30) + '...');
    
    if (!ivHex || !encrypted) {
      console.error('❌ Debug - IV ou dados encriptados ausentes');
      return null;
    }
    
    const iv = Buffer.from(ivHex, 'hex');
    const decipher = crypto.createDecipheriv(ALGORITHM, KEY, iv);
    
    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    console.log('🔍 Debug - Dados descriptografados:', decrypted);
    
    const payload: CredenciaisLogin = JSON.parse(decrypted);
    console.log('✅ Debug - Payload parseado:', payload);
    
    // Link sem expiração - sempre válido
    return payload;
    
  } catch (error) {
    console.error('❌ Debug - Erro na descriptografia:', error);
    return null;
  }
}

/**
 * Gera um link completo de auto-login
 */
export function generateAutoLoginLink(identificador: string, senha: string, baseUrl: string): string {
  const encryptedData = encryptCredentials(identificador, senha);
  return `${baseUrl}/auth/auto?token=${encryptedData}`;
}

/**
 * Função helper para gerar link a partir do ID do cliente
 */
export async function generateAutoLoginLinkFromClienteId(clienteId: number, baseUrl: string): Promise<string | null> {
  try {
    // Importa prisma apenas quando necessário para evitar problemas de edge cases
    const { prisma } = await import('@/lib/prisma');
    
    const cliente = await prisma.cliente.findUnique({
      where: { id: clienteId },
      select: { 
        identificador: true, 
        numeroDocumento: true 
      }
    });
    
    if (!cliente || !cliente.identificador || !cliente.numeroDocumento) {
      return null;
    }
    
    // Extrai os últimos 3 dígitos do documento (senha)
    const numeroLimpo = cliente.numeroDocumento.replace(/[\D]/g, '');
    const senha = numeroLimpo.slice(-3);
    
    return generateAutoLoginLink(cliente.identificador, senha, baseUrl);
    
  } catch (error) {
    console.error('Erro ao gerar link de auto-login:', error);
    return null;
  }
} 