'use client';

import React from 'react';

interface Metricas {
  usuariosAtivosHoje: number;
  sessoesAtivas: number;
  downloadsHoje: number;
  tempoMedioSessao: number; // em minutos
  totalUsuarios: number;
  totalProcessos: number;
}

interface MetricasProps {
  metricas: Metricas | null;
  loading: boolean;
}

const MetricsCards: React.FC<MetricasProps> = ({ metricas, loading }) => {
  const formatarTempo = (minutos: number): string => {
    if (minutos < 60) {
      return `${minutos}min`;
    }
    const horas = Math.floor(minutos / 60);
    const minutosRestantes = minutos % 60;
    return `${horas}h ${minutosRestantes}min`;
  };

  const cards = [
    {
      titulo: 'Usuários Ativos Hoje',
      valor: metricas?.usuariosAtivosHoje || 0,
      icone: '👥',
      cor: 'bg-blue-500',
      descricao: 'Usuários únicos que fizeram login hoje',
    },
    {
      titulo: 'Sessões Ativas',
      valor: metricas?.sessoesAtivas || 0,
      icone: '🟢',
      cor: 'bg-green-500',
      descricao: 'Usuários online agora',
      realTime: true,
    },
    {
      titulo: 'Downloads Hoje',
      valor: metricas?.downloadsHoje || 0,
      icone: '📥',
      cor: 'bg-purple-500',
      descricao: 'Protocolos baixados hoje',
    },
    {
      titulo: 'Tempo Médio de Sessão',
      valor: metricas?.tempoMedioSessao || 0,
      icone: '⏱️',
      cor: 'bg-orange-500',
      descricao: 'Baseado nas últimas 100 sessões',
      formatCustom: formatarTempo,
    },
    {
      titulo: 'Total de Usuários',
      valor: metricas?.totalUsuarios || 0,
      icone: '📊',
      cor: 'bg-indigo-500',
      descricao: 'Clientes cadastrados no sistema',
    },
    {
      titulo: 'Total de Processos',
      valor: metricas?.totalProcessos || 0,
      icone: '📋',
      cor: 'bg-teal-500',
      descricao: 'Processos no banco de dados',
    },
  ];

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {Array.from({ length: 6 }).map((_, index) => (
          <div key={index} className="bg-white p-6 rounded-lg shadow-md animate-pulse">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-gray-200 rounded-lg mr-4"></div>
              <div className="flex-1">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-full"></div>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {cards.map((card, index) => (
        <div 
          key={index}
          className="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow border-l-4 border-transparent hover:border-gray-300"
        >
          <div className="flex items-center">
            {/* Ícone */}
            <div className={`${card.cor} p-3 rounded-lg text-white text-2xl mr-4 flex-shrink-0`}>
              {card.icone}
            </div>
            
            {/* Conteúdo */}
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-600 truncate">
                {card.titulo}
                {card.realTime && (
                  <span className="ml-2 inline-block w-2 h-2 bg-green-400 rounded-full animate-pulse"></span>
                )}
              </p>
              
              <p className="text-2xl font-bold text-gray-900 mt-1">
                {card.formatCustom 
                  ? card.formatCustom(card.valor) 
                  : card.valor.toLocaleString('pt-BR')
                }
              </p>
              
              <p className="text-xs text-gray-500 mt-2 leading-tight">
                {card.descricao}
              </p>
            </div>
          </div>
          
          {/* Indicador de atualização para cards em tempo real */}
          {card.realTime && (
            <div className="mt-3 pt-3 border-t border-gray-100">
              <div className="flex items-center text-xs text-gray-500">
                <div className="w-1 h-1 bg-green-400 rounded-full mr-2 animate-pulse"></div>
                Atualizado em tempo real
              </div>
            </div>
          )}
        </div>
      ))}
    </div>
  );
};

export default MetricsCards; 