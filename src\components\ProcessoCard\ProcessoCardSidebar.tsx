import React from 'react';

interface ProcessoCardSidebarProps {
  sidebarBgColorClass: string;
  SidebarIcon: React.FC | null; // Recebe o componente do ícone
  className?: string; // Adiciona className como prop opcional
  onToggleExpand?: (e?: React.MouseEvent) => void; // Nova prop para controlar expansão
}

const ProcessoCardSidebar: React.FC<ProcessoCardSidebarProps> = ({ 
  sidebarBgColorClass, 
  SidebarIcon, 
  className,
  onToggleExpand // Nova prop
}) => {
  // Não renderiza nada se não houver ícone
  if (!SidebarIcon) {
    return null;
  }
  
  // Função para lidar com cliques na sidebar
  const handleSidebarClick = (e: React.MouseEvent) => {
    e.stopPropagation(); // Evita propagação dupla
    if (onToggleExpand) {
      onToggleExpand();
    }
  };
  
  // Simplifica a construção das classes
  const classes = [
    'min-w-[58px]',
    'h-full', // Mantém altura fixa da sidebar superior
    'flex',
    'items-center',
    'justify-center',
    'cursor-pointer', // Adiciona cursor pointer
    'hover:opacity-80', // Adiciona efeito hover
    'transition-opacity', // Transição suave
    sidebarBgColorClass,
    className || '' // Deve conter rounded-tr passado pelo pai
  ];

  const finalClassName = classes.filter(Boolean).join(' ').trim();

  return (
    <div
      className={finalClassName}
      onClick={handleSidebarClick}
      title="Expandir/Fechar detalhes"
    >
      <SidebarIcon /> {/* Renderiza o componente do ícone */}
    </div>
  );
};

export default ProcessoCardSidebar; 