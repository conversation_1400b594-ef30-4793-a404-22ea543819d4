.ribbonOposicao {
  position: absolute;
  right: -5px;
  top: -5px;
  z-index: 10;
  overflow: hidden;
  width: 90px;
  height: 90px;
  text-align: right;
  pointer-events: none;
}

.ribbonOposicao span {
  font-size: 10px;
  font-weight: 700;
  text-transform: uppercase;
  text-align: center;
  line-height: 20px;
  transform: rotate(45deg);
  width: 120px;
  display: block;
  background: linear-gradient(#7E7E7E, #606060);
  position: absolute;
  top: 25px;
  right: -25px;
  color: white;
}

.ribbonOposicao span::before {
  content: "";
  position: absolute;
  left: 0;
  top: 100%;
  z-index: -1;
  border-color: #404040 transparent transparent #404040;
  border-style: solid;
  border-width: 3px;
}

.ribbonOposicao span::after {
  content: "";
  position: absolute;
  right: 0;
  top: 100%;
  z-index: -1;
  border-color: #404040 transparent transparent transparent;
  border-style: solid;
  border-width: 3px;
}

.ribbonOposicaoDesktop span {
  background: #C2C2C2;
  color: white;
  font-weight: 500; /* Medium */
  font-size: 14px;
  text-transform: uppercase;
  text-align: center;
  line-height: 20px;
  transform: rotate(45deg);
  width: 120px;
  display: block;
  box-shadow: 0 3px 10px -5px #000;
  position: absolute;
  top: 25px;
  right: -25px;
}

.ribbonOposicaoDesktop span::before, .ribbonOposicaoDesktop span::after {
    display: none; /* Oculta as bordas triangulares para a versão desktop se não forem desejadas com a nova cor */
} 