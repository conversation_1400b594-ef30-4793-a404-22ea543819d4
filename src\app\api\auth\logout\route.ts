import { NextResponse } from 'next/server';

export async function POST() {
  try {
    // Criar resposta
    const response = NextResponse.json({
      message: 'Logout realizado com sucesso'
    });

    // Limpar cookie de autenticação
    response.cookies.set('auth_token', '', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      path: '/',
      sameSite: 'strict',
      maxAge: 0, // Expira imediatamente
    });

    console.log('🚪 Cookie de sessão removido via API');
    
    return response;
    
  } catch (error) {
    console.error('Erro no logout:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
} 