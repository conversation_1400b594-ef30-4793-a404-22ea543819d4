{"id": "ddb06225-e8a3-4253-a14a-cf19b2be97e4", "numero": "*********", "dataDeposito": "2025-04-25T13:15:43.000Z", "dataConcessao": null, "dataVigencia": null, "dataMerito": null, "diasCorridosMerito": null, "dataMeritoEstimada": "2026-11-27T13:15:43.000Z", "oposicao": false, "dataOposicao": "2025-07-26T03:00:00.000Z", "sobrestamento": false, "dataSobrestamento": null, "exigencia": false, "dataExigencia": null, "procuradorId": "65b3c0c0-aa3b-4d89-85fb-2a144e538800", "monitorado": true, "inicioVigencia": null, "prorrogacaoOrdinaria": null, "prorrogacaoExtraordinaria": null, "apostila": null, "clienteId": 4884, "marcaId": "d26d5168-f6e2-4d15-9c10-e4fc4aab2a97", "createdAt": "2025-04-25T13:15:43.790Z", "updatedAt": "2025-06-12T17:20:48.284Z", "procuradorMonitoradoId": null, "diasAteMeritoEstimada": 581, "dataElegibilidade": null, "elegivelParaComunicados": false, "taxaConcessaoPaga": false, "linkProtocolo": "https://www.dropbox.com/scl/fi/q5auo3zh0rq0skul11yeq/ECONOMIT-PROTOCOLO-CLASSE-41-*********.pdf?rlkey=fj7yc483ua9wmq3m3ffo6a295&dl=0", "dataTaxaConcessaoPaga": null, "Cliente": {"crmStageId": null}, "Marca": {"id": "d26d5168-f6e2-4d15-9c10-e4fc4aab2a97", "processoId": "ddb06225-e8a3-4253-a14a-cf19b2be97e4", "nome": "TECNOLOGIA FUTURA", "apresentacao": "Nominativa", "natureza": "Produtos e/ou Serviço", "logomarcaPath": null, "temLogomarca": false, "NCL": [{"id": "e51948f9-04e4-43d9-b65c-b0f3d1175511", "marcaId": "d26d5168-f6e2-4d15-9c10-e4fc4aab2a97", "codigo": "42", "especificacao": "Serviços de desenvolvimento de software; consultoria em tecnologia da informação; serviços de computação em nuvem", "status": null, "edicao": null, "estadoDasEspecificacoes": "Definidas"}]}, "Despacho": [{"id": "aa727256-f75e-4741-875d-3bb57079b5a3", "processoId": "ddb06225-e8a3-4253-a14a-cf19b2be97e4", "rpiId": "4bbb11d0-ff8a-478c-8a40-19a7c31f00d2", "codigo": "IPAS009", "nome": "Publicação de pedido de registro para oposição (exame formal concluído)", "textoComplementar": null, "detalhesDespachoId": null, "DetalhesDespacho": null, "ProtocoloDespacho": [], "RPI": {"dataPublicacao": "2025-05-27T03:00:00.000Z", "numero": 2838}}], "Titular": [{"nomeRazaoSocial": "TECNOLOGIA FUTURA LTDA", "pais": "BR", "uf": "SP"}], "Procurador": {"nome": "REGISTRE-SE LTDA."}, "dataPublicacaoRPI": "2025-05-27T03:00:00.000Z"}