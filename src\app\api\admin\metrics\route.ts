import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET() {
  try {
    // TODO: Adicionar autenticação admin aqui
    // const token = request.cookies.get('admin_token')?.value;
    // if (!token || !await validateAdminToken(token)) {
    //   return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    // }

    const hoje = new Date();
    const inicioHoje = new Date(hoje.getFullYear(), hoje.getMonth(), hoje.getDate());
    const fimHoje = new Date(inicioHoje.getTime() + 24 * 60 * 60 * 1000);

    // Calcular métricas em paralelo para melhor performance
    const [
      usuariosAtivosHoje,
      sessoesAtivas,
      downloadsHoje,
      tempoMedioSessao,
      totalUsuarios,
      totalProcessos,
      downloadsUltimos7Dias,
      loginsUltimos7Dias
    ] = await Promise.all([
      // Usuários únicos que fizeram login hoje
      prisma.sessionLog.findMany({
        where: {
          loginAt: {
            gte: inicioHoje,
            lt: fimHoje,
          },
        },
        select: { clienteId: true },
        distinct: ['clienteId'],
      }),

      // Sessões ativas no momento
      prisma.sessionLog.count({
        where: {
          isActive: true,
        },
      }),

      // Downloads de protocolo hoje
      prisma.protocoloDownloadLog.count({
        where: {
          downloadAt: {
            gte: inicioHoje,
            lt: fimHoje,
          },
          success: true,
        },
      }),

      // Tempo médio de sessão (últimas 100 sessões finalizadas)
      prisma.sessionLog.findMany({
        where: {
          isActive: false,
          sessionDuration: { not: null },
        },
        select: { sessionDuration: true },
        orderBy: { logoutAt: 'desc' },
        take: 100,
      }),

      // Total de usuários cadastrados
      prisma.cliente.count(),

      // Total de processos
      prisma.processo.count(),

      // Downloads dos últimos 7 dias
      prisma.protocoloDownloadLog.groupBy({
        by: ['downloadAt'],
        where: {
          downloadAt: {
            gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
          },
          success: true,
        },
        _count: true,
      }),

      // Logins dos últimos 7 dias
      prisma.sessionLog.groupBy({
        by: ['loginAt'],
        where: {
          loginAt: {
            gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
          },
        },
        _count: true,
      }),
    ]);

    // Calcular tempo médio de sessão
    const tempoMedio = tempoMedioSessao.length > 0
      ? Math.round(
          tempoMedioSessao.reduce((acc, session) => acc + (session.sessionDuration || 0), 0) 
          / tempoMedioSessao.length
        )
      : 0;

    // Buscar usuários mais ativos (por downloads)
    const usuariosMaisAtivos = await prisma.protocoloDownloadLog.groupBy({
      by: ['clienteId'],
      where: {
        downloadAt: {
          gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // últimos 30 dias
        },
        success: true,
      },
      _count: { clienteId: true },
      orderBy: { _count: { clienteId: 'desc' } },
      take: 10,
    });

    // Buscar dados dos clientes mais ativos
    const clienteIds = usuariosMaisAtivos.map(u => u.clienteId);
    const dadosClientes = await prisma.cliente.findMany({
      where: { id: { in: clienteIds } },
      select: { id: true, nome: true, identificador: true },
    });

    const usuariosComDados = usuariosMaisAtivos.map(usuario => {
      const cliente = dadosClientes.find(c => c.id === usuario.clienteId);
      return {
        clienteId: usuario.clienteId,
        nome: cliente?.nome || 'Não informado',
        identificador: cliente?.identificador || 'N/A',
        totalDownloads: usuario._count.clienteId,
      };
    });

    // Processos mais baixados
    const processosMaisBaixados = await prisma.protocoloDownloadLog.groupBy({
      by: ['numeroProcesso'],
      where: {
        downloadAt: {
          gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // últimos 30 dias
        },
        success: true,
      },
      _count: { numeroProcesso: true },
      orderBy: { _count: { numeroProcesso: 'desc' } },
      take: 10,
    });

    return NextResponse.json({
      metricas: {
        usuariosAtivosHoje: usuariosAtivosHoje.length,
        sessoesAtivas,
        downloadsHoje,
        tempoMedioSessao: tempoMedio,
        totalUsuarios,
        totalProcessos,
      },
      tendencias: {
        downloadsUltimos7Dias: downloadsUltimos7Dias.length,
        loginsUltimos7Dias: loginsUltimos7Dias.length,
      },
      rankings: {
        usuariosMaisAtivos: usuariosComDados,
        processosMaisBaixados: processosMaisBaixados.map(p => ({
          numeroProcesso: p.numeroProcesso,
          totalDownloads: p._count.numeroProcesso,
        })),
      },
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Erro ao buscar métricas:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
} 