'use client';

import React, { useState } from 'react';

interface BuscaParams {
  termo?: string;
  nomeMarca?: string;
  numeroProcesso?: string;
  identificadorCliente?: string;
  clienteId?: number;
  dataInicio?: string;
  dataFim?: string;
  apenasProcuradorRegistrese?: boolean;
  page?: number;
  pageSize?: number;
}

interface ProcessoBuscaProps {
  onBuscar: (filtros: BuscaParams) => void;
  loading?: boolean;
}

const ProcessoBusca: React.FC<ProcessoBuscaProps> = ({ onBuscar, loading = false }) => {
  const [filtros, setFiltros] = useState<BuscaParams>({
    page: 1,
    pageSize: 20,
  });
  const [mostrarFiltrosAvancados, setMostrarFiltrosAvancados] = useState(false);

  const handleInputChange = (field: keyof BuscaParams, value: string | number) => {
    setFiltros(prev => ({
      ...prev,
      [field]: value === '' ? undefined : value,
      page: 1, // Resetar página quando filtros mudam
    }));
  };

  const handleBuscar = () => {
    onBuscar(filtros);
  };

  const handleLimpar = () => {
    const filtrosLimpos = { page: 1, pageSize: 20 };
    setFiltros(filtrosLimpos);
    onBuscar(filtrosLimpos);
  };

  const handleCheckboxChange = (field: keyof BuscaParams, checked: boolean) => {
    setFiltros(prev => ({
      ...prev,
      [field]: checked,
      page: 1, // Resetar página quando filtros mudam
    }));
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-2xl font-bold text-gray-800">Buscar Processos</h2>
        <button
          onClick={() => setMostrarFiltrosAvancados(!mostrarFiltrosAvancados)}
          className="text-blue-600 hover:text-blue-800 font-medium"
        >
          {mostrarFiltrosAvancados ? '← Busca Simples' : 'Filtros Avançados →'}
        </button>
      </div>

      {!mostrarFiltrosAvancados ? (
        // Busca Rápida
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Busca Geral
            </label>
            <input 
              type="text"
              placeholder="Digite nome da marca ou número do processo..."
              value={filtros.termo || ''}
              onChange={(e) => handleInputChange('termo', e.target.value)}
              className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              onKeyPress={(e) => e.key === 'Enter' && handleBuscar()}
            />
            <p className="text-sm text-gray-500 mt-1">
              Busca por nome da marca ou número do processo
            </p>
          </div>
        </div>
      ) : (
        // Filtros Avançados
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Nome da Marca
              </label>
              <input 
                type="text"
                placeholder="Nome da marca"
                value={filtros.nomeMarca || ''}
                onChange={(e) => handleInputChange('nomeMarca', e.target.value)}
                className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Número do Processo
              </label>
              <input 
                type="text"
                placeholder="Número do processo"
                value={filtros.numeroProcesso || ''}
                onChange={(e) => handleInputChange('numeroProcesso', e.target.value)}
                className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Identificador do Cliente
              </label>
              <input 
                type="text"
                placeholder="Identificador do cliente"
                value={filtros.identificadorCliente || ''}
                onChange={(e) => handleInputChange('identificadorCliente', e.target.value)}
                className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Data de Depósito - Início
              </label>
              <input 
                type="date"
                value={filtros.dataInicio || ''}
                onChange={(e) => handleInputChange('dataInicio', e.target.value)}
                className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Data de Depósito - Fim
              </label>
              <input 
                type="date"
                value={filtros.dataFim || ''}
                onChange={(e) => handleInputChange('dataFim', e.target.value)}
                className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                ID do Cliente
              </label>
              <input 
                type="number"
                placeholder="ID numérico do cliente"
                value={filtros.clienteId || ''}
                onChange={(e) => handleInputChange('clienteId', e.target.value ? parseInt(e.target.value) : '')}
                className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>

          {/* Seção de Filtros Especiais */}
          <div className="border-t border-gray-200 pt-4 mt-4">
            <h3 className="text-sm font-medium text-gray-700 mb-3">Filtros Especiais</h3>
            <div className="flex items-center">
              <input
                type="checkbox"
                id="procuradorRegistrese"
                checked={filtros.apenasProcuradorRegistrese || false}
                onChange={(e) => handleCheckboxChange('apenasProcuradorRegistrese', e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="procuradorRegistrese" className="ml-2 text-sm text-gray-700">
                Apenas processos do procurador <span className="font-medium text-blue-600">REGISTRE-SE LTDA</span>
              </label>
            </div>
            <p className="text-xs text-gray-500 mt-1 ml-6">
              Filtra apenas processos onde o procurador contém "REGISTRE-SE LTDA"
            </p>
          </div>
        </div>
      )}

      {/* Botões de ação */}
      <div className="flex gap-3 mt-6">
        <button 
          onClick={handleBuscar}
          disabled={loading}
          className="bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700 disabled:bg-blue-400 disabled:cursor-not-allowed font-medium transition-colors"
        >
          {loading ? (
            <div className="flex items-center">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Buscando...
            </div>
          ) : (
            'Buscar'
          )}
        </button>

        <button 
          onClick={handleLimpar}
          disabled={loading}
          className="bg-gray-500 text-white px-6 py-3 rounded-md hover:bg-gray-600 disabled:bg-gray-400 disabled:cursor-not-allowed font-medium transition-colors"
        >
          Limpar
        </button>

        <div className="ml-auto flex items-center">
          <label className="text-sm font-medium text-gray-700 mr-2">
            Resultados por página:
          </label>
          <select
            value={filtros.pageSize || 20}
            onChange={(e) => handleInputChange('pageSize', parseInt(e.target.value))}
            className="border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value={10}>10</option>
            <option value={20}>20</option>
            <option value={50}>50</option>
            <option value={100}>100</option>
          </select>
        </div>
      </div>
    </div>
  );
};

export default ProcessoBusca; 