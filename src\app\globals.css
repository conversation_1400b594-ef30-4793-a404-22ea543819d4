@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
  
  /* Cores personalizadas */
  --color-brancoHeader: rgba(255,255,255,0.6);
  --color-verde: #30B946;
  --color-verdeOpaco: #5EBB47;
  --color-verdeClaro: #38D658;
  --color-verdeEscuro: #2FA43A;
  --color-verdeCTA: #1F2F20;
  --color-azul: #269AB6;
  --color-azulHover: #2CB1D1;
  --color-azulClaro: #30C1E0;
  --color-azulEscuro: #2090A8;
  --color-azulFundoSection: #458DAA;
  --color-TextosEscurosEBg: #1C3622;
  --color-backgroundCinza: #EFEFEF;
  --color-branco: #FFFFFF;
  --color-preto: #000000;
  --color-cinza: #7C7D7C;
  --color-cinzaClaro: #E6E6E6;
  --color-cinzaFooter: #808080;
  --color-cinzaFaixa: #D9D9D9;
  --color-cinzaTexto: #7D7D7D;
  --color-lineGray: #D8D8D8;
  --color-verdeDark: #277A34;
  --color-azulDark: #1D708C;
  --color-cinzaDark: #333333;
  --color-brancoDark: #F1F1F1;
  --color-darkIncomplete: #FF5722;
  --color-darkComplete: #26A69A;
  --color-fundoArquivado: #FFE1E1; /* Nova cor para fundo de card arquivado */
  --color-fundoVigor: #EBEEFF; /* Nova cor para fundo de card em vigor */
  --color-fundoIndeferido: #FFFFE3; /* Nova cor para fundo de card indeferido */
  --color-fundoDeferido: #E8F7EC; /* Nova cor para fundo de card deferido */
  
  /* Cores adicionadas para as tabelas */
  --color-headerTabela: #A2A2A2; /* Cor do cabeçalho da tabela */
  --color-linhaTabela: #E7E7E7; /* Cor de fundo das linhas alternadas */
  --color-bordaTabela: #ACACAC; /* Cor da borda inferior da tabela */
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #F5F5F5;
    --foreground: #ededed;
  }
}
body {
  background-color: var(--background);
  font-family: var(--font-raleway);
  font-variant-numeric: lining-nums;
}

/* Utilitários para texto */
.text-verde { color: var(--color-verde); }
.text-verdeEscuro { color: var(--color-verdeEscuro); }
.text-verdeDark { color: var(--color-verdeDark); }
.text-azul { color: var(--color-azul); }
.text-azulHover { color: var(--color-azulHover); }
.text-branco { color: var(--color-branco); }
.text-preto { color: var(--color-preto); }
.text-cinza { color: var(--color-cinza); }
.text-cinzaTexto { color: var(--color-cinzaTexto); }
.text-TextosEscurosEBg { color: var(--color-TextosEscurosEBg); }

/* Utilitários para background */
.bg-verde { background-color: var(--color-verde); }
.bg-verdeEscuro { background-color: var(--color-verdeEscuro); }
.bg-azul { background-color: var(--color-azul); }
.bg-azulHover { background-color: var(--color-azulHover); }
.bg-branco { background-color: var(--color-branco); }
.bg-preto { background-color: var(--color-preto); }
.bg-backgroundCinza { background-color: var(--color-backgroundCinza); }
.bg-cinzaClaro { background-color: var(--color-cinzaClaro); }
.bg-cinzaFaixa { background-color: var(--color-cinzaFaixa); }
.bg-fundoArquivado { background-color: var(--color-fundoArquivado); } /* Nova classe utilitária */
.bg-fundoVigor { background-color: var(--color-fundoVigor); } /* Nova classe */
.bg-fundoIndeferido { background-color: var(--color-fundoIndeferido); } /* Nova classe */
.bg-fundoDeferido { background-color: var(--color-fundoDeferido); } /* Nova classe */

/* Animações personalizadas */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes bounce-gentle {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(48, 185, 70, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(48, 185, 70, 0.6);
  }
}

.animate-fade-in {
  animation: fade-in 0.5s ease-out;
}

.animate-shimmer {
  animation: shimmer 2s infinite;
}

.animate-bounce-gentle {
  animation: bounce-gentle 2s infinite;
}

.animate-pulse-glow {
  animation: pulse-glow 2s infinite;
}

/* Efeitos de hover melhorados */
.hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

/* Cores adicionais para o novo design */
.text-laranja { color: #FF9500; }
.bg-laranja { background-color: #FF9500; }
.border-laranja { border-color: #FF9500; }