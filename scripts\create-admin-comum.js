const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function createAdminComum() {
  try {
    // Configurações do usuário comum
    const email = "<EMAIL>";
    const nome = 'Comercial e Processos';
    const senha = 'comercial123'; // Senha mais simples para teste
    const role = 'COMUM';

    // Verificar se já existe
    const existingUser = await prisma.adminUser.findUnique({
      where: { email }
    });

    if (existingUser) {
      console.log('❌ Usuário admin comum já existe!');
      console.log(`Email: ${existingUser.email}`);
      console.log(`Nome: ${existingUser.nome}`);
      console.log(`Role: ${existingUser.role}`);
      
      // Atualizar senha mesmo assim
      console.log('\n🔧 Atualizando senha...');
      const senhaHash = await bcrypt.hash(senha, 12);
      
      await prisma.adminUser.update({
        where: { email },
        data: { 
          senha: senhaHash,
          ativo: true 
        }
      });
      
      console.log('✅ Senha atualizada!');
      console.log(`🔑 Nova senha: ${senha}`);
      return;
    }

    // Hash da senha
    const senhaHash = await bcrypt.hash(senha, 12);

    // Criar usuário
    const user = await prisma.adminUser.create({
      data: {
        email,
        nome,
        senha: senhaHash,
        role,
        ativo: true
      }
    });

    console.log('✅ Usuário admin comum criado com sucesso!');
    console.log('📧 Email:', email);
    console.log('🔑 Senha:', senha);
    console.log('👤 Nome:', nome);
    console.log('🛡️  Role:', role);
    console.log('');
    console.log('🌐 Acesse: http://localhost:3000/admin/login');
    console.log('➡️  Será redirecionado para /admin/busca');

  } catch (error) {
    console.error('❌ Erro ao criar usuário admin comum:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createAdminComum(); 