import React from 'react';

interface LogoProps {
  className?: string;
}

export default function LogoInovacaoBrasil({ className = "" }: LogoProps) {
  return (
    <svg
      viewBox="0 0 200 200"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <rect width="200" height="200" rx="20" fill="#E5FFE8" />
      <path
        d="M100 40L160 140H40L100 40Z"
        fill="#00CC33"
      />
      <circle cx="100" cy="100" r="30" fill="#008822" />
      <path
        d="M85 100L95 110L115 90"
        stroke="white"
        strokeWidth="6"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
} 