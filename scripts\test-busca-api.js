const fetch = require('node-fetch');

async function testBuscaAPI() {
  try {
    console.log('🧪 Testando API de busca...\n');
    
    const tests = [
      {
        name: 'Busca simples por marca',
        params: { nomeMarca: 'test', pageSize: 5 }
      },
      {
        name: 'Busca por número processo',
        params: { numeroProcesso: '123', pageSize: 5 }
      },
      {
        name: 'Busca por nome cliente',
        params: { nomeCliente: 'teste', pageSize: 5 }
      },
      {
        name: 'Busca sem filtros (todos)',
        params: { pageSize: 5 }
      }
    ];

    for (const test of tests) {
      console.log(`🔍 ${test.name}:`);
      
      const params = new URLSearchParams();
      Object.entries(test.params).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, String(value));
        }
      });

      const url = `http://localhost:3000/api/admin/processos/busca?${params.toString()}`;
      console.log(`   URL: ${url}`);
      
      try {
        const response = await fetch(url);
        const status = response.status;
        
        if (response.ok) {
          const data = await response.json();
          console.log(`   ✅ Status: ${status}`);
          console.log(`   📊 Total processos: ${data.totalProcessos}`);
          console.log(`   📁 Processos retornados: ${data.processos?.length}`);
          
          if (data.processos && data.processos.length > 0) {
            console.log(`   🔖 Primeiro processo: ${data.processos[0].numero} - ${data.processos[0].Marca?.nome || 'N/A'}`);
          }
        } else {
          console.log(`   ❌ Status: ${status}`);
          const errorText = await response.text();
          console.log(`   📝 Erro: ${errorText}`);
        }
      } catch (fetchError) {
        console.log(`   💥 Erro de conexão: ${fetchError.message}`);
      }
      
      console.log('   ' + '─'.repeat(50));
    }

  } catch (error) {
    console.error('❌ Erro no teste:', error);
  }
}

testBuscaAPI(); 