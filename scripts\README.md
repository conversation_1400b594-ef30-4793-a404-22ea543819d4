# Scripts de Utilitários

## remove-duplicates.js

Script para remover imagens duplicadas na pasta `src/private-assets/processos`.

### Funcionamento

O script:
1. <PERSON><PERSON><PERSON> to<PERSON> as imagens na pasta de processos
2. Identifica duplicatas (mesmo nome com extensões diferentes)
3. Prioriza arquivos JPG/JPEG sobre PNG (melhor performance)
4. <PERSON><PERSON>ve as duplicatas, mantendo apenas uma versão de cada imagem
5. Gera um relatório detalhado do processo

### Como usar

1. Primeiro, certifique-se de que suas imagens estão na pasta `src/private-assets/processos`

2. Execute o script:
```bash
node scripts/remove-duplicates.js
```

### Critérios de priorização

- **JPG/JPEG**: Prioridade alta (mantido)
- **PNG**: Prioridade baixa (removido se existir JPG/JPEG)

### Exemplo de saída

```
🔍 Analisando imagens duplicadas na pasta: /projeto/src/private-assets/processos
📊 Total de imagens encontradas: 150

🔄 Processando duplicatas para: 123456
   ✅ Mantendo: 123456.jpg
   🗑️  Removido: 123456.png

✨ Processo concluído!
📈 Arquivos removidos: 25
📈 Arquivos restantes: 125

📋 Resumo das extensões restantes:
   .jpg: 120 arquivos
   .png: 5 arquivos
```

### Segurança

- O script faz backup implícito ao manter sempre uma versão
- Logs detalhados de todas as operações
- Não remove arquivos que não sejam duplicatas 