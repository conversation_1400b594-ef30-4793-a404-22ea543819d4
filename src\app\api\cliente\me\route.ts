import { NextResponse } from 'next/server';
// import { cookies } from 'next/headers'; // Não vamos usar a importação direta
import type { NextRequest } from 'next/server'; // Importar NextRequest para tipagem correta
import { jwtVerify } from 'jose';
import { prisma } from '@/lib/prisma';

const JWT_SECRET = process.env.JWT_SECRET;

// Função auxiliar para verificar o token (semelhante à do middleware)
async function verifyToken(token: string, secret: string): Promise<any | null> {
  try {
    const { payload } = await jwtVerify(
      token,
      new TextEncoder().encode(secret)
    );
    return payload;
  } catch (error) {
    console.error("Erro ao verificar token na API /me:", error);
    return null;
  }
}

// Usar NextRequest para ter acesso a request.cookies
export async function GET(request: NextRequest) {
  const token = request.cookies.get('auth_token')?.value;

  if (!token) {
    return NextResponse.json({ error: 'Não autorizado - Token ausente' }, { status: 401 });
  }

  if (!JWT_SECRET) {
    console.error('API /me: JWT_SECRET não configurada!');
    return NextResponse.json({ error: 'Erro interno do servidor - Configuração' }, { status: 500 });
  }

  try {
    const payload = await verifyToken(token, JWT_SECRET);

    if (!payload || !payload.clientId) {
      return NextResponse.json({ error: 'Não autorizado - Token inválido ou sem ID' }, { status: 401 });
    }
console.log(payload.clientId);    
    const cliente = await prisma.cliente.findUnique({
      where: {
        id: payload.clientId,
      },
      select: { // Seleciona apenas os campos necessários
        id: true,
        nome: true,
        identificador: true,
        numeroDocumento: true, // Pode ser útil
        ContatoCliente: true, // Pode ser útil
        // Não inclua Processo aqui para manter a resposta leve
      },
    });

    if (!cliente) {
      return NextResponse.json({ error: 'Cliente não encontrado' }, { status: 404 });
    }

    return NextResponse.json({ cliente });

  } catch (error) {
    console.error('Erro na API /me:', error);
    return NextResponse.json({ error: 'Erro interno do servidor' }, { status: 500 });
  }
} 