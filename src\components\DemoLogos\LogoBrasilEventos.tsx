import React from 'react';

interface LogoProps {
  className?: string;
}

export default function LogoBrasilEventos({ className = "" }: LogoProps) {
  return (
    <svg
      viewBox="0 0 200 200"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <rect width="200" height="200" rx="20" fill="#FFE5E5" />
      <circle cx="100" cy="100" r="50" fill="#FF3333" />
      <path
        d="M80 100C80 88.954 88.954 80 100 80C111.046 80 120 88.954 120 100"
        stroke="white"
        strokeWidth="8"
        strokeLinecap="round"
      />
      <circle cx="80" cy="110" r="6" fill="white" />
      <circle cx="120" cy="110" r="6" fill="white" />
      <path
        d="M60 60L80 80M120 80L140 60M60 140L80 120M120 120L140 140"
        stroke="#FF3333"
        strokeWidth="8"
        strokeLinecap="round"
      />
    </svg>
  );
} 