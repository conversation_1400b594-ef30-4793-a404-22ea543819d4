'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import ProcessoCard from '@/components/ProcessoCard';
import { useEstimativasMerito } from '@/hooks/useEstimativasMerito';
import ClienteHeader from '@/components/ClienteHeader/ClienteHeader';

// Importar os processos de exemplo
import processoPublicado from '@/utils/processosExemplo/processoPublicado.json';
import processoComOposicao from '@/utils/processosExemplo/processoComOposiçao.json';
import processoDeferido from '@/utils/processosExemplo/processoDeferido.json';
import processoConcessao from '@/utils/processosExemplo/processoConcessao.json';

// Interface para o cliente de demonstração
interface DemoCliente {
  id: number;
  nome: string;
  identificador: string;
  numeroDocumento: string;
  ContatoCliente: {
    email: string | null;
    telefone: string | null;
    endereco: string | null;
  }[];
}

// Cliente fictício para demonstração
const demoCliente: DemoCliente = {
  id: 9999,
  nome: "Cliente Demonstração",
  identificador: "DEMO123",
  numeroDocumento: "12345678901234",
  ContatoCliente: [
    {
      email: "<EMAIL>",
      telefone: "(11) 99999-9999",
      endereco: "Av. Paulista, 1000 - São Paulo/SP"
    }
  ]
};

// Função para modificar os processos de exemplo para a demonstração
const adaptarProcessosParaDemo = () => {
  // Função para gerar um ID único para cada processo
  const gerarIdUnico = () => `demo-${Math.random().toString(36).substring(2, 15)}`;
  
  // Função para modificar um processo para a demonstração
  const adaptarProcesso = (processo: any, modificacoes: any) => {
    return {
      ...processo,
      id: gerarIdUnico(),
      clienteId: demoCliente.id,
      // Adicionar modificações específicas
      ...modificacoes
    };
  };

  // Adaptar cada processo com modificações específicas
  const processo1 = adaptarProcesso(processoPublicado, {
    Marca: {
      ...processoPublicado.Marca,
      nome: "TECNOLOGIA FUTURA",
      apresentacao: "Nominativa",
      NCL: [{ ...processoPublicado.Marca.NCL[0], codigo: "42" }]
    }
  });

  const processo2 = adaptarProcesso(processoComOposicao, {
    Marca: {
      ...processoComOposicao.Marca,
      nome: "INOVAÇÃO BRASIL",
      apresentacao: "Mista",
      NCL: [{ ...processoComOposicao.Marca.NCL[0], codigo: "35" }]
    }
  });

  const processo3 = adaptarProcesso(processoDeferido, {
    Marca: {
      ...processoDeferido.Marca,
      nome: "SAÚDE NATURAL",
      apresentacao: "Mista",
      NCL: [{ ...processoDeferido.Marca.NCL[0], codigo: "44" }]
    }
  });

  const processo4 = adaptarProcesso(processoConcessao, {
    Marca: {
      ...processoConcessao.Marca,
      nome: "BRASIL EVENTOS",
      apresentacao: "Mista",
      NCL: [{ ...processoConcessao.Marca.NCL[0], codigo: "41" }]
    }
  });

  return [processo1, processo2, processo3, processo4];
};

// Função para obter a saudação baseada no horário
const obterSaudacao = () => {
  const hora = new Date().getHours();
  
  if (hora >= 5 && hora < 12) {
    return "Bom dia";
  } else if (hora >= 12 && hora < 18) {
    return "Boa tarde";
  } else {
    return "Boa noite";
  }
};

export default function DemoPage() {
  const router = useRouter();
  const [processos, setProcessos] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [saudacao, setSaudacao] = useState<string>("");
  
  // Hook para buscar estimativas de mérito
  const { estimativas: estimativasMerito, loading: loadingEstimativas } = useEstimativasMerito();

  useEffect(() => {
    // Carregar os processos adaptados
    const processosDemo = adaptarProcessosParaDemo();
    setProcessos(processosDemo);
    setLoading(false);
  }, []);

  // Atualizar a saudação a cada minuto
  useEffect(() => {
    const atualizarSaudacao = () => {
      setSaudacao(obterSaudacao());
    };

    // Atualizar inicialmente
    atualizarSaudacao();

    // Atualizar a cada minuto
    const intervalo = setInterval(atualizarSaudacao, 60000);

    return () => clearInterval(intervalo);
  }, []);

  const handleLogout = () => {
    // Redirecionar para o site principal
    window.location.href = 'https://registre.se';
  };

  if (loading) {
    return (
      <div className="fixed inset-0 flex items-center justify-center bg-white bg-opacity-80">
        <div className="text-center">
          <div className="mb-4">
            <svg className="animate-spin h-12 w-12 mx-auto text-green-500" viewBox="0 0 24 24">
              <circle
                className="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                strokeWidth="4"
              ></circle>
              <path
                className="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              ></path>
            </svg>
          </div>
          <p className="text-gray-800 font-medium">Carregando demonstração...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#F5F5F5]">
      <ClienteHeader cliente={demoCliente} handleLogout={handleLogout} />

      {/* Banner de Demonstração */}
      <div className="bg-blue-600 text-white py-2 text-center">
        <p className="text-sm font-medium">
          Esta é uma página de demonstração. Os dados são fictícios.
        </p>
      </div>

      {/* Conteúdo Principal */}
      <main className="max-w-7xl mx-auto px-2 sm:px-6 lg:px-8 mt-28">
        {/* Cabeçalho da página */}
        <div className="mb-8 text-center">
          <h1 className="text-3xl font-bold text-gray-800 mb-2">
            {saudacao}! Seja bem-vindo(a)
          </h1>
          <p className="text-gray-600">
            Monitore o andamento dos seus processos de registro.
          </p>
          <p className="text-sm text-gray-500 mt-2">
            Mostrando {processos.length} processos de demonstração
          </p>
        </div>

        {/* Lista de processos */}
        <div className="space-y-6">
          {processos.length > 0 ? (
            processos.map((processo) => (
              <ProcessoCard 
                key={processo.id} 
                processo={processo} 
                estimativasMerito={estimativasMerito}
              />
            ))
          ) : (
            <div className="bg-white p-6 rounded-lg shadow-md text-center">
              <p className="text-gray-600">Nenhum processo encontrado.</p>
            </div>
          )}
        </div>
        
        <span className="text-[#A3A3A3] mt-8 text-sm text-center block pb-8">
          Esta é uma demonstração da página de clientes. Os dados são fictícios.
        </span>
      </main>
    </div>
  );
} 