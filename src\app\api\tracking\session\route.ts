import { NextRequest, NextResponse } from 'next/server';
import { jwtVerify } from 'jose';
import { prisma } from '@/lib/prisma';

const JWT_SECRET = process.env.JWT_SECRET;

// Função auxiliar para verificar o token
async function verifyToken(token: string, secret: string): Promise<any | null> {
  try {
    const { payload } = await jwtVerify(
      token,
      new TextEncoder().encode(secret)
    );
    return payload;
  } catch (error) {
    console.error("Erro ao verificar token:", error);
    return null;
  }
}

// Função para extrair IP do cliente
function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');
  const cfConnectingIP = request.headers.get('cf-connecting-ip');
  
  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }
  if (realIP) {
    return realIP;
  }
  if (cfConnectingIP) {
    return cfConnectingIP;
  }
  
  return 'unknown';
}

// POST - Criar nova sessão (login)
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { clienteId, identificador } = body;

    if (!clienteId || !identificador) {
      return NextResponse.json(
        { error: 'clienteId e identificador são obrigatórios' },
        { status: 400 }
      );
    }

    const ipAddress = getClientIP(request);
    const userAgent = request.headers.get('user-agent') || null;

    // Marcar sessões anteriores como inativas para este cliente
    await prisma.sessionLog.updateMany({
      where: {
        clienteId,
        isActive: true,
      },
      data: {
        isActive: false,
        logoutAt: new Date(),
        sessionDuration: null, // Será calculado se necessário
      },
    });

    // Criar nova sessão
    const sessionLog = await prisma.sessionLog.create({
      data: {
        clienteId,
        identificador,
        ipAddress,
        userAgent,
        isActive: true,
      },
    });

    console.log(`Nova sessão criada: Cliente ${clienteId}, IP ${ipAddress}`);

    return NextResponse.json({
      message: 'Sessão registrada com sucesso',
      sessionId: sessionLog.id,
    });

  } catch (error) {
    console.error('Erro ao registrar sessão:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

// PATCH - Atualizar sessão existente (logout ou heartbeat)
export async function PATCH(request: NextRequest) {
  try {
    // Verificar autenticação
    const token = request.cookies.get('auth_token')?.value;

    if (!token || !JWT_SECRET) {
      return NextResponse.json(
        { error: 'Não autorizado' }, 
        { status: 401 }
      );
    }

    const payload = await verifyToken(token, JWT_SECRET);
    if (!payload || !payload.clientId) {
      return NextResponse.json(
        { error: 'Token inválido' }, 
        { status: 401 }
      );
    }

    const body = await request.json();
    const { action } = body; // 'logout' ou 'heartbeat'
    const clienteId = payload.clientId;

    if (action === 'logout') {
      // Encontrar e finalizar sessão ativa
      const sessionAtiva = await prisma.sessionLog.findFirst({
        where: {
          clienteId,
          isActive: true,
        },
        orderBy: {
          loginAt: 'desc',
        },
      });

      if (sessionAtiva) {
        const now = new Date();
        const sessionDuration = Math.floor(
          (now.getTime() - sessionAtiva.loginAt.getTime()) / (1000 * 60)
        ); // em minutos

        await prisma.sessionLog.update({
          where: { id: sessionAtiva.id },
          data: {
            isActive: false,
            logoutAt: now,
            sessionDuration,
          },
        });

        console.log(`Sessão finalizada: Cliente ${clienteId}, Duração: ${sessionDuration}min`);

        return NextResponse.json({
          message: 'Logout registrado com sucesso',
          sessionDuration,
        });
      }
    } else if (action === 'heartbeat') {
      // Apenas confirmar que a sessão está ativa
      const sessionAtiva = await prisma.sessionLog.findFirst({
        where: {
          clienteId,
          isActive: true,
        },
        orderBy: {
          loginAt: 'desc',
        },
      });

      if (sessionAtiva) {
        // Atualizar timestamp da sessão
        await prisma.sessionLog.update({
          where: { id: sessionAtiva.id },
          data: {
            updatedAt: new Date(),
          },
        });

        return NextResponse.json({
          message: 'Heartbeat registrado',
          sessionActive: true,
        });
      }
    }

    return NextResponse.json(
      { error: 'Ação inválida ou sessão não encontrada' },
      { status: 400 }
    );

  } catch (error) {
    console.error('Erro ao atualizar sessão:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
} 