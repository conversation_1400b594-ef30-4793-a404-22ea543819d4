'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { FaEye, FaEyeSlash } from 'react-icons/fa';
import Image from 'next/image';


export default function AdminLoginPage() {
  const [email, setEmail] = useState('');
  const [senha, setSenha] = useState('');
  const [manterConectado, setManterConectado] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const router = useRouter();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const response = await fetch('/api/admin/auth/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          email,
          senha,
          manterConectado
        })
      });

      const data = await response.json();

      if (response.ok) {
        // Redirecionar baseado na role
        if (data.user.role === 'MASTER') {
          router.push('/admin');
        } else {
          router.push('/admin/busca');
        }
      } else {
        setError(data.error || 'Erro ao fazer login');
      }
    } catch (error) {
      setError('Erro de conexão. Tente novamente.');
      console.error('Erro no login:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 relative" style={{backgroundImage: "radial-gradient(#d1d5db 1px, transparent 1px)", backgroundSize: "30px 30px"}}>
      <div className="w-full max-w-md">
        {/* Header verde com logo */}
        <div className="text-center bg-[#45B063] w-full h-[93px] flex items-center justify-center rounded-tr-lg rounded-tl-lg">
          <Image src="/logo.svg" alt="Logo Registre.se" width={200} height={80} className="mx-auto" />
        </div>
        {/* Card do formulário */}
        <div className="bg-white p-8 rounded-b-lg shadow-lg">
          <h2 className="text-2xl font-bold text-center text-TextosEscurosEBg mb-2">Acesso Administrativo</h2>
          <p className="text-center text-gray-500 mb-6">Entre com seu e-mail e senha</p>
          <form onSubmit={handleSubmit} className="space-y-6">
            {error && (
              <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4 text-sm">
                {error}
              </div>
            )}
            {/* Email */}
            <div>
              <label htmlFor="email" className="block text-TextosEscurosEBg text-sm font-medium mb-2">Email</label>
              <input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                className="w-full bg-white px-4 py-2 border-2 border-[#EFF1F2] rounded-md outline-none focus:outline-none"
                placeholder="<EMAIL>"
              />
            </div>
            {/* Senha */}
            <div>
              <label htmlFor="senha" className="block text-TextosEscurosEBg text-sm font-medium mb-2">Senha</label>
              <div className="relative">
                <input
                  id="senha"
                  type={showPassword ? 'text' : 'password'}
                  value={senha}
                  onChange={(e) => setSenha(e.target.value)}
                  required
                  className="w-full bg-white px-4 py-2 border-2 border-[#EFF1F2] rounded-md outline-none focus:outline-none pr-10"
                  placeholder="Digite sua senha"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                >
                  {showPassword ? (
                    <FaEyeSlash className="h-5 w-5 text-gray-400" />
                  ) : (
                    <FaEye className="h-5 w-5 text-gray-400" />
                  )}
                </button>
              </div>
            </div>
            {/* Manter Conectado */}
            <div className="flex items-center">
              <input
                id="manter-conectado"
                type="checkbox"
                checked={manterConectado}
                onChange={(e) => setManterConectado(e.target.checked)}
                className="w-4 h-4 text-verde border-gray-300 rounded focus:ring-verde"
              />
              <label htmlFor="manter-conectado" className="ml-2 block text-sm text-TextosEscurosEBg">
                Manter conectado
              </label>
            </div>
            {/* Botão de login */}
            <button
              type="submit"
              disabled={loading}
              className="w-full bg-verde hover:bg-verdeEscuro text-branco font-bold py-2 px-4 rounded-md transition duration-300 ease-in-out flex justify-center items-center disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? (
                <svg
                  className="animate-spin h-5 w-5 mr-3 text-branco"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
              ) : (
                'Entrar'
              )}
            </button>
          </form>
          {/* Footer */}
          <div className="mt-8 text-center">
            <p className="text-xs text-gray-500">
              Sistema administrativo RGSE - Acesso restrito
            </p>
          </div>
        </div>
      </div>
    </div>
  );
} 