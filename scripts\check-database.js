const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkDatabase() {
  try {
    console.log('🔍 Verificando dados no banco...\n');
    
    // Contar processos
    const totalProcessos = await prisma.processo.count();
    console.log(`📊 Total de processos: ${totalProcessos}`);
    
    // Contar clientes
    const totalClientes = await prisma.cliente.count();
    console.log(`👥 Total de clientes: ${totalClientes}`);
    
    // Contar marcas
    const totalMarcas = await prisma.marca.count();
    console.log(`🏷️ Total de marcas: ${totalMarcas}`);
    
    // Contar contatos
    const totalContatos = await prisma.contatoCliente.count();
    console.log(`📞 Total de contatos: ${totalContatos}\n`);
    
    if (totalProcessos > 0) {
      console.log('📋 Primeiros 3 processos:');
      const processos = await prisma.processo.findMany({
        take: 3,
        include: {
          Cliente: {
            select: {
              nome: true,
              identificador: true,
              ContatoCliente: {
                select: {
                  telefone: true,
                  telefoneSegundario: true
                }
              }
            }
          },
          Marca: {
            select: {
              nome: true
            }
          }
        }
      });
      
      processos.forEach((processo, index) => {
        console.log(`  ${index + 1}. Processo: ${processo.numero}`);
        console.log(`     Cliente: ${processo.Cliente?.nome || 'N/A'}`);
        console.log(`     Marca: ${processo.Marca?.nome || 'N/A'}`);
        const telefones = processo.Cliente?.ContatoCliente?.map(c => c.telefone || c.telefoneSegundario).filter(Boolean) || [];
        console.log(`     Telefones: ${telefones.length > 0 ? telefones.join(', ') : 'N/A'}`);
        console.log('');
      });
    }
    
    // Testar busca por telefone
    if (totalContatos > 0) {
      console.log('🧪 Testando busca por telefone...');
      const contatoComTelefone = await prisma.contatoCliente.findFirst({
        where: {
          OR: [
            { telefone: { not: null } },
            { telefoneSegundario: { not: null } }
          ]
        },
        select: {
          telefone: true,
          telefoneSegundario: true,
          Cliente: {
            select: {
              nome: true
            }
          }
        }
      });
      
      if (contatoComTelefone) {
        const telefoneTest = contatoComTelefone.telefone || contatoComTelefone.telefoneSegundario;
        console.log(`  Telefone encontrado: ${telefoneTest}`);
        console.log(`  Cliente: ${contatoComTelefone.Cliente.nome}`);
        
        // Testar a query que usamos na API
        const processosComTelefone = await prisma.processo.findMany({
          where: {
            Cliente: {
              ContatoCliente: {
                some: {
                  OR: [
                    { telefone: { contains: telefoneTest, mode: 'insensitive' } },
                    { telefoneSegundario: { contains: telefoneTest, mode: 'insensitive' } },
                  ]
                }
              }
            }
          },
          take: 1,
          include: {
            Cliente: {
              select: {
                nome: true,
                ContatoCliente: {
                  select: {
                    telefone: true,
                    telefoneSegundario: true
                  }
                }
              }
            }
          }
        });
        
        console.log(`  ✅ Processos encontrados com este telefone: ${processosComTelefone.length}`);
      }
    }

  } catch (error) {
    console.error('❌ Erro ao verificar banco:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkDatabase(); 