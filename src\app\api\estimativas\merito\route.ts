import { NextResponse } from 'next/server';
import { prisma } from "@/lib/prisma";

// Ajuste o caminho se o seu prisma client estiver em outro local

export async function GET() {
  try {
    const estimativaMaisRecente = await prisma.estimativaMeritoSalva.findFirst({
      orderBy: {
        dataCalculo: 'desc',
      },
    });

    if (!estimativaMaisRecente) {
      // Retorna null ou um objeto vazio se nenhuma estimativa for encontrada,
      // para que a timelineLogica possa lidar com isso gracefuly.
      return NextResponse.json(null, { status: 200 });
    }

    return NextResponse.json(estimativaMaisRecente, { status: 200 });
  } catch (error) {
    console.error('Erro ao buscar estimativas de mérito:', error);
    return NextResponse.json({ error: 'Erro interno do servidor ao buscar estimativas.' }, { status: 500 });
  }
}
