'use client';

import { useState, useEffect, JSX, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import ProcessoCard from '@/components/ProcessoCard';
import { ProcessoParaTimeline, EtapaTimelineGerada } from "@/lib/timelineNovasTypes";
import { gerarTimelineSimplificada } from "@/lib/timelineLogica";
import TimelineDinamica from '../../../_old_components/TimelineDinamica/TimelineDinamica';
import { 
  Processo_Interface, 
  Cliente_Interface, 
  ContatoCliente_Interface, 
} from "@/lib/appTypes";

// Interface simplificada para as estimativas de mérito (espelhando a de timelineLogica)
interface EstimativaMeritoData {
  mediaSemIntervencoes?: number | null;
  mediaComOposicao?: number | null;
  // Adicione outros campos se necessário para tipagem no frontend
}

const PAGE_SIZE = 100;

export default function Cliente() {
  const router = useRouter();
  
  const [cliente, setCliente] = useState<Cliente_Interface | null>(null);
  const [processos, setProcessos] = useState<Processo_Interface[]>([]);
  const [estimativasMerito, setEstimativasMerito] = useState<EstimativaMeritoData | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [erro, setErro] = useState<string>('');
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [totalPages, setTotalPages] = useState<number>(0);
  const [totalProcessos, setTotalProcessos] = useState<number>(0);
  const [isLoadingMore, setIsLoadingMore] = useState<boolean>(false);
  const [processoAbertoId, setProcessoAbertoId] = useState<string | null>(null);

  const fetchProcessos = useCallback(async (identificador: string, page: number, isInitialLoad: boolean) => {
    if (isInitialLoad) {
      setLoading(true);
      setProcessos([]);
      setCurrentPage(1);
    } else {
      setIsLoadingMore(true);
    }
    
    try {
      const response = await fetch(`/api/cliente/processos?identificador=${identificador}&page=${page}&pageSize=${PAGE_SIZE}`);
      if (!response.ok) {
        throw new Error('Erro ao buscar processos');
      }
      const data = await response.json();
      
      setProcessos(prevProcessos => isInitialLoad ? data.processos as Processo_Interface[] : [...prevProcessos, ...data.processos as Processo_Interface[]]);
      setCurrentPage(data.currentPage);
      setTotalPages(data.totalPages);
      setTotalProcessos(data.totalProcessos);

    } catch (error) {
      console.error('Erro ao buscar processos:', error);
      setErro(isInitialLoad ? 'Não foi possível carregar seus processos. Tente novamente mais tarde.' : 'Erro ao carregar mais processos.');
    } finally {
      if (isInitialLoad) setLoading(false);
      else setIsLoadingMore(false);
    }
  }, []);

  useEffect(() => {
    const fetchClienteData = async () => {
      setErro('');
      setLoading(true); // Garante que o loading seja true no início desta função
      try {
        const response = await fetch('/api/cliente/me');
        if (!response.ok) {
          if (response.status === 401) {
             router.push('/');
             return;
          }
          throw new Error('Falha ao buscar dados do cliente');
        }
        const data = await response.json();
        const clienteData = data.cliente as Cliente_Interface;

        // Busca das estimativas de mérito
        try {
            const estimativasResponse = await fetch('/api/estimativas/merito');
            if (estimativasResponse.ok) {
                const estimativasData = await estimativasResponse.json();
                setEstimativasMerito(estimativasData as EstimativaMeritoData | null);
            } else {
                console.warn('Falha ao buscar estimativas de mérito. A timeline usará valores padrão.');
                setEstimativasMerito(null); // Continua sem estimativas se a busca falhar
            }
        } catch (estimativaError) {
            console.warn('Erro ao buscar estimativas de mérito:', estimativaError);
            setEstimativasMerito(null); // Continua sem estimativas em caso de erro
        }
        
        if (clienteData && clienteData.identificador) {
            setCliente(clienteData);
            // fetchProcessos será chamado após o cliente e as estimativas (opcionalmente) serem setados
            // para garantir que ambos estejam disponíveis se fetchProcessos depender deles (embora não dependa diretamente)
            await fetchProcessos(clienteData.identificador, 1, true);
        } else {
            throw new Error('Dados do cliente inválidos recebidos da API');
        }
      } catch (error: any) {
        console.error('Erro ao buscar dados do cliente via API /me ou processos iniciais:', error);
        setErro(error.message || 'Erro ao carregar dados do cliente. Tente logar novamente.');
      } finally {
        setLoading(false); // Só desliga o loading principal após todas as buscas iniciais (cliente, estimativas, processos)
      }
    };
    fetchClienteData();
  }, [router, fetchProcessos]);

  useEffect(() => {
    const handleScroll = () => {
      if (
        window.innerHeight + document.documentElement.scrollTop >= document.documentElement.offsetHeight - 200 &&
        !isLoadingMore &&
        cliente && cliente.identificador &&
        currentPage < totalPages
      ) {
        fetchProcessos(cliente.identificador, currentPage + 1, false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [isLoadingMore, currentPage, totalPages, cliente, fetchProcessos]);

  const handleLogout = () => {
    localStorage.removeItem('cliente');
    sessionStorage.removeItem('cliente');
    localStorage.removeItem('manterConectado');
    router.push('/');
  };

  const formatDate = (date: Date | string | null) => {
    if (!date) return 'N/A';
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return dateObj.toLocaleDateString('pt-BR', { day: '2-digit', month: '2-digit', year: 'numeric' });
  };

  if (loading && processos.length === 0) {
    return (
      <div className="fixed inset-0 flex items-center justify-center bg-white bg-opacity-80">
        <div className="text-center">
          <div className="mb-4">
            <svg className="animate-spin h-12 w-12 mx-auto text-green-500" viewBox="0 0 24 24">
              <circle
                className="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                strokeWidth="4"
              ></circle>
              <path
                className="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              ></path>
            </svg>
          </div>
          <p className="text-gray-800 font-medium">Carregando seus dados...</p>
        </div>
      </div>
    );
  }

  if (erro && processos.length === 0) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100 px-4">
        <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full">
          <div className="text-red-600 mb-4 text-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
            </svg>
          </div>
          <h2 className="text-xl font-bold text-gray-800 mb-2 text-center">Erro ao carregar dados</h2>
          <p className="text-center text-gray-600 mb-6">{erro}</p>
          <button
            onClick={() => window.location.reload()}
            className="w-full bg-green-500 hover:bg-green-600 text-white font-bold py-2 px-4 rounded-md transition duration-300 ease-in-out"
          >
            Tentar novamente
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#F5F5F5]">
      <header className="bg-green-500 shadow-md">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 flex justify-between items-center">
          <div>
            <Image
              src="/logo.svg"
              alt="Logo"
              width={150}
              height={40}
              className="h-10 w-auto"
            />
          </div>
          <button
            onClick={handleLogout}
            className="text-white hover:text-green-100 font-medium"
          >
            Sair
          </button>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-1 sm:px-6 lg:px-8 py-8">
        <div className="mb-8 text-center">
          <h1 className="text-3xl font-bold text-gray-800 mb-2">
            Olá, {cliente?.nome || "Cliente"}!
          </h1>
          <p className="text-gray-600">
            Monitore o andamento dos seus processos de registro.
          </p>
        </div>

        {processos.length > 0 && (
            <p className="text-center text-sm text-gray-600 mb-4">
                Mostrando {processos.length} de {totalProcessos} processos totais.
            </p>
        )}

        <div className="space-y-4">
          {processos.map((processo: Processo_Interface) => {
            const processoParaCard = {
              ...processo,
              dataPublicacaoRPI: processo.dataPublicacaoRPI === undefined ? null : processo.dataPublicacaoRPI,
              dataMeritoEstimada: processo.dataMeritoEstimada === undefined ? null : processo.dataMeritoEstimada,
              dataOposicao: processo.dataOposicao === undefined ? null : processo.dataOposicao,
              dataConcessao: processo.dataConcessao === undefined ? null : processo.dataConcessao,
              dataVigencia: processo.dataVigencia === undefined ? null : processo.dataVigencia,
              Marca: processo.Marca === undefined ? null : processo.Marca,
              oposicao: processo.oposicao === undefined ? false : processo.oposicao,
              logoUrl: processo.logoUrl === undefined ? null : processo.logoUrl,
              Cliente: processo.Cliente === undefined ? undefined : processo.Cliente,
              taxaConcessaoPaga: processo.taxaConcessaoPaga === undefined ? false : processo.taxaConcessaoPaga,
              Despacho: processo.Despacho.map(d => ({
                ...d,
                RPI: d.RPI === undefined ? null : d.RPI,
                DetalhesDespacho: d.DetalhesDespacho === undefined ? null : d.DetalhesDespacho,
                ProtocoloDespacho: d.ProtocoloDespacho === undefined ? undefined : d.ProtocoloDespacho.map(pd => ({...pd})),
                textoComplementar: d.textoComplementar === undefined ? null : d.textoComplementar,
              })),
            };

            const timelineGerada = gerarTimelineSimplificada(processo, estimativasMerito);
            
            const isAberto = processoAbertoId === processo.id;
            
            return (
              <div key={processo.id} className="bg-white shadow-lg rounded-lg overflow-hidden">
                <button
                  onClick={() => setProcessoAbertoId(isAberto ? null : processo.id)}
                  className="w-full text-left p-4 bg-gray-50 hover:bg-gray-100 focus:outline-none"
                >
                <ProcessoCard processo={processoParaCard as any} />
                  <span className="text-sm text-blue-600 hover:text-blue-800 mt-2 inline-block">
                    {isAberto ? 'Fechar Detalhes' : 'Ver Detalhes e Timeline'}
                  </span>
                </button>

                {isAberto && (
                  <div className="p-6">
                    <div className="mt-2 mb-6">
                      <h3 className="text-lg font-semibold text-gray-700 mb-3">Despachos do Processo</h3>
                      {processo.Despacho && processo.Despacho.length > 0 ? (
                        <ul className="space-y-2 text-sm">
                          {processo.Despacho.map((desp, index) => (
                            <li key={`${desp.codigo}-${index}`} className="p-2 border rounded-md bg-gray-50">
                              <p className="font-medium text-gray-800">
                                {desp.DetalhesDespacho?.nome || desp.nome || "Nome do despacho não disponível"}
                              </p>
                              {desp.RPI && (
                                <p className="text-xs text-gray-500">
                                  RPI: {desp.RPI.numero} ({desp.RPI.dataPublicacao})
                                </p>
                              )}
                              {desp.ProtocoloDespacho && desp.ProtocoloDespacho.length > 0 && desp.ProtocoloDespacho[0].codigoServico && (
                                <p className="text-xs text-gray-500">
                                  Protocolo (Cód. Serviço): {desp.ProtocoloDespacho[0].codigoServico}
                                </p>
                              )}
                               {desp.textoComplementar && (
                                <p className="text-xs text-gray-600 mt-1 border-t pt-1">
                                  {desp.textoComplementar}
                                </p>
                              )}
                            </li>
                          ))}
                        </ul>
                      ) : (
                        <p className="text-sm text-gray-500">
                          Nenhum despacho disponível para este processo.
                        </p>
                      )}
                    </div>

                  <h3 className="text-xl font-semibold text-gray-700 mb-4">Linha do Tempo do Processo</h3>
                  {timelineGerada.length > 0 ? (
                    <TimelineDinamica etapas={timelineGerada} />
                  ) : (
                    <p className="text-gray-500">Nenhuma etapa da timeline pôde ser determinada para este processo.</p>
                  )}
                </div>
                )}
              </div>
            );
          })}
          
          {erro && processos.length > 0 && currentPage > 1 && (
            <div className="text-center py-4 text-red-600">
                <p>{erro}</p>
            </div>
          )}

          {isLoadingMore && (
            <div className="text-center py-10">
                <svg className="animate-spin h-8 w-8 mx-auto text-green-500" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <p className="mt-2 text-sm font-medium text-gray-900">Carregando mais processos...</p>
            </div>
          )}

          {!isLoadingMore && processos.length > 0 && currentPage >= totalPages && totalProcessos > 0 && (
            <div className="text-center py-10">
                <p className="text-sm text-gray-500">Todos os {totalProcessos} processos foram carregados.</p>
            </div>
          )}

          {!loading && processos.length === 0 && !erro && (
            <div className="text-center py-10">
              <svg xmlns="http://www.w3.org/2000/svg" className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
              </svg>
              <h3 className="mt-2 text-sm font-medium text-gray-900">Nenhum processo encontrado</h3>
              <p className="mt-1 text-sm text-gray-500">Parece que não há processos vinculados a este identificador.</p>
            </div>
          )}
        </div>
        
        <span className="text-[#A3A3A3] mt-8 text-sm text-center block">
          A previsão para a análise de mérito é estimada conforme o prazo atual
          do INPI.
        </span>
      </main>
    </div>
  );
} 