generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model CFE {
  id      String  @id
  marcaId String
  codigo  String?
  edicao  String?
  Marca   Marca   @relation(fields: [marcaId], references: [id], onDelete: Cascade)

  @@index([marcaId])
}

model Cliente {
  id                   Int                    @id @default(autoincrement())
  identificador        String?
  crmId                Int?                   @unique
  crmLeadIds           Int[]
  crmStageId           String?
  tempoNaEtapa         String?
  camposPersonalizados Json?
  nome                 String?
  tipoDeDocumento      String?
  numeroDocumento      String?
  nomeDaMarca          String?
  metodoComunicacao    metodoComunicacao?
  autoLoginUrl         String?
  ContatoCliente       ContatoCliente[]
  Processo             Processo[]
  ProtocoloDownloadLog ProtocoloDownloadLog[]
  SessionLog           SessionLog[]
  ShortUrl             ShortUrl[]
  Titular              Titular[]

  @@index([crmId])
  @@index([identificador])
}

model ComunicadoPrazoMerito {
  id                             String                           @id
  crmId                          Int
  processoId                     String
  dialogId                       String
  prazoEmMeses                   Int?
  dataEnvio                      DateTime                         @default(now())
  success                        Boolean                          @default(false)
  errorMessage                   String?
  createdAt                      DateTime                         @default(now())
  updatedAt                      DateTime
  detalhesErro                   Json?
  status                         ComunicadoStatus                 @default(PENDENTE)
  tentativas                     Int                              @default(0)
  ultimaTentativa                DateTime?
  Processo                       Processo                         @relation(fields: [processoId], references: [id])
  HistoricoComunicadoPrazoMerito HistoricoComunicadoPrazoMerito[]

  @@unique([crmId, processoId, dialogId])
  @@index([crmId])
  @@index([dataEnvio])
  @@index([processoId])
  @@index([status])
}

model ContatoCliente {
  id                 Int     @id @default(autoincrement())
  clienteId          Int
  endereco           String?
  telefone           String?
  telefoneSegundario String?
  email              String?
  cidade             String?
  estado             String?
  cep                String?
  Cliente            Cliente @relation(fields: [clienteId], references: [id])
}

model CrmProcessoControle {
  id         String   @id
  crmId      Int
  processoId String
  automove   Boolean  @default(false)
  blockmove  Boolean  @default(false)
  createdAt  DateTime @default(now())
  updatedAt  DateTime
  Processo   Processo @relation(fields: [processoId], references: [id])

  @@unique([crmId, processoId])
  @@index([crmId])
  @@index([processoId])
}

model Despacho {
  id                 String              @id
  processoId         String
  rpiId              String
  codigo             String
  nome               String?
  textoComplementar  String?
  detalhesDespachoId String?
  DetalhesDespacho   DetalhesDespacho?   @relation(fields: [detalhesDespachoId], references: [id])
  Processo           Processo            @relation(fields: [processoId], references: [id], onDelete: Cascade)
  RPI                RPI                 @relation(fields: [rpiId], references: [id], onDelete: Cascade)
  ProtocoloDespacho  ProtocoloDespacho[]

  @@index([codigo])
  @@index([nome])
  @@index([processoId])
  @@index([rpiId])
  @@index([textoComplementar(ops: raw("gin_trgm_ops"))], map: "idx_despacho_textocomplementar_trgm", type: Gin)
}

model DetalhesDespacho {
  id                String     @id
  codigo            String     @unique
  nome              String
  acaoCumprir       String?
  abreviacao        String?
  statusRelacionado String?
  comum             Boolean    @default(false)
  prazoACumprir     Float?
  notificado        Boolean    @default(false)
  cumprido          Boolean    @default(false)
  observacao        String?
  createdAt         DateTime   @default(now())
  updatedAt         DateTime
  Despacho          Despacho[]

  @@index([codigo])
  @@index([nome])
}

model HistoricoComunicadoPrazoMerito {
  id                      String                @id
  comunicadoPrazoMeritoId String
  dataTentativa           DateTime              @default(now())
  status                  ComunicadoStatus
  success                 Boolean
  errorMessage            String?
  detalhesErro            Json?
  createdAt               DateTime              @default(now())
  ComunicadoPrazoMerito   ComunicadoPrazoMerito @relation(fields: [comunicadoPrazoMeritoId], references: [id], onDelete: Cascade)

  @@index([comunicadoPrazoMeritoId])
  @@index([dataTentativa])
  @@index([status])
}

model Marca {
  id            String   @id
  processoId    String   @unique
  nome          String?
  apresentacao  String?
  natureza      String?
  logomarcaPath String?
  temLogomarca  Boolean  @default(false)
  CFE           CFE[]
  Processo      Processo @relation(fields: [processoId], references: [id], onDelete: Cascade)
  NCL           NCL[]

  @@index([natureza])
  @@index([nome])
  @@index([processoId])
}

model NCL {
  id                      String  @id
  marcaId                 String
  codigo                  String?
  especificacao           String?
  status                  String?
  edicao                  String?
  estadoDasEspecificacoes String?
  Marca                   Marca   @relation(fields: [marcaId], references: [id], onDelete: Cascade)

  @@index([marcaId])
}

model Processo {
  id                        String                   @id
  numero                    String                   @unique
  dataDeposito              DateTime?
  dataConcessao             DateTime?
  dataVigencia              DateTime?
  dataMerito                DateTime?
  diasCorridosMerito        Int?
  dataMeritoEstimada        DateTime?
  oposicao                  Boolean                  @default(false)
  dataOposicao              DateTime?
  sobrestamento             Boolean                  @default(false)
  dataSobrestamento         DateTime?
  exigencia                 Boolean                  @default(false)
  dataExigencia             DateTime?
  procuradorId              String?
  monitorado                Boolean                  @default(false)
  inicioVigencia            DateTime?
  prorrogacaoOrdinaria      String?
  prorrogacaoExtraordinaria String?
  apostila                  String?
  clienteId                 Int?
  marcaId                   String?
  createdAt                 DateTime                 @default(now())
  updatedAt                 DateTime
  procuradorMonitoradoId    String?
  diasAteMeritoEstimada     Int?
  dataElegibilidade         DateTime?
  elegivelParaComunicados   Boolean                  @default(false)
  taxaConcessaoPaga         Boolean                  @default(false)
  linkProtocolo             String?
  dataTaxaConcessaoPaga     DateTime?
  ComunicadoPrazoMerito     ComunicadoPrazoMerito[]
  CrmProcessoControle       CrmProcessoControle[]
  Despacho                  Despacho[]
  Marca                     Marca?
  Cliente                   Cliente?                 @relation(fields: [clienteId], references: [id])
  Procurador                Procurador?              @relation(fields: [procuradorId], references: [id])
  ProcuradorMonitorado      ProcuradorMonitorado?    @relation(fields: [procuradorMonitoradoId], references: [id])
  ProcessoInteresse         ProcessoInteresse?
  ProcessoScrapingControl   ProcessoScrapingControl?
  ProtocoloDownloadLog      ProtocoloDownloadLog[]
  Sobrestador               Sobrestador[]
  Titular                   Titular[]

  @@index([clienteId])
  @@index([dataConcessao])
  @@index([dataDeposito])
  @@index([dataMeritoEstimada])
  @@index([dataMerito])
  @@index([dataVigencia])
  @@index([marcaId])
  @@index([monitorado])
  @@index([numero])
  @@index([procuradorId])
  @@index([procuradorMonitoradoId])
}

model ProcessoInteresse {
  id          String   @id
  processoId  String   @unique
  interessado String?
  observacao  String?
  createdAt   DateTime @default(now())
  Processo    Processo @relation(fields: [processoId], references: [id], onDelete: Cascade)
}

model Procurador {
  id                String              @id
  nome              String              @unique
  Processo          Processo[]
  ProtocoloDespacho ProtocoloDespacho[]

  @@index([nome])
}

model ProcuradorMonitorado {
  id       String     @id
  nome     String     @unique
  Processo Processo[]
}

model ProtocoloDespacho {
  id                        String      @id
  despachoId                String
  numero                    String
  data                      DateTime
  codigoServico             String
  requerenteNomeRazaoSocial String
  requerentePais            String?
  requerenteUf              String?
  procuradorId              String?
  Despacho                  Despacho    @relation(fields: [despachoId], references: [id], onDelete: Cascade)
  Procurador                Procurador? @relation(fields: [procuradorId], references: [id])

  @@index([data])
  @@index([despachoId])
  @@index([numero])
}

model RPI {
  id                     String     @id
  numero                 Int        @unique
  dataPublicacao         DateTime
  pdfProcessado          Boolean    @default(false)
  xmlProcessado          Boolean    @default(false)
  createdAt              DateTime   @default(now())
  updatedAt              DateTime
  estimativasProcessadas Boolean    @default(false)
  Despacho               Despacho[]
  Titular                Titular[]

  @@index([dataPublicacao])
  @@index([numero])
}

model Sobrestador {
  id                   String   @id
  processoId           String
  referenciaProcessual String?
  marca                String?
  Processo             Processo @relation(fields: [processoId], references: [id], onDelete: Cascade)

  @@index([marca])
  @@index([processoId])
  @@index([referenciaProcessual])
}

model Titular {
  id              String   @id
  processoId      String
  nomeRazaoSocial String
  pais            String?
  uf              String?
  rpiId           String?
  clienteId       Int?
  numeroDocumento String?
  Cliente         Cliente? @relation(fields: [clienteId], references: [id])
  Processo        Processo @relation(fields: [processoId], references: [id], onDelete: Cascade)
  RPI             RPI?     @relation(fields: [rpiId], references: [id])

  @@index([nomeRazaoSocial])
  @@index([processoId])
  @@index([clienteId])
  @@index([numeroDocumento])
}

model ProcessoScrapingControl {
  id               String        @id
  processoId       String        @unique
  processoNumero   String
  stage            ScrapingStage
  isActive         Boolean       @default(true)
  lastScrapedAt    DateTime?
  nextScrapeDueAt  DateTime?
  stageEnteredAt   DateTime
  stageCompletedAt DateTime?
  failureCount     Int           @default(0)
  lastErrorMessage String?
  createdAt        DateTime      @default(now())
  updatedAt        DateTime
  Processo         Processo      @relation(fields: [processoId], references: [id], onDelete: Cascade)

  @@index([processoNumero])
  @@index([stage, isActive, nextScrapeDueAt])
}

model WebhookPayloadFalho {
  id                    String   @id
  tipoWebhook           String
  payload               Json
  mensagemErro          String
  dataFalha             DateTime @default(now())
  processadoManualmente Boolean  @default(false)

  @@index([dataFalha])
  @@index([processadoManualmente])
  @@index([tipoWebhook])
}

model EstimativaMeritoSalva {
  id                      String   @id
  dataCalculo             DateTime @default(now())
  mediaSemIntervencoes    Float?
  medianaSemIntervencoes  Float?
  minSemIntervencoes      Int?
  maxSemIntervencoes      Int?
  nSemIntervencoes        Int
  mediaComOposicao        Float?
  medianaComOposicao      Float?
  minComOposicao          Int?
  maxComOposicao          Int?
  nComOposicao            Int
  mediaComSobrestamento   Float?
  medianaComSobrestamento Float?
  minComSobrestamento     Int?
  maxComSobrestamento     Int?
  nComSobrestamento       Int
  mediaComExigencia       Float?
  medianaComExigencia     Float?
  minComExigencia         Int?
  maxComExigencia         Int?
  nComExigencia           Int
}

model SessionLog {
  id              String    @id @default(cuid())
  clienteId       Int
  identificador   String
  loginAt         DateTime  @default(now())
  logoutAt        DateTime?
  ipAddress       String?
  userAgent       String?
  isActive        Boolean   @default(true)
  sessionDuration Int?
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
  Cliente         Cliente   @relation(fields: [clienteId], references: [id], onDelete: Cascade)

  @@index([clienteId])
  @@index([loginAt])
  @@index([isActive])
  @@index([identificador])
}

model ProtocoloDownloadLog {
  id             String   @id @default(cuid())
  clienteId      Int
  processoId     String
  numeroProcesso String
  downloadAt     DateTime @default(now())
  ipAddress      String?
  userAgent      String?
  fileSize       Int?
  success        Boolean  @default(true)
  errorMessage   String?
  createdAt      DateTime @default(now())
  Cliente        Cliente  @relation(fields: [clienteId], references: [id], onDelete: Cascade)
  Processo       Processo @relation(fields: [processoId], references: [id], onDelete: Cascade)

  @@index([clienteId])
  @@index([processoId])
  @@index([downloadAt])
  @@index([numeroProcesso])
}

model ShortUrl {
  id         String    @id @default(uuid())
  shortCode  String    @unique
  longToken  String
  clienteId  Int?
  createdAt  DateTime  @default(now())
  expiresAt  DateTime?
  usageCount Int       @default(0)
  isActive   Boolean   @default(true)
  Cliente    Cliente?  @relation(fields: [clienteId], references: [id], onDelete: Cascade)

  @@index([clienteId])
  @@index([createdAt])
  @@index([isActive])
  @@index([shortCode])
}

enum ComunicadoStatus {
  PENDENTE
  EM_PROCESSAMENTO
  ENVIADO
  FALHA
  CANCELADO
}

enum ComunicadoTipo {
  CRM_MOVIMENTACAO
  DESPACHO
  ATUALIZACAO_PRAZO
}

enum metodoComunicacao {
  EMAIL
  WHATSAPP
}

enum ScrapingStage {
  AGUARDANDO_PUBLICACAO
  AGUARDANDO_FIM_PRAZO_OPOSICAO
  AGUARDANDO_PAGAMENTO_TAXA_CONCESSAO
  MONITORANDO_NULIDADE_POS_CONCESSAO
  MONITORANDO_POS_NOTIFICACAO_CADUCIDADE
  MONITORANDO_PERIODO_RENOVACAO
  AGUARDANDO_RECURSO_INDEFERIMENTO
  MONITORANDO_POS_RECURSO_PROVIDO
  MONITORANDO_POS_RECURSO_NEGADO
  CONCLUIDO_SEM_SCRAPING_ATIVO
}

enum AdminRole {
  MASTER
  COMUM
}

model AdminUser {
  id           String            @id @default(cuid())
  email        String            @unique
  nome         String
  senha        String
  role         AdminRole         @default(COMUM)
  ativo        Boolean           @default(true)
  ultimoLogin  DateTime?
  createdAt    DateTime          @default(now())
  updatedAt    DateTime          @updatedAt
  AdminSession AdminSession[]

  @@index([email])
  @@index([role])
  @@index([ativo])
}

model AdminSession {
  id           String    @id @default(cuid())
  adminUserId  String
  token        String    @unique
  expiresAt    DateTime
  persistent   Boolean   @default(false)
  ipAddress    String?
  userAgent    String?
  createdAt    DateTime  @default(now())
  AdminUser    AdminUser @relation(fields: [adminUserId], references: [id], onDelete: Cascade)

  @@index([token])
  @@index([adminUserId])
  @@index([expiresAt])
}
