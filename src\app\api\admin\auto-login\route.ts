import { NextRequest, NextResponse } from 'next/server';
import { generateAutoLoginLink, generateAutoLoginLinkFromClienteId } from '@/lib/autoLogin';
import { prisma } from '@/lib/prisma';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { clienteId, identificador, baseUrl } = body;

    // Validar entrada
    if (!clienteId && !identificador) {
      return NextResponse.json(
        { error: 'clienteId ou identificador é obrigatório' },
        { status: 400 }
      );
    }

    const finalBaseUrl = baseUrl || process.env.NEXTAUTH_URL || 'http://localhost:3000';

    let autoLoginLink: string | null = null;
    let cliente:any = null;

    // Se forneceu clienteId, usar função helper
    if (clienteId) {
      autoLoginLink = await generateAutoLoginLinkFromClienteId(clienteId, finalBaseUrl);
      
      // Buscar dados do cliente para resposta
      cliente = await prisma.cliente.findUnique({
        where: { id: clienteId },
        select: { id: true, nome: true, identificador: true }
      });
    } 
    // Se forneceu identificador, buscar cliente e gerar link
    else if (identificador) {
      cliente = await prisma.cliente.findFirst({
        where: { identificador },
        select: { 
          id: true, 
          nome: true, 
          identificador: true, 
          numeroDocumento: true 
        }
      });

      if (cliente && cliente.numeroDocumento) {
        // Extrair senha (últimos 3 dígitos do documento)
        const numeroLimpo = cliente.numeroDocumento.replace(/[\D]/g, '');
        const senha = numeroLimpo.slice(-3);
        
        autoLoginLink = generateAutoLoginLink(cliente.identificador!, senha, finalBaseUrl);
      }
    }

    if (!cliente) {
      return NextResponse.json(
        { error: 'Cliente não encontrado' },
        { status: 404 }
      );
    }

    if (!autoLoginLink) {
      return NextResponse.json(
        { error: 'Erro ao gerar link de auto-login - dados incompletos' },
        { status: 400 }
      );
    }

    // Opcionalmente, criar também uma versão short URL
    let shortUrl = null;
    if (body.createShortUrl !== false) { // Por padrão, criar short URL
      try {
        const { createAutoLoginShortUrl } = await import('@/lib/shortUrl');
        
        if (clienteId) {
          // Se forneceu clienteId, buscar identificador e senha
          const clienteData = await prisma.cliente.findUnique({
            where: { id: clienteId },
            select: { identificador: true, numeroDocumento: true }
          });
          
          if (clienteData?.identificador && clienteData?.numeroDocumento) {
            const numeroLimpo = clienteData.numeroDocumento.replace(/[\D]/g, '');
            const senha = numeroLimpo.slice(-3);
            
            shortUrl = await createAutoLoginShortUrl(
              clienteData.identificador,
              senha,
              finalBaseUrl,
              clienteId
            );
          }
        } else if (identificador && cliente?.numeroDocumento) {
          const numeroLimpo = cliente.numeroDocumento.replace(/[\D]/g, '');
          const senha = numeroLimpo.slice(-3);
          
          shortUrl = await createAutoLoginShortUrl(
            identificador,
            senha,
            finalBaseUrl,
            cliente.id
          );
        }
      } catch (shortUrlError) {
        console.warn('Erro ao criar short URL:', shortUrlError);
        // Não interrompe se a short URL falhar
      }
    }

    return NextResponse.json({
      autoLoginLink,
      shortUrl, // Novo campo
      cliente: {
        id: cliente.id,
        nome: cliente.nome,
        identificador: cliente.identificador
      },
      expiresIn: 'Nunca expira',
      usage: 'Link pode ser usado múltiplas vezes, válido permanentemente',
      shortUrlInfo: shortUrl ? 'Short URL criada para facilitar envio' : 'Short URL não criada'
    });

  } catch (error) {
    console.error('Erro ao gerar link de auto-login:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

// GET - Exemplo de como usar (opcional)
export async function GET() {
  return NextResponse.json({
    message: 'API para gerar links de auto-login',
    usage: {
      method: 'POST',
      body: {
        clienteId: 'number (opcional)',
        identificador: 'string (opcional)',
        baseUrl: 'string (opcional, usa NEXTAUTH_URL como padrão)'
      },
      examples: [
        {
          description: 'Gerar por ID do cliente',
          body: { clienteId: 123 }
        },
        {
          description: 'Gerar por identificador',
          body: { identificador: '12345678' }
        }
      ]
    }
  });
} 