// src/lib/appTypes.ts

// Interfaces migradas de page.tsx e adaptadas para uso geral

export interface RPI_Interface {
  dataPublicacao: string;
  numero: string;
}

export interface ProtocoloDespacho_Interface {
  numero: string;
  data: string;
  codigoServico: string;
  requerenteNomeRazaoSocial: string;
  // Adicionar outros campos do schema.prisma se necessário no futuro
  // requerentePais?: string | null;
  // requerenteUf?: string | null;
  // procuradorId?: string | null;
}

export interface DetalhesDespacho_Interface {
  nome: string | null;
  // Outros campos se houver
}

export interface Despacho_Interface {
  codigo: string;
  nome: string | null;
  RPI: RPI_Interface | null;
  ProtocoloDespacho?: ProtocoloDespacho_Interface[];
  DetalhesDespacho?: DetalhesDespacho_Interface | null;
  textoComplementar?: string | null;  
  titulo?: string | null;
  // Outros campos do schema.prisma se necessário no futuro
  // rpiId?: string;
  // processoId?: string;
}

export interface Marca_Interface { // Adicionando interface para Marca baseada em page.tsx
  nome: string | null;
  apresentacao: string | null;
  natureza: string | null;
  NCL: {
    codigo: string | null;
    especificacao: string | null;
  }[];
}

export interface Processo_Interface {
  id: string;
  numero: string;
  dataDeposito: string | null;
  dataPublicacaoRPI?: string | null; // Mantendo como estava em page.tsx
  dataMeritoEstimada?: string | null; // Mantendo
  dataOposicao?: string | null; // Mantendo
  dataConcessao?: string | null; // Mantendo
  dataVigencia?: string | null; // Mantendo
  Despacho: Despacho_Interface[];
  Marca?: Marca_Interface | null; // Adicionado de page.tsx
  oposicao?: boolean; // Adicionado de page.tsx
  logoUrl?: string | null; // Adicionado de page.tsx
  Cliente?: { crmStageId?: number }; // Adicionado de page.tsx
  taxaConcessaoPaga?: boolean; // Adicionado de page.tsx
}

// Interfaces de Cliente (migradas de page.tsx)
export interface ContatoCliente_Interface {
  email: string | null;
  telefone: string | null;
  endereco: string | null;
}

export interface Cliente_Interface {
  id: number;
  nome: string | null;
  identificador: string | null;
  numeroDocumento: string | null;
  ContatoCliente: ContatoCliente_Interface[];
} 