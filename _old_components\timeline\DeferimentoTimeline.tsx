import { format, addDays } from 'date-fns';
import { ptBR } from 'date-fns/locale';

interface DeferimentoTimelineProps {
  dataDeferimento: Date;
  taxaPaga: boolean; // Combinação de crmStageId === 280246 ou taxaConcessaoPaga === true
  isRecursoProvido: boolean; // Nova prop
}

export default function DeferimentoTimeline({
  dataDeferimento,
  taxaPaga,
  isRecursoProvido,
}: DeferimentoTimelineProps) {
  const hoje = new Date();
  
  // Cálculo das datas base
  const dataPrazoOrdinario = addDays(dataDeferimento, 60);
  const dataPrazoExtraordinario = addDays(dataPrazoOrdinario, 30);

  // Cálculo da data de concessão estimada
  const getDataConcessaoEstimada = () => {
    // Define a quantidade de dias para estimar baseado na origem do deferimento
    const estimativaDias = isRecursoProvido ? 42 : 47;

    if (taxaPaga) {
      if (hoje < dataPrazoOrdinario) {
        return addDays(dataPrazoOrdinario, estimativaDias);
      }
      return addDays(hoje, estimativaDias); // Conta a partir de hoje se pagou após prazo ord.
    }
    
    if (hoje > dataPrazoExtraordinario) {
      return null; 
    }

    // Se não está paga, conta a partir do prazo extraordinário
    return addDays(dataPrazoExtraordinario, estimativaDias);
  };

  const dataConcessaoEstimada = getDataConcessaoEstimada();
  const dataPrazoNulidade = dataConcessaoEstimada ? addDays(dataConcessaoEstimada, 180) : null;

  const getProgressoAtual = () => {
    const tHoje = hoje.getTime();
    if (tHoje < dataDeferimento.getTime()) return 0;

    const tDeferimento = dataDeferimento.getTime();
    const tPrazoOrdinario = dataPrazoOrdinario.getTime();
    const tPrazoExtraordinario = dataPrazoExtraordinario.getTime();
    
    if (!dataConcessaoEstimada) {
      if (tHoje >= tPrazoExtraordinario) return 30;
      if (tHoje >= tPrazoOrdinario) {
        return 20 + ((tHoje - tPrazoOrdinario) / (tPrazoExtraordinario - tPrazoOrdinario)) * 10;
      }
      return (tHoje - tDeferimento) / (tPrazoOrdinario - tDeferimento) * 20;
    }

    const tConcessao = dataConcessaoEstimada.getTime();
    const tNulidade = dataPrazoNulidade!.getTime();

    if (taxaPaga) {
      if (tHoje >= tNulidade) return 80;
      if (tHoje >= tConcessao) {
        return 45 + ((tHoje - tConcessao) / (tNulidade - tConcessao)) * 35;
      }
      if (tHoje >= tPrazoOrdinario) {
        return 20 + ((tHoje - tPrazoOrdinario) / (tConcessao - tPrazoOrdinario)) * 25;
      }
      return (tHoje - tDeferimento) / (tPrazoOrdinario - tDeferimento) * 20;
    } else {
      if (tHoje >= tPrazoExtraordinario) return 30;
      if (tHoje >= tPrazoOrdinario) {
        return 20 + ((tHoje - tPrazoOrdinario) / (tPrazoExtraordinario - tPrazoOrdinario)) * 10;
      }
      return (tHoje - tDeferimento) / (tPrazoOrdinario - tDeferimento) * 20;
    }
  };

  const getConcessaoPosition = () => {
    if (taxaPaga || !hoje || hoje < dataPrazoOrdinario) {
      return "35%"; // Mais próximo quando não tem prazo extraordinário
    }
    return "50%"; // Posição original quando tem prazo extraordinário
  };

  return (
    <div className="mt-8 mb-4">
      <div className="relative">
        {/* Container para datas (acima da linha) */}
        <div className="absolute w-full" style={{ bottom: "42px" }}>
          {/* Data Deferimento */}
          <div className="absolute left-0">
            <span className="text-[12px] font-medium text-[#A3A3A3] drop-shadow-sm">
              {format(dataDeferimento, "dd/MM/yyyy")}
            </span>
          </div>

          {/* Data Prazo Ordinário */}
          <div className="absolute left-[15%]">
            <span className="text-[12px] font-medium text-[#A3A3A3] drop-shadow-sm">
              {format(dataPrazoOrdinario, "dd/MM/yyyy")}
            </span>
          </div>

          {/* Data Prazo Extraordinário - só mostra se a taxa não foi paga E passou do prazo ordinário */}
          {!taxaPaga && hoje >= dataPrazoOrdinario && (
             <div className="absolute left-[30%]">
              <span className="text-[12px] font-medium text-[#A3A3A3] drop-shadow-sm">
                {format(dataPrazoExtraordinario, "dd/MM/yyyy")}
              </span>
            </div>
          )}

          {/* Data Concessão Estimada */}
          {dataConcessaoEstimada && (
            <div className="absolute" style={{ left: getConcessaoPosition() }}>
              <span className="text-[12px] font-medium text-[#A3A3A3] drop-shadow-sm">
                {format(dataConcessaoEstimada, "dd/MM/yyyy")}
                <em className="text-[10px] not-italic"> (est.)</em>
              </span>
            </div>
          )}

          {/* Data Prazo Nulidade */}
          {dataPrazoNulidade && (
            <div className="absolute left-[80%]">
              <span className="text-[12px] font-medium text-[#A3A3A3] drop-shadow-sm">
                {format(dataPrazoNulidade, "dd/MM/yyyy")}
              </span>
            </div>
          )}
        </div>

        {/* Linha do tempo e marcadores */}
        <div className="relative ml-2">
          {/* Barra de progresso */}
          <div className="relative">
            {/* Barra cinza de fundo */}
            <div className="h-[2px] bg-[#C3C3C3]" />
            
            {/* Barra azul de progresso */}
            <div
              className="absolute top-[-2.5px] left-0 h-[7px] bg-[#4597B5] transition-all duration-300"
              style={{ width: `${getProgressoAtual()}%` }}
            />
          </div>

          {/* Marcadores */}
          <div className="absolute w-full" style={{ top: "-10px" }}>
            {/* Deferimento */}
            <div className="absolute -left-2.5">
              <div className="w-5 h-5 rounded-full bg-[#4597B5] shadow-md flex items-center justify-center">
                <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2.5} d="M5 13l4 4L19 7" />
                </svg>
              </div>
            </div>

            {/* Prazo Ordinário */}
            <div className="absolute left-[15%]">
              <div className={`w-5 h-5 rounded-full shadow-md flex items-center justify-center ${
                taxaPaga ? 'bg-[#4597B5]' :
                hoje >= dataPrazoOrdinario ? 'border-2 border-[#FF0000] bg-white' :
                'border-2 border-[#C3C3C3] bg-white'
              }`}>
                {taxaPaga && (
                  <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2.5} d="M5 13l4 4L19 7" />
                  </svg>
                )}
              </div>
            </div>

            {/* Prazo Extraordinário */}
            {!taxaPaga && hoje >= dataPrazoOrdinario && (
              <div className="absolute left-[30%]">
                <div className={`w-5 h-5 rounded-full shadow-md flex items-center justify-center ${ 
                  hoje >= dataPrazoExtraordinario 
                   ? 'border-2 border-[#FF0000] bg-white'
                   : 'border-2 border-[#C3C3C3] bg-white'
                }`}>
                  {hoje >= dataPrazoExtraordinario && (
                    <span className="text-xs">X</span> 
                  )}
                </div>
              </div>
            )}

            {/* Concessão */}
            {dataConcessaoEstimada && (
              <div className="absolute" style={{ left: getConcessaoPosition() }}>
                <div className="w-5 h-5 rounded-full border-2 border-[#C3C3C3] bg-white shadow-md flex items-center justify-center">
                </div>
              </div>
            )}

            {/* Prazo Nulidade */}
            {dataPrazoNulidade && (
              <div className="absolute left-[80%]">
                <div className="w-5 h-5 rounded-full border-2 border-[#C3C3C3] bg-white shadow-md flex items-center justify-center">
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Container para nomes das etapas (abaixo da linha) */}
        <div className="absolute w-full" style={{ top: "20px" }}>
          {/* Nome Deferimento */}
          <div className="absolute left-0">
            <span className="text-[14px] font-semibold text-black drop-shadow-sm block">
              Deferimento
            </span>
          </div>

          {/* Nome Taxa de Concessão */}
          <div className="absolute left-[15%]">
            <div>
              <span className={`text-[14px] font-semibold ${taxaPaga ? 'text-black' : 'text-[#FF0000]'} block`}>
                Taxa de Concessão
              </span>
              {!taxaPaga && (
                <span className="text-[#969696] text-[14px] font-normal italic flex items-center gap-1">
                  Prazo ordinário
                </span>
              )}
            </div>
          </div>

          {/* Nome Prazo Extraordinário - só mostra se a taxa não foi paga E passou do prazo ordinário */}
          {!taxaPaga && hoje >= dataPrazoOrdinario && (
            <div className="absolute left-[30%]">
              <div>
                <span className={`text-[14px] font-semibold text-[#FF0000] block`}>
                  Taxa de Concessão
                </span>
                <span className="text-[#969696] text-[14px] font-normal italic flex items-center gap-1">
                  ⚠️ Prazo extraordinário
                </span>
              </div>
            </div>
          )}

          {/* Nome Concessão */}
          {dataConcessaoEstimada && (
            <div className="absolute" style={{ left: getConcessaoPosition() }}>
              <span className="text-[14px] font-semibold text-black drop-shadow-sm block">
                Concessão
              </span>
            </div>
          )}

          {/* Nome Prazo Nulidade */}
          {dataPrazoNulidade && (
            <div className="absolute left-[80%]">
              <span className="text-[14px] font-semibold text-black drop-shadow-sm text-right block">
                Fim do prazo de nulidade
              </span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
} 