import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ numero: string }> }
) {
  try {
    const resolvedParams = await params;
    const numero = resolvedParams.numero;
    
    if (!numero) {
      return NextResponse.json(
        { error: 'Número do processo não fornecido' },
        { status: 400 }
      );
    }

    // Busca o processo pelo número com todas as suas relações importantes
    const processo = await prisma.processo.findUnique({
      where: {
        numero,
      },
      include: {
        Marca: {
          include: {
            NCL: true,
          },
        },
        Despacho: {
          include: {
            RPI: true,
            DetalhesDespacho: true,
            ProtocoloDespacho: {
              include: {
                Procurador: true,
              },
            },
          },
        },
        Titular: true,
        Procurador: true,
        Cliente: true,
      },
    });

    if (!processo) {
      return NextResponse.json(
        { error: 'Processo não encontrado' },
        { status: 404 }
      );
    }

    return NextResponse.json(processo);
  } catch (error) {
    console.error('Erro ao buscar processo:', error);
    return NextResponse.json(
      { error: 'Erro interno ao buscar processo' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
} 