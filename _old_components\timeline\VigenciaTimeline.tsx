import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';

interface VigenciaTimelineProps {
  dataDeposito: Date;
  dataConcessao: Date;
  dataVigencia: Date; // Data final de vigência (renovação)
}

export default function VigenciaTimeline({
  dataDeposito,
  dataConcessao,
  dataVigencia,
}: VigenciaTimelineProps) {
  const hoje = new Date();
  
  // Define os pontos de referência visuais na timeline (%)
  const posConcessao = 25;
  const posRenovacao = 100; // Renovação (dataVigencia) no final

  // Calcula o progresso atual baseado nas datas e posições visuais
  const getProgressoAtual = () => {
    const tHoje = hoje.getTime();
    if (tHoje < dataDeposito.getTime()) return 0;

    const tDeposito = dataDeposito.getTime();
    const tConcessao = dataConcessao.getTime();
    const tVigencia = dataVigencia.getTime(); // Usando dataVigencia como ponto final
    
    // Função para interpolação linear
    const interpolate = (startT: number, endT: number, startP: number, endP: number) => {
      if (tHoje < startT) return startP;
      if (tHoje >= endT) return endP;
      if (endT <= startT) return startP;
      const progressRatio = (tHoje - startT) / (endT - startT);
      return startP + progressRatio * (endP - startP);
    };

    // Determina em qual segmento da timeline estamos
    if (tHoje >= tVigencia) return posRenovacao;
    if (tHoje >= tConcessao) {
      return interpolate(tConcessao, tVigencia, posConcessao, posRenovacao);
    }
    return interpolate(tDeposito, tConcessao, 0, posConcessao);
  };

  return (
    <div className="mt-8 mb-4">
      <div className="relative">
        {/* Container para datas (acima da linha) */}
        <div className="absolute w-full" style={{ bottom: "42px" }}>
          {/* Data Protocolo */}
          <div className="absolute left-0">
            <span className="text-[12px] font-medium text-[#A3A3A3] drop-shadow-sm">
              {format(dataDeposito, "dd/MM/yyyy")}
            </span>
          </div>

          {/* Data Concessão */}
          <div className="absolute" style={{ left: `${posConcessao}%`, transform: 'translateX(-50%)' }}>
            <span className="text-[12px] font-medium text-[#A3A3A3] drop-shadow-sm">
              {format(dataConcessao, "dd/MM/yyyy")}
            </span>
          </div>

          {/* Data Renovação (Vigência) */}
          <div className="absolute right-0 text-right">
            <span className="text-[12px] font-medium text-[#A3A3A3] drop-shadow-sm">
              {format(dataVigencia, "dd/MM/yyyy")}
            </span>
          </div>
        </div>

        {/* Linha do tempo e marcadores */}
        <div className="relative ml-2">
          {/* Barra de progresso */}
          <div className="relative">
            {/* Barra cinza de fundo */}
            <div className="h-[2px] bg-[#C3C3C3]" />
            
            {/* Barra azul de progresso */}
            <div
              className="absolute top-[-2.5px] left-0 h-[7px] bg-[#4597B5] transition-all duration-300"
              style={{ width: `${getProgressoAtual()}%` }}
            />
          </div>

          {/* Marcadores */}
          <div className="absolute w-full" style={{ top: "-10px" }}>
            {/* Protocolo */}
            <div className="absolute -left-2.5">
              <div className="w-5 h-5 rounded-full bg-[#4597B5] shadow-md flex items-center justify-center">
                <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2.5} d="M5 13l4 4L19 7" />
                </svg>
              </div>
            </div>

            {/* Concessão */}
            <div className="absolute" style={{ left: `${posConcessao}%`, transform: 'translateX(-50%)' }}>
              <div className="w-5 h-5 rounded-full bg-[#4597B5] shadow-md flex items-center justify-center">
                <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2.5} d="M5 13l4 4L19 7" />
                </svg>
              </div>
            </div>

            {/* Renovação (Vigência) - Sempre cinza/branco */}
            <div className="absolute right-0">
              <div className={`w-5 h-5 rounded-full shadow-md flex items-center justify-center border-2 border-[#C3C3C3] bg-white`}>
                {/* Nenhum check aqui, representa prazo futuro */}
              </div>
            </div>
          </div>
        </div>

        {/* Container para nomes das etapas (abaixo da linha) */}
        <div className="absolute w-full" style={{ top: "20px" }}>
          {/* Nome Protocolo */}
          <div className="absolute left-0">
            <span className="text-[14px] font-semibold text-black drop-shadow-sm block">
              Protocolo
            </span>
          </div>

          {/* Nome Concessão */}
          <div className="absolute" style={{ left: `${posConcessao}%`, transform: 'translateX(-50%)' }}>
            <span className="text-[14px] font-semibold text-black drop-shadow-sm block text-center">
              Concessão
            </span>
          </div>

          {/* Nome Renovação */}
          <div className="absolute right-0">
            <span className="text-[14px] font-semibold text-black drop-shadow-sm text-right block">
              Renovação
            </span>
          </div>
        </div>
      </div>
    </div>
  );
} 