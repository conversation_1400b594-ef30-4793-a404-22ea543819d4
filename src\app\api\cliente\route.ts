import { NextResponse } from 'next/server';

// Usuários de exemplo (em um cenário real, isso seria um banco de dados)
const usuarios = [
  {
    identificador: '12345678',
    senha: 'senha123',
    nome: '<PERSON>',
    email: '<EMAIL>',
    telefone: '(11) 91234-5678',
    endereco: 'Rua Exemplo, 123 - São Paulo, SP'
  },
  {
    identificador: '87654321',
    senha: 'senha456',
    nome: '<PERSON>',
    email: '<EMAIL>',
    telefone: '(11) 98765-4321',
    endereco: 'Av. Modelo, 456 - São Paulo, SP'
  }
];

export async function GET(request: Request) {
  try {
    // Em um cenário real, você validaria o token JWT
    const authorization = request.headers.get('authorization');
    
    if (!authorization || !authorization.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Token não fornecido' },
        { status: 401 }
      );
    }
    
    const token = authorization.split(' ')[1];
    
    // Validação de token simplificada (apenas para demonstração)
    // Em um cenário real, você verificaria a assinatura JWT
    if (!token || !token.startsWith('token-')) {
      return NextResponse.json(
        { error: 'Token inválido' },
        { status: 401 }
      );
    }
    
    const identificadorNoToken = token.split('-')[1];
    
    const usuario = usuarios.find(u => u.identificador === identificadorNoToken);
    
    if (!usuario) {
      return NextResponse.json(
        { error: 'Usuário não encontrado' },
        { status: 404 }
      );
    }
    
    // Nunca retorne informações sensíveis como senhas
    const { senha, ...dadosUsuario } = usuario;
    
    return NextResponse.json(dadosUsuario, { status: 200 });
    
  } catch (error) {
    console.error('Erro ao buscar dados do cliente:', error);
    return NextResponse.json(
      { error: 'Erro no servidor' },
      { status: 500 }
    );
  }
} 