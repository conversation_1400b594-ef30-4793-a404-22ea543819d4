import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import jwt from 'jsonwebtoken';
// import { cookies } from 'next/headers'; // Não é necessário para SETAR cookies na resposta

// Função para extrair IP do cliente
function getClientIP(request: Request): string {
  const forwarded = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');
  const cfConnectingIP = request.headers.get('cf-connecting-ip');
  
  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }
  if (realIP) {
    return realIP;
  }
  if (cfConnectingIP) {
    return cfConnectingIP;
  }
  
  return 'unknown';
}

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { identificador, senha } = body;

    if (!identificador || !senha) {
      return NextResponse.json(
        { error: 'Identificador e senha são obrigatórios' },
        { status: 400 }
      );
    }

    // 1. Buscar todos os clientes com o identificador fornecido
    const clientes = await prisma.cliente.findMany({
      where: {
        identificador
      },
      include: {
        ContatoCliente: true, // Incluir se for necessário no payload do token ou para alguma lógica
        // Processo: { include: { Marca: true } } // Pode ser removido se não usado no login
      }
    });

    if (!clientes || clientes.length === 0) {
      return NextResponse.json(
        { error: 'Identificador não encontrado' },
        { status: 401 }
      );
    }

    let clienteAutenticado = null;

    // 2. Iterar sobre os clientes encontrados
    for (const cliente of clientes) {
      if (cliente.numeroDocumento) {
        const numeroDocumentoLimpo = cliente.numeroDocumento.replace(/[\D]/g, ''); // Remove não dígitos
        const ultimos3Digitos = numeroDocumentoLimpo.slice(-3);

        if (senha === ultimos3Digitos) {
          clienteAutenticado = cliente; // Cliente encontrado e senha corresponde
          break; // Interrompe o loop assim que encontrar um cliente válido
        }
      }
    }

    if (!clienteAutenticado) {
      return NextResponse.json(
        { error: 'Senha incorreta ou documento não cadastrado para este identificador' },
        { status: 401 }
      );
    }

    // --- Geração do JWT --- 
    const secret = process.env.JWT_SECRET;
    if (!secret) {
      console.error('JWT_SECRET não está definida nas variáveis de ambiente.');
      return NextResponse.json(
        { error: 'Erro interno do servidor - configuração' },
        { status: 500 }
      );
    }

    const tokenPayload = {
      clientId: clienteAutenticado.id,
      identificador: clienteAutenticado.identificador,
      nome: clienteAutenticado.nome, // Adicionando o nome ao payload
      // Adicione outras informações não sensíveis que podem ser úteis
    };

    // ✅ NOVO: Token de longa duração para sessão persistente
    const token = jwt.sign(tokenPayload, secret, {
      expiresIn: '365d', // Token expira em 1 ano para sessão persistente
    });

    // --- Cria a resposta com dados para sessão persistente --- 
    const response = NextResponse.json({
      message: 'Login realizado com sucesso',
      cliente: { // Retornar dados completos do cliente para localStorage
        id: clienteAutenticado.id,
        nome: clienteAutenticado.nome,
        identificador: clienteAutenticado.identificador,
        email: clienteAutenticado.ContatoCliente?.[0]?.email || null,
        telefone: clienteAutenticado.ContatoCliente?.[0]?.telefone || null,
      },
      sessionPersistence: true // Indica ao frontend para salvar no localStorage
    });

    // ✅ NOVO: Registrar sessão de login
    try {
      const ipAddress = getClientIP(request);
      const userAgent = request.headers.get('user-agent') || null;

      // Marcar sessões anteriores como inativas para este cliente
      await prisma.sessionLog.updateMany({
        where: {
          clienteId: clienteAutenticado.id,
          isActive: true,
        },
        data: {
          isActive: false,
          logoutAt: new Date(),
        },
      });

      // Criar nova sessão
      await prisma.sessionLog.create({
        data: {
          clienteId: clienteAutenticado.id,
          identificador: clienteAutenticado.identificador!,
          ipAddress,
          userAgent,
          isActive: true,
        },
      });

      console.log(`Nova sessão registrada: Cliente ${clienteAutenticado.id}, IP ${ipAddress}`);
    } catch (sessionError) {
      console.warn('Erro ao registrar sessão:', sessionError);
      // Não interrompe o login se o tracking falhar
    }

    // --- Configuração do Cookie na Resposta (Sessão Persistente) --- 
    response.cookies.set('auth_token', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      path: '/',
      sameSite: 'strict',
      maxAge: 60 * 60 * 24 * 365, // 1 ano em segundos (sessão persistente)
    });

    // --- Retorna a Resposta com o Cookie --- 
    return response;
    
  } catch (error) {
    console.error('Erro no login:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
} 