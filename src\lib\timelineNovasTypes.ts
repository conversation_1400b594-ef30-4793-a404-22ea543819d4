import { Processo_Interface, Despacho_Interface } from "./appTypes"; // Importar o tipo canônico
import { EtapaDefinicao } from "./etapasConfig";

// ProcessoParaTimeline agora é basicamente um alias ou pode ser Processo_Interface diretamente,
// se Processo_Interface já tiver todos os campos necessários (como dataDeposito).
// A interface Processo_Interface já inclui dataDeposito.
export type ProcessoParaTimeline = Processo_Interface;

export type StatusEtapaGerada = 
  | 'CONCLUIDA' 
  | 'ATUAL' 
  | 'PROXIMA_ESTIMADA' 
  | 'PROXIMA_EXATA' 
  | 'FUTURA_PENDENTE' // Para etapas que dependem de uma ação ainda não realizada
  | 'ALERTA_ACAO';    // Para coisas como "Cumprir exigência"

// Nova interface para informações sobre exigências ativas
export interface ExigenciaInfo {
  dataDespachoExigencia: Date;
  dataLimiteCumprimento: Date;
  // nomeDespachoExigencia?: string; // Opcional, se precisarmos exibir
  // definicaoEtapaAlerta?: EtapaDefinicao; // Opcional, se precisarmos da definição do alerta
  codigoServicoProtocoloOriginador?: string; // Código do serviço do protocolo que originou a exigência
  cumprida?: boolean; // Adicionado para rastrear se a exigência foi cumprida
  dataCumprimento?: Date; // Adicionado para rastrear a data de cumprimento
  dataNaoCumprimento?: Date; // Adicionado para rastrear a data de não cumprimento
}

export interface EtapaTimelineGerada {
  idOriginal: string; // ID da EtapaDefinicao original
  etapaDefinicao: EtapaDefinicao; // A definição completa da etapa de etapasConfig
  status: StatusEtapaGerada;
  dataInicio: Date | null; // Pode ser null para PROXIMA_ESTIMADA sem data de início clara
  dataFim?: Date | null; // Para prazos ou datas estimadas de conclusão
  // Poderíamos adicionar observações específicas geradas pela lógica, se necessário
  observacaoGerada?: string;
  exigenciaInfo?: ExigenciaInfo | null; // Adicionado para carregar infos da exigência ativa
}

// A definição de interface abaixo para ProcessoParaTimeline foi removida para evitar duplicidade.
// A definição type ProcessoParaTimeline = Processo_Interface; acima será usada. 