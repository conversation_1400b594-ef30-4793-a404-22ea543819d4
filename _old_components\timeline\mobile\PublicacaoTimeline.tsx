import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';

interface PublicacaoTimelineProps {
  dataDeposito: Date | null;
  dataPublicacao: Date | null;
  dataFimOposicao: Date | null;
  dataMeritoEstimada: Date | null;
  temDespachoMerito?: boolean;
  temNotificacaoOposicao?: boolean;
}

interface ProgressoInfo {
  etapaAtual: number; // 0: Dep->Pub, 1: Pub->Opo, 2: Opo->Merito, 3: Merito ou além
  progressoNaEtapaAtual: number; // 0 a 1
}

// Função auxiliar para verificar se um marcador está completo
function calcularSeMarcadorCompleto(index: number, progInfo: ProgressoInfo): boolean {
  // Marcador completo se for de etapa anterior, ou se for da etapa atual E o progresso for 100%, ou se já passou da etapa de mérito
  return index < progInfo.etapaAtual || 
         (index === progInfo.etapaAtual && progInfo.progressoNaEtapaAtual === 1) || 
         progInfo.etapaAtual >= 3; 
}

export default function PublicacaoTimeline({
  dataDeposito,
  dataPublicacao,
  dataFimOposicao,
  dataMeritoEstimada,
  temDespachoMerito = false,
  temNotificacaoOposicao = false
}: PublicacaoTimelineProps) {
  const hoje = new Date();
  const hojeMs = hoje.getTime();

  // --- Função para Calcular Progresso Detalhado ---
  const calcularProgresso = (): ProgressoInfo => {
    if (!dataDeposito) return { etapaAtual: -1, progressoNaEtapaAtual: 0 };

    const dataDepositoMs = dataDeposito.getTime();
    // Considerar data de publicação apenas se for maior ou igual ao depósito
    const dataPublicacaoMs = dataPublicacao && dataPublicacao.getTime() >= dataDepositoMs ? dataPublicacao.getTime() : null;
    // Considerar data de fim de oposição apenas se for maior ou igual à publicação
    const dataFimOposicaoMs = dataPublicacaoMs && dataFimOposicao && dataFimOposicao.getTime() >= dataPublicacaoMs ? dataFimOposicao.getTime() : null;
    // Considerar data de mérito apenas se for maior ou igual ao fim da oposição
    const dataMeritoEstimadaMs = dataFimOposicaoMs && dataMeritoEstimada && dataMeritoEstimada.getTime() >= dataFimOposicaoMs ? dataMeritoEstimada.getTime() : null;

    // 0. Antes do Depósito
    if (hojeMs < dataDepositoMs) {
      return { etapaAtual: -1, progressoNaEtapaAtual: 0 };
    }

    // 1. Etapa Depósito -> Publicação
    if (!dataPublicacaoMs || hojeMs < dataPublicacaoMs) {
      const inicioEtapa = dataDepositoMs;
      const fimEtapa = dataPublicacaoMs; // Pode ser null
      let progresso = 0;
      if(fimEtapa) {
        const duracaoEtapa = fimEtapa - inicioEtapa;
        const tempoDecorrido = hojeMs - inicioEtapa;
        progresso = duracaoEtapa > 0 ? Math.max(0, Math.min(1, tempoDecorrido / duracaoEtapa)) : 0;
      } else {
        progresso = 0; // Sem data fim, progresso é 0 nesta etapa
      }
      return { etapaAtual: 0, progressoNaEtapaAtual: progresso };
    }

    // 2. Etapa Publicação -> Fim Oposição
    if (!dataFimOposicaoMs || hojeMs < dataFimOposicaoMs) {
      const inicioEtapa = dataPublicacaoMs;
      const fimEtapa = dataFimOposicaoMs; // Pode ser null
      let progresso = 0;
       if(fimEtapa) {
        const duracaoEtapa = fimEtapa - inicioEtapa;
        const tempoDecorrido = hojeMs - inicioEtapa;
        progresso = duracaoEtapa > 0 ? Math.max(0, Math.min(1, tempoDecorrido / duracaoEtapa)) : 0;
      } else {
        progresso = 0; // Sem data fim, progresso é 0 nesta etapa
      }
      return { etapaAtual: 1, progressoNaEtapaAtual: progresso };
    }

    // 3. Etapa Fim Oposição -> Mérito
    if (temDespachoMerito) { // Mérito concluído
      return { etapaAtual: 3, progressoNaEtapaAtual: 1 };
    }
    if (!dataMeritoEstimadaMs || hojeMs < dataMeritoEstimadaMs) {
      const inicioEtapa = dataFimOposicaoMs;
      const fimEtapa = dataMeritoEstimadaMs; // Pode ser null
      let progresso = 0;
       if(fimEtapa) {
        const duracaoEtapa = fimEtapa - inicioEtapa;
        const tempoDecorrido = hojeMs - inicioEtapa;
        progresso = duracaoEtapa > 0 ? Math.max(0, Math.min(1, tempoDecorrido / duracaoEtapa)) : 0;
      } else {
         progresso = 0; // Sem data fim, progresso é 0 nesta etapa
      }
      return { etapaAtual: 2, progressoNaEtapaAtual: progresso };
    }

    // 4. Passou da data de mérito estimada, mas sem despacho
    // Consideramos a etapa 2 como 100% completa neste caso
    return { etapaAtual: 2, progressoNaEtapaAtual: 1 };
  };

  const progressoInfo = calcularProgresso();

  // --- Componente Interno TimelineMarker --- 
  const TimelineMarker = ({ 
    data, 
    titulo, 
    descricao, 
    isPrevisualizacao = false,
    index, // Índice do marcador (0 a 3)
    isUltimoMarcador // Flag para não desenhar linha após o último
  }: { 
    data: Date | null, 
    titulo: string, 
    descricao: string,
    isPrevisualizacao?: boolean,
    index: number,
    isUltimoMarcador: boolean,
    temDespachoMerito: boolean
  }) => {

    // REVISÃO LÓGICA COMPLETUDE DO MARCADOR (para alinhar com Desktop)
    let isMarkerCompleted: boolean;
    if (index === 3) {
      // Marcador de Mérito (index 3) completo se temDespachoMerito for true
      isMarkerCompleted = temDespachoMerito; 
    } else {
      // Outros marcadores completos se a data passou
      isMarkerCompleted = data ? hojeMs >= data.getTime() : false;
    }

    // Lógica da LINHA AZUL permanece baseada no progresso detalhado
    const showBlueLine = index < progressoInfo.etapaAtual || (index === progressoInfo.etapaAtual && progressoInfo.progressoNaEtapaAtual > 0);
    const blueLineHeightPercent = index < progressoInfo.etapaAtual ? 100 : progressoInfo.progressoNaEtapaAtual * 100;

    return (
      <div className="flex relative"> 
        {/* Data (lado esquerdo) */}
        <div className="w-24 flex-shrink-0 mr-4 text-right">
          {data && (
            <span className="text-sm font-medium text-[#A3A3A3]">
              {format(data, "dd/MM/yyyy")}
            </span>
          )}
          {isPrevisualizacao && dataMeritoEstimada && (
            <div className="flex flex-col items-end mt-0.5"> {/* Ajuste de margem */}
              <span className="text-xs font-medium italic text-[#45B063]">
                Previsão
              </span>
              <span className="text-xs font-bold italic text-[#45B063]">
                {format(dataMeritoEstimada, "MMM, yyyy", {
                  locale: ptBR,
                }).replace(".", "").replace(/^\w/, c => c.toUpperCase())}
              </span>
            </div>
          )}
        </div>

        {/* Marcador e Linha de Conexão (Centro) */}
        <div className="flex flex-col items-center w-5 relative"> {/* Container relativo para linhas */} 

          {/* Marcador */}
          <div className={`w-5 h-5 rounded-full flex items-center justify-center ${ 
            isMarkerCompleted
              ? "bg-[#4597B5] shadow-md border-2 border-[#4597B5]" // Azul com borda azul
              : "border-2 border-[#C3C3C3] bg-white shadow-md" // Cinza com fundo branco
          } relative z-10`}> {/* z-10 para marcador ficar sobre linhas */} 
            {isMarkerCompleted && (
              <svg className="w-3.5 h-3.5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M5 13l4 4L19 7" />
              </svg>
            )}
          </div>

          {/* Linhas de Conexão (Cinza e Azul) - posicionadas abaixo do marcador */}
          {!isUltimoMarcador && (
            <div className="absolute top-5 left-1/2 transform -translate-x-1/2 w-1 h-full"> 
              {/* Linha Cinza (Fundo) */}
              <div className="absolute top-0 left-0 w-full h-full bg-[#C3C3C3]"></div>

              {/* Linha Azul (Sobrepõe a cinza) */}
              {showBlueLine && (
                <div
                  className="absolute top-0 left-0 w-full bg-[#4597B5] transition-height duration-300 ease-in-out" // Adiciona transição suave
                  style={{ height: `${blueLineHeightPercent}%` }}
                ></div>
              )}
            </div>
          )}
        </div>

        {/* Texto (lado direito) */}
        <div className="ml-4 flex-grow pb-6"> 
          <h3 className="text-base font-semibold text-black leading-tight">{titulo}</h3>
          <p className="text-sm text-gray-500 mt-0.5">{descricao}</p>
        </div>
      </div>
    );
  };

  // --- Renderização Principal --- 
  return (
    <div className="pt-4 pb-2 px-2">
      {/* Renderiza os marcadores passando o índice e a flag de último */}
      <TimelineMarker
        index={0}
        data={dataDeposito}
        titulo="Protocolo"
        descricao="Data em que o pedido foi depositado no INPI."
        isUltimoMarcador={false}
        temDespachoMerito={temDespachoMerito}
      />
      
      <TimelineMarker
        index={1}
        data={dataPublicacao}
        titulo="Publicação"
        descricao="Data em que o pedido foi publicado para oposição na RPI."
        isUltimoMarcador={false}
        temDespachoMerito={temDespachoMerito}
      />
      
      <TimelineMarker
        index={2}
        data={dataFimOposicao}
        titulo="Fim da fase de oposição"
        descricao="Data limite para terceiros apresentarem oposição ao pedido."
        isUltimoMarcador={false}
        temDespachoMerito={temDespachoMerito}
      />
      
      <TimelineMarker
        index={3}
        data={null} // Data de mérito não é mostrada aqui, apenas a previsão
        titulo="Análise de mérito"
        descricao="Fase em que o examinador analisa o pedido."
        isPrevisualizacao={true}
        isUltimoMarcador={true} // Marca como último para não desenhar linha
        temDespachoMerito={temDespachoMerito}
      />
    </div>
  );
} 