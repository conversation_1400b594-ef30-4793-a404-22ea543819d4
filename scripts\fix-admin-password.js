const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function fixAdminPassword() {
  try {
    console.log('🔧 Corrigindo senha do usuário admin...\n');
    
    const email = "<EMAIL>";
    const novaSenha = "#Rgse//7599@";
    
    // Buscar usuário
    const user = await prisma.adminUser.findUnique({
      where: { email }
    });

    if (!user) {
      console.log('❌ Usuário admin não encontrado!');
      console.log('💡 Execute: node scripts/create-admin-user.js');
      return;
    }

    console.log(`👤 Usuário encontrado: ${user.nome} (${user.email})`);
    console.log(`🔑 Gerando novo hash para senha: "${novaSenha}"`);

    // Gerar novo hash
    const novoHash = await bcrypt.hash(novaSenha, 12);
    console.log(`🔐 Novo hash gerado: ${novoHash.substring(0, 20)}...`);

    // Testar se o hash está correto
    const testeHash = await bcrypt.compare(novaSenha, novoHash);
    console.log(`✅ Teste do hash: ${testeHash ? 'VÁLIDO' : 'INVÁLIDO'}`);

    if (!testeHash) {
      console.log('❌ Erro ao gerar hash! Abortando...');
      return;
    }

    // Atualizar no banco
    await prisma.adminUser.update({
      where: { email },
      data: { 
        senha: novoHash,
        role: 'MASTER',
        ativo: true
      }
    });

    console.log('✅ Senha atualizada com sucesso!');
    console.log('\n📝 Credenciais atualizadas:');
    console.log(`   Email: ${email}`);
    console.log(`   Senha: ${novaSenha}`);
    console.log(`   Role: MASTER`);
    console.log('\n🌐 Acesse: http://localhost:3000/admin/login');

    // Limpar sessões existentes
    await prisma.adminSession.deleteMany({
      where: { adminUserId: user.id }
    });
    
    console.log('🧹 Sessões antigas limpas');

  } catch (error) {
    console.error('❌ Erro ao corrigir senha:', error);
  } finally {
    await prisma.$disconnect();
  }
}

fixAdminPassword(); 