'use client';

import { useEffect, useState, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { decryptCredentials } from '@/lib/autoLogin';
import { saveClienteSession } from '@/lib/persistentSession';
import Image from 'next/image';

function AutoLoginContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [status, setStatus] = useState<'loading' | 'error' | 'success'>('loading');
  const [mensagem, setMensagem] = useState<string>('Processando login automático...');

  useEffect(() => {
    const performAutoLogin = async () => {
      try {
        // Obter token da URL
        const token = searchParams.get('token');
        
        if (!token) {
          setStatus('error');
          setMensagem('Link inválido - token não encontrado');
          return;
        }

        // Descriptografar credenciais
        console.log('🔍 Debug - Token recebido:', token.substring(0, 50) + '...');
        const credenciais = decryptCredentials(token);
        console.log('🔍 Debug - Credenciais descriptografadas:', credenciais);
        
        if (!credenciais) {
          console.error('❌ Debug - Falha na descriptografia');
          setStatus('error');
          setMensagem('Link inválido ou expirado');
          return;
        }

        setMensagem('Fazendo login...');

        // Fazer login usando a API existente
        const response = await fetch('/api/auth/login', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            identificador: credenciais.identificador,
            senha: credenciais.senha,
            manterConectado: true // Auto-login sempre mantém conectado
          }),
        });

        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.error || 'Erro ao fazer login');
        }

        // ✅ NOVO: Salvar sessão persistente para auto-login
        if (data.cliente) {
          saveClienteSession({
            clienteId: data.cliente.id,
            identificador: data.cliente.identificador,
            nome: data.cliente.nome,
            email: data.cliente.email || undefined,
            telefone: data.cliente.telefone || undefined
          });
          console.log('💾 Sessão persistente salva via auto-login');
        }

        setStatus('success');
        setMensagem('Login realizado com sucesso! Redirecionando...');

        // Aguardar um momento e redirecionar
        setTimeout(() => {
          router.push('/cliente');
        }, 1500);

      } catch (error: any) {
        console.error('Erro no auto-login:', error);
        setStatus('error');
        setMensagem(error.message || 'Erro durante o login automático');
        
        // Redirecionar para login manual após erro
        setTimeout(() => {
          router.push('/?error=auto-login-failed');
        }, 3000);
      }
    };

    performAutoLogin();
  }, [searchParams, router]);

  return (
    <div className="min-h-screen bg-backgroundCinza flex flex-col items-center justify-center p-4">
      <div className="bg-branco shadow-lg rounded-lg p-8 max-w-md w-full text-center">
        {/* Logo */}
        <div className="mb-6">
          <Image
            src="/logo.svg"
            alt="Logo"
            width={200}
            height={80}
            className="mx-auto"
          />
        </div>

        {/* Status */}
        <div className="space-y-4">
          {status === 'loading' && (
            <div className="flex flex-col items-center">
              <svg
                className="animate-spin h-8 w-8 text-verde mb-4"
                viewBox="0 0 24 24"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                ></circle>
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
              <p className="text-TextosEscurosEBg text-lg">{mensagem}</p>
            </div>
          )}

          {status === 'success' && (
            <div className="flex flex-col items-center">
              <div className="w-8 h-8 bg-verde rounded-full flex items-center justify-center mb-4">
                <svg className="w-5 h-5 text-branco" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                </svg>
              </div>
              <p className="text-verde text-lg font-semibold">{mensagem}</p>
            </div>
          )}

          {status === 'error' && (
            <div className="flex flex-col items-center">
              <div className="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center mb-4">
                <svg className="w-5 h-5 text-branco" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
              </div>
              <p className="text-red-600 text-lg">{mensagem}</p>
              <p className="text-cinzaTexto text-sm mt-2">
                Você será redirecionado para a página de login em alguns segundos...
              </p>
            </div>
          )}
        </div>

        {/* Link manual */}
        {status === 'error' && (
          <div className="mt-6 pt-4 border-t border-cinzaClaro">
            <button
              onClick={() => router.push('/')}
              className="text-azul hover:text-azulHover underline text-sm"
            >
              Ir para login manual
            </button>
          </div>
        )}
      </div>
    </div>
  );
}

function LoadingFallback() {
  return (
    <div className="min-h-screen bg-backgroundCinza flex flex-col items-center justify-center p-4">
      <div className="bg-branco shadow-lg rounded-lg p-8 max-w-md w-full text-center">
        <div className="mb-6">
          <Image
            src="/logo.svg"
            alt="Logo"
            width={200}
            height={80}
            className="mx-auto"
          />
        </div>
        <div className="flex flex-col items-center">
          <svg
            className="animate-spin h-8 w-8 text-verde mb-4"
            viewBox="0 0 24 24"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            ></circle>
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path>
          </svg>
          <p className="text-TextosEscurosEBg text-lg">Carregando...</p>
        </div>
      </div>
    </div>
  );
}

export default function AutoLoginPage() {
  return (
    <Suspense fallback={<LoadingFallback />}>
      <AutoLoginContent />
    </Suspense>
  );
} 