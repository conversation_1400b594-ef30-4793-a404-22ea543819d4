import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

const DEFAULT_PAGE_SIZE = 20; // Reduzido para 20 para melhor UX

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const identificador = searchParams.get('identificador');
    const page = parseInt(searchParams.get('page') || '1', 10);
    const pageSize = parseInt(searchParams.get('pageSize') || DEFAULT_PAGE_SIZE.toString(), 10);

    if (!identificador) {
      return NextResponse.json(
        { error: 'Identificador do cliente é obrigatório' },
        { status: 400 }
      );
    }

    let processosDoCliente;
    let totalProcessosDoCliente;
    const skip = (page - 1) * pageSize;

    if (identificador === '00000013') {
      // Lógica especial para o identificador coringa "00000013"
      const whereClauseCoringa = {
        Procurador: {
          nome: {
            contains: 'REGISTRE-SE LTDA',
            mode: 'insensitive' as const,
          },
        },
        NOT: {
          Despacho: {
            some: {
              codigo: 'IPAS047',
            },
          },
        },
      };

      // 1. Buscar TODOS os processos que atendem aos critérios iniciais para o coringa
      const todosProcessosCoringa = await prisma.processo.findMany({
        where: whereClauseCoringa,
        include: {
          Cliente: { select: { crmStageId: true } },
          Marca: { include: { NCL: true } },
          Despacho: {
            orderBy: { RPI: { dataPublicacao: 'asc' } },
            include: {
              DetalhesDespacho: true,
              ProtocoloDespacho: true,
              RPI: { select: { dataPublicacao: true, numero: true } },
            },
          },
          Titular: { select: { nomeRazaoSocial: true, pais: true, uf: true } },
          Procurador: { select: { nome: true } },
        },
        orderBy: {
          dataDeposito: 'desc' as const,
        },
      });

      // 2. Aplicar filtro de fluxos únicos
      const fluxosUnicos = new Map<string, typeof todosProcessosCoringa[0]>();
      for (const processo of todosProcessosCoringa) {
        const assinaturaDespachos = processo.Despacho
          .map(d => d.DetalhesDespacho?.nome || d.nome || "DESPACHO_SEM_NOME")
          .join(' > ');
        if (!fluxosUnicos.has(assinaturaDespachos)) {
          fluxosUnicos.set(assinaturaDespachos, processo);
        }
      }
      const processosComFluxosUnicos = Array.from(fluxosUnicos.values());

      // 3. Total de processos é a contagem após o filtro de fluxos únicos
      totalProcessosDoCliente = processosComFluxosUnicos.length;

      // 4. Aplicar paginação à lista filtrada
      processosDoCliente = processosComFluxosUnicos.slice(skip, skip + pageSize);

    } else {
      // Lógica normal para clientes específicos
    const cliente = await prisma.cliente.findFirst({
      where: { identificador },
    });

    if (!cliente) {
      return NextResponse.json(
        { error: 'Cliente não encontrado' },
        { status: 404 }
      );
    }

      const whereClauseCliente = {
      clienteId: cliente.id,
    };

      totalProcessosDoCliente = await prisma.processo.count({
        where: whereClauseCliente,
      });

      processosDoCliente = await prisma.processo.findMany({
        where: whereClauseCliente,
      include: {
        Cliente: { select: { crmStageId: true } },
        Marca: { include: { NCL: true } },
        Despacho: {
          orderBy: { RPI: { dataPublicacao: 'asc' } },
          include: {
            DetalhesDespacho: true,
            ProtocoloDespacho: true,
              RPI: { select: { dataPublicacao: true, numero: true } },
            },
        },
        Titular: { select: { nomeRazaoSocial: true, pais: true, uf: true } },
        Procurador: { select: { nome: true } },
      },
      orderBy: {
          dataDeposito: 'desc' as const,
        },
        skip,
        take: pageSize,
      });
    }

    const textoDespachoPublicacao = "publicação de pedido de registro para oposição";
    const processosFinaisComDatas = processosDoCliente.map(processo => {
      const despachoPublicacao = processo.Despacho.find(
        despacho => despacho.nome?.toLowerCase().includes(textoDespachoPublicacao)
      );
      if (despachoPublicacao?.RPI?.dataPublicacao) {
        const dataPublicacao = new Date(despachoPublicacao.RPI.dataPublicacao);
        const dataFimOposicao = new Date(dataPublicacao);
        dataFimOposicao.setDate(dataFimOposicao.getDate() + 60);
        return {
          ...processo,
          dataPublicacaoRPI: dataPublicacao.toISOString(),
          dataOposicao: dataFimOposicao.toISOString()
        };
      }
      return processo;
    });

    return NextResponse.json({
      processos: processosFinaisComDatas,
      totalProcessos: totalProcessosDoCliente,
      currentPage: page,
      pageSize: pageSize,
      totalPages: Math.ceil(totalProcessosDoCliente / pageSize),
      hasNextPage: page < Math.ceil(totalProcessosDoCliente / pageSize),
      hasPreviousPage: page > 1,
    });
    
  } catch (error) {
    console.error('Erro ao buscar processos:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
} 