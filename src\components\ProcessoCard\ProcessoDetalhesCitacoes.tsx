import React from 'react'; // Remove useState, useEffect
import { format } from 'date-fns'; // Importar para formatar a data

// Interface ajustada para os dados da API
interface CitacaoApi {
  processo: string;
  marca: string;
  classe: string;
  titular: string;
  dataRpi: string; // Vem como ISO string da API
  numeroRpi?: number; // Renomeado de rpiNum para numeroRpi
  despacho: string;
  complemento: string; // Complemento é sempre string na API
}

// Função auxiliar para transformar números de processo em links no texto
const linkifyProcessNumbers = (text: string): React.ReactNode[] => {
  if (!text) return [text];
  // Ajusta a regex para pegar exatamente 9 dígitos usando word boundaries (\b)
  const processNumberRegex = /\b(\d{9})\b/g;
  const parts: React.ReactNode[] = [];
  let lastIndex = 0;
  let match;

  while ((match = processNumberRegex.exec(text)) !== null) {
    const index = match.index;
    const number = match[0];
    if (index > lastIndex) {
      parts.push(text.substring(lastIndex, index));
    }
    parts.push(
      <a
        key={`${number}-${index}`}
        href={`/processo/${number}`}
        target="_blank"
        rel="noopener noreferrer"
        // Remove text-blue-600, usa text-current e mantém hover:underline
        className="text-current underline font-extrabold hover:text-blue-600"
      >
        {number}
      </a>
    );
    lastIndex = index + number.length;
  }
  if (lastIndex < text.length) {
    parts.push(text.substring(lastIndex));
  }
  return parts.length > 0 ? parts : [text];
};

// Remover DADOS_FICTICIOS_CITACOES
// const DADOS_FICTICIOS_CITACOES: Citacao[] = [ ... ];

interface ProcessoDetalhesCitacoesProps {
  // numeroProcesso: string; // Remove prop antiga
  citacoes: CitacaoApi[]; // Adiciona a nova prop
}

// Recebe 'citacoes' diretamente via props
const ProcessoDetalhesCitacoes: React.FC<ProcessoDetalhesCitacoesProps> = ({ citacoes }) => {
  // Remove useState para citacoes, loading, error
  // Remove useEffect para fetchCitacoes

  // Função para formatar a data
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'dd/MM/yyyy');
    } catch (e) {
      console.error("Erro ao formatar data:", e);
      return 'Data inválida'; // Retorna um fallback caso a data seja inválida
    }
  };

  return (
    <div className="mb-6 overflow-x-auto">
      <h4 className="text-base font-semibold mb-0 uppercase text-center bg-[#A2A2A2] text-white py-1">CITAÇÃO EM OUTROS PROCESSOS</h4>
      <table className="min-w-full text-sm border-b-2 border-[#ACACAC]">
        <thead className="text-gray-700" style={{ backgroundColor: "var(--color-linhaTabela)" }}>
          <tr>
            <th className="px-3 py-2 text-center font-semibold tracking-wider text-black text-xs w-[8%]">PROCESSO</th>
            <th className="px-3 py-2 text-center font-semibold tracking-wider text-black text-xs w-[14%]">MARCA</th>
            <th className="px-3 py-2 text-center font-semibold tracking-wider text-black text-xs w-[5%]">CLASSE</th>
            <th className="px-3 py-2 text-center font-semibold tracking-wider text-black text-xs w-[14%]">TITULAR</th>
            <th className="px-3 py-2 text-center font-semibold tracking-wider text-black text-xs w-[8%]">DATA (RPI)</th>
            <th className="px-3 py-2 text-left font-semibold tracking-wider text-black text-xs w-[16%]">DESPACHO</th>
            <th className="px-3 py-2 text-left font-semibold tracking-wider text-black text-xs w-[35%]">COMPLEMENTO</th>
          </tr>
        </thead>
        <tbody>
          {/* Usar um índice ou gerar um ID único se a API não fornecer um ID estável */}
          {citacoes.map((citacao, index) => (
            // Usando o índice como chave - idealmente a API forneceria um ID único
            <tr key={`${citacao.processo}-${citacao.dataRpi}-${index}`} style={{ backgroundColor: index % 2 === 0 ? "white" : "var(--color-linhaTabela)" }}>
              <td className="px-3 py-2 whitespace-nowrap text-black font-semibold text-xs text-center">
                {/* Link para o processo - ajusta estilo */}
                <a
                  href={`/processo/${citacao.processo}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  // Remove text-blue-600, usa text-current e mantém hover:underline
                  className="text-current underline font-extrabold hover:text-blue-600"
                >
                  {citacao.processo}
                </a>
              </td>
              <td className="px-3 py-2 text-black font-semibold text-xs text-center">{citacao.marca}</td>
              <td className="px-3 py-2 whitespace-nowrap text-black font-semibold text-xs text-center">{citacao.classe}</td>
              <td className="px-3 py-2 text-black font-semibold text-xs text-center">{citacao.titular}</td>
              <td className="px-3 py-2 whitespace-nowrap text-black font-semibold text-xs text-center">
                {formatDate(citacao.dataRpi)} {/* Formata a data */}
                {citacao.numeroRpi && <span className="block text-xs text-black font-semibold">({citacao.numeroRpi})</span>} {/* Usa numeroRpi */}
              </td>
              <td className="px-3 py-2 text-black font-semibold text-xs text-left">{citacao.despacho}</td>
              {/* O complemento agora é processado para links */}
              <td className="px-3 py-4 text-black font-semibold text-xs text-left whitespace-normal">
                {linkifyProcessNumbers(citacao.complemento)}
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default ProcessoDetalhesCitacoes; 