import React from 'react';

const StatusVigorIcon: React.FC = () => (
  <svg
    width="29"
    height="29"
    viewBox="0 0 29 29"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <mask
      id="mask0_214_76"
      style={{ maskType: "luminance" }}
      maskUnits="userSpaceOnUse"
      x="0"
      y="0"
      width="29"
      height="29"
    >
      <path d="M29 0H0V29H29V0Z" fill="white" />
    </mask>
    <g mask="url(#mask0_214_76)">
      <mask
        id="mask1_214_76"
        style={{ maskType: "luminance" }}
        maskUnits="userSpaceOnUse"
        x="0"
        y="0"
        width="29"
        height="29"
      >
        <path d="M29 0H0V29H29V0Z" fill="white" />
      </mask>
      <g mask="url(#mask1_214_76)">
        <path
          d="M14.4983 0C6.50403 0 0 6.50403 0 14.4932C0 22.4823 6.50403 28.9957 14.4983 28.9957C22.4926 28.9957 29 22.4909 29 14.4932C29 6.4955 22.496 0 14.4983 0ZM14.4983 26.5472C7.84848 26.5472 2.44849 21.143 2.44849 14.4974C2.44849 7.85189 7.85189 2.45276 14.4983 2.45276C21.1447 2.45276 26.5472 7.85616 26.5472 14.4974C26.5472 21.1387 21.143 26.5472 14.4983 26.5472ZM14.7899 7.45802H9.27052V13.3312H12.2169V10.4001H14.7899C15.5818 10.4007 16.3445 10.6992 16.9265 11.2362C17.5085 11.7732 17.8673 12.5095 17.9315 13.2988H20.8821C20.8141 11.7281 20.1427 10.2442 19.0078 9.15614C17.8729 8.06811 16.362 7.45984 14.7899 7.45802ZM14.7899 15.3866H9.27052V21.2606H12.2169V18.3347H14.7899C15.5818 18.3353 16.3445 18.6338 16.9265 19.1708C17.5085 19.7078 17.8673 20.4441 17.9315 21.2334H20.8821C20.8158 19.6615 20.1451 18.176 19.01 17.0867C17.8749 15.9973 16.3631 15.3883 14.7899 15.3866Z"
          fill="white"
        />
      </g>
    </g>
  </svg>
);

export default StatusVigorIcon; 