import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { encryptCredentials, decryptCredentials } from '@/lib/autoLogin';
import { getAdminFromRequest, requireAdminRole } from '@/lib/adminAuth';

export async function POST(request: NextRequest) {
  try {
    // Verificar autenticação e role MASTER
    const user = await getAdminFromRequest(request);
    if (!user || !requireAdminRole(['MASTER'])(user)) {
      return NextResponse.json(
        { error: 'Acesso negado. Apenas usuários MASTER podem acessar esta funcionalidade.' },
        { status: 403 }
      );
    }
    const body = await request.json();
    const { clienteId, shortCode } = body;

    // Validar entrada
    if (!clienteId || !shortCode) {
      return NextResponse.json(
        { error: 'ClienteId e shortCode são obrigatórios' },
        { status: 400 }
      );
    }

    // Buscar o cliente atual com dados atualizados
    const cliente = await prisma.cliente.findUnique({
      where: { id: clienteId },
      select: {
        id: true,
        nome: true,
        identificador: true,
        numeroDocumento: true,
      },
    });

    if (!cliente) {
      return NextResponse.json(
        { error: 'Cliente não encontrado' },
        { status: 404 }
      );
    }

    if (!cliente.identificador || !cliente.numeroDocumento) {
      return NextResponse.json(
        { error: 'Cliente não possui identificador ou documento necessários' },
        { status: 400 }
      );
    }

    // Buscar o short URL existente
    const shortUrl = await prisma.shortUrl.findUnique({
      where: { shortCode },
      include: { Cliente: true },
    });

    if (!shortUrl) {
      return NextResponse.json(
        { error: 'Short URL não encontrado' },
        { status: 404 }
      );
    }

    // Verificar se pertence ao cliente correto
    if (shortUrl.clienteId !== clienteId) {
      return NextResponse.json(
        { error: 'Short URL não pertence ao cliente especificado' },
        { status: 400 }
      );
    }

    // Tentar descriptografar o token atual para comparar
    let tokenAtualInfo = null;
    try {
      tokenAtualInfo = decryptCredentials(shortUrl.longToken);
    } catch (error) {
      console.log('Token atual não pode ser descriptografado:', error);
    }

    // Gerar nova senha baseada no documento atual
    const numeroLimpo = cliente.numeroDocumento.replace(/[\D]/g, '');
    const senhaAtual = numeroLimpo.slice(-3);

    console.log('🔧 Corrigindo Short URL:', {
      shortCode,
      clienteId,
      identificador: cliente.identificador,
      senhaAnterior: tokenAtualInfo?.senha,
      senhaAtual,
    });

    // Gerar novo token com a senha correta
    const novoToken = encryptCredentials(cliente.identificador, senhaAtual);

    // Atualizar o registro do Short URL
    const shortUrlAtualizado = await prisma.shortUrl.update({
      where: { shortCode },
      data: {
        longToken: novoToken,
        // Reseta contador de uso para permitir novos testes
        usageCount: 0,
        // Garante que está ativo
        isActive: true,
      },
    });

    // Verificar se o novo token funciona
    const verificacao = decryptCredentials(novoToken);
    
    return NextResponse.json({
      message: 'Short URL corrigido com sucesso',
      shortCode,
      cliente: {
        id: cliente.id,
        nome: cliente.nome,
        identificador: cliente.identificador,
      },
      correcao: {
        senhaAnterior: tokenAtualInfo?.senha || 'N/A',
        senhaAtual,
        tokenAtualizado: !!verificacao,
      },
      shortUrl: {
        id: shortUrlAtualizado.id,
        usageCount: shortUrlAtualizado.usageCount,
        isActive: shortUrlAtualizado.isActive,
        createdAt: shortUrlAtualizado.createdAt,
      },
    });

  } catch (error) {
    console.error('Erro ao corrigir short URL:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
} 