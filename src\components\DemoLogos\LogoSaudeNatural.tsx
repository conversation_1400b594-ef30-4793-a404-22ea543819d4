import React from 'react';

interface LogoProps {
  className?: string;
}

export default function LogoSaudeNatural({ className = "" }: LogoProps) {
  return (
    <svg
      viewBox="0 0 200 200"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <rect width="200" height="200" rx="20" fill="#FFF5E5" />
      <path
        d="M100 60C77.909 60 60 77.909 60 100C60 122.091 77.909 140 100 140C122.091 140 140 122.091 140 100C140 77.909 122.091 60 100 60Z"
        fill="#FF9900"
      />
      <path
        d="M90 100H110M100 90V110"
        stroke="white"
        strokeWidth="8"
        strokeLinecap="round"
      />
      <path
        d="M70 70C90 50 110 50 130 70"
        stroke="#FF9900"
        strokeWidth="8"
        strokeLinecap="round"
      />
      <path
        d="M70 130C90 150 110 150 130 130"
        stroke="#FF9900"
        strokeWidth="8"
        strokeLinecap="round"
      />
    </svg>
  );
} 