import { EtapaDefinicao, definicoesEtapas } from "./etapasConfig";
import { ProcessoParaTimeline, EtapaTimelineGerada, StatusEtapaGerada, ExigenciaInfo } from "./timelineNovasTypes";
import { Despacho_Interface } from "./appTypes";
import {
  formatarData,
  encontrarDefinicaoEtapaPorId,
  calcularProximaTerca,
  isDespachoDePublicacao,
  encontrarDataPublicacaoMaisRecente,
  verificarOposicaoAposData,
  encontrarDataProtocoloManifestacao,
  encontrarDataNotificacaoOposicaoMaisRecente,
  isDespachoDeExigencia,
  isDespachoDeArquivamento,
  isDespachoDeCumprimentoExigencia,
  isDespachoDeSobrestamento,
  isDespachoDeDeferimento,
  isDespachoDePagamentoTaxaOrdinaria,
  isDespachoDePagamentoTaxaExtraordinaria,
  isDespachoDeArquivamentoFaltaPagamentoTaxa,
  isDespachoDeConcessaoMantidaPosNulidade,
  isDespachoDeRegistroAnulado
} from "./timelineConfigUtil";
import {
  TimelineFluxoEstado,
  processarConcessaoRegistroHandler,
  processarArquivamentoFaltaPagamentoHandler,
  processarArquivamentoGeralHandler,
  processarSobrestamentoHandler,
  atualizarEstadoDeferimentoPagamentoHandler,
  gerenciarExigenciasHandler,
  processarPrazosPagamentoPosLoopHandler,
  processarFluxoPadraoTimelineHandler,
  FluxoPadraoParams,
  processarIndeferimentoHandler,
  processarApresentacaoRecursoIndeferimentoHandler,
  processarDecisaoRecursoHandler,
  processarArquivamentoPorRecursoNaoApresentadoPosLoopHandler,
  processarAnaliseRecursoSemApresentacaoPosLoopHandler,
  processarFimPrazoNulidadeSemInstauracaoHandler,
  processarInstauracaoNulidadeHandler,
  processarManifestacaoNulidadeHandler,
  processarExpiracaoPrazoManifestacaoNulidadeHandler,
  processarDecisaoNulidadeHandler,
  processarArquivamentoPosRecursoNulidadeNegadoHandler,
  processarRenovacoesAnterioresHandler
} from "./timelineFluxoHandlers";

// Interface simplificada para as estimativas de mérito
interface EstimativaMeritoSalva {
  mediaSemIntervencoes?: number | null;
  mediaComOposicao?: number | null;
  mediaComSobrestamento?: number | null;
  mediaComExigencia?: number | null;
}

/**
 * Verifica se já existe uma etapa que representa o resultado do mérito
 * (deferimento, indeferimento, concessão, arquivamento)
 */
function jaMeritoFoiProcessado(timeline: EtapaTimelineGerada[]): boolean {
  return timeline.some(etapa => {
    const idOriginal = etapa.idOriginal;
    const nome = etapa.etapaDefinicao.nomeOriginal.toLowerCase();
    
    // Verificar por IDs específicos de resultados de mérito
    const resultadosMerito = [
      'DEFERIMENTO_PEDIDO_01',
      'DEFERIMENTO_POS_RECURSO_01', 
      'INDEFERIMENTO_PEDIDO_01',
      'CONCESSAO_REGISTRO_01',
      'ARQUIVAMENTO_01'
    ];
    
    if (resultadosMerito.includes(idOriginal)) {
      return true;
    }
    
    // Verificar por nomes que indicam resultado de mérito
    const palavrasResultadoMerito = [
      'deferimento',
      'indeferimento', 
      'concessão',
      'arquivamento'
    ];
    
    return palavrasResultadoMerito.some(palavra => nome.includes(palavra));
  });
}

export function gerarTimelineSimplificada(
  processo: ProcessoParaTimeline,
  estimativasDb?: EstimativaMeritoSalva | null
): EtapaTimelineGerada[] {
  let timeline: EtapaTimelineGerada[] = [];
  const despachosOrdenados = (processo.Despacho || []).slice().sort((a, b) => {
    // Função auxiliar para obter a data mais relevante de um despacho
    const obterDataRelevante = (despacho: Despacho_Interface): number => {
      // Prioriza a data do protocolo se disponível
      if (despacho.ProtocoloDespacho && despacho.ProtocoloDespacho.length > 0 && despacho.ProtocoloDespacho[0].data) {
        return new Date(despacho.ProtocoloDespacho[0].data).getTime();
      }
      // Fallback para data de publicação da RPI
      if (despacho.RPI?.dataPublicacao) {
        return new Date(despacho.RPI.dataPublicacao).getTime();
      }
      return 0;
    };

    const dataA = obterDataRelevante(a);
    const dataB = obterDataRelevante(b);
    return dataA - dataB;
  });

  const temDataDeposito = !!(processo.dataDeposito && processo.dataDeposito.length > 0);
  const hoje = new Date();
  
  let fluxoEstado: TimelineFluxoEstado = {
    dataDeferimento: null,
    taxaConcessaoPaga: null,
    dataPagamentoTaxa: null,
    dataConcessao: null,
    arquivadoPorFaltaPagamentoTaxa: null,
    exigenciaAtiva: null,
    etapaAtualIdentificada: false,
    dataUltimaRenovacaoConcluida: null,
  };
  
  // LÓGICA DE INFERÊNCIA INICIAL PARA PROCESSOS ANTIGOS
  if (processo.dataConcessao && !fluxoEstado.dataConcessao) {
    const dataConcessaoInferida = new Date(processo.dataConcessao);
    fluxoEstado.dataConcessao = dataConcessaoInferida;

    const defConcessao = encontrarDefinicaoEtapaPorId('CONCESSAO_REGISTRO_01');
    if (defConcessao) {
      timeline.push({
        idOriginal: defConcessao.id, etapaDefinicao: defConcessao, status: 'CONCLUIDA',
        dataInicio: dataConcessaoInferida, dataFim: dataConcessaoInferida,
        observacaoGerada: `Concessão inferida a partir de dados do processo (${formatarData(dataConcessaoInferida)}).`
      });
    }

    const defFimPrazoNulidade = encontrarDefinicaoEtapaPorId('FIM_PRAZO_NULIDADE_01');
    if (defFimPrazoNulidade && fluxoEstado.dataConcessao) {
      const dataFimPrazoNul = new Date(fluxoEstado.dataConcessao);
      dataFimPrazoNul.setDate(dataFimPrazoNul.getDate() + 180);
      const statusFimPrazo = hoje > dataFimPrazoNul ? 'CONCLUIDA' : 'PROXIMA_EXATA';

      timeline.push({
        idOriginal: defFimPrazoNulidade.id, etapaDefinicao: defFimPrazoNulidade, status: statusFimPrazo,
        dataInicio: dataFimPrazoNul, dataFim: dataFimPrazoNul,
        observacaoGerada: statusFimPrazo === 'CONCLUIDA' ? `Prazo de nulidade encerrado em ${formatarData(dataFimPrazoNul)} (inferido).` : `Prazo para nulidade até ${formatarData(dataFimPrazoNul)} (inferido).`
      });

      // Adicionar RENOVACAO_01 sempre que há concessão e não há processo de nulidade instaurado
      if (!timeline.some(e => e.idOriginal === 'PROCESSO_NULIDADE_INSTAURADO_01')) {
        const defRenovacao = encontrarDefinicaoEtapaPorId('RENOVACAO_01');
        if (defRenovacao) {
          const dataConcessaoOriginal = new Date(fluxoEstado.dataConcessao);
          const anosAdicionais = 10; // Para o segundo decênio, dataUltimaRenovacaoConcluida é null aqui
          const dataInicioProximaJanela = new Date(dataConcessaoOriginal);
          dataInicioProximaJanela.setFullYear(dataConcessaoOriginal.getFullYear() + anosAdicionais);
          const observacaoRenovacao = `Próxima janela de renovação. Registro válido até ${formatarData(dataInicioProximaJanela)}, calculado a partir da data de concessão (${formatarData(dataConcessaoOriginal)}).`;
          timeline = timeline.filter(e => !(e.idOriginal === 'RENOVACAO_01' && e.status === 'PROXIMA_ESTIMADA'));
          timeline.push({
            idOriginal: defRenovacao.id, etapaDefinicao: defRenovacao, status: 'PROXIMA_ESTIMADA',
            dataInicio: dataInicioProximaJanela, observacaoGerada: observacaoRenovacao
          });
        }
      }
    }
    timeline.sort((a, b) => (a.dataInicio?.getTime() || 0) - (b.dataInicio?.getTime() || 0));
  }
  // FIM DA LÓGICA DE INFERÊNCIA INICIAL

  for (const despacho of despachosOrdenados) {
    const dataDespacho = despacho.RPI?.dataPublicacao ? new Date(despacho.RPI.dataPublicacao) : 
                         (despacho.ProtocoloDespacho && despacho.ProtocoloDespacho.length > 0 && despacho.ProtocoloDespacho[0].data ? new Date(despacho.ProtocoloDespacho[0].data) : null);

    if (!dataDespacho) continue;

    const handlerParamsBase = {despacho, dataDespacho, processo, despachosOrdenados, temDataDeposito };

    // 1. HANDLER: Arquivamento Geral (tem prioridade)
    const resultadoArqGeral = processarArquivamentoGeralHandler({...handlerParamsBase, timelineAnterior: timeline, estadoAnterior: fluxoEstado});
    timeline = resultadoArqGeral.timelineAtualizada;
    fluxoEstado = resultadoArqGeral.estadoAtualizado;
    if (resultadoArqGeral.finalizarProcessamento) return limparTimelineFinal(timeline, processo, temDataDeposito);

    // 2. HANDLER: Sobrestamento
    const resultadoSobrestamento = processarSobrestamentoHandler({...handlerParamsBase, timelineAnterior: timeline, estadoAnterior: fluxoEstado});
    timeline = resultadoSobrestamento.timelineAtualizada;
    fluxoEstado = resultadoSobrestamento.estadoAtualizado;
    // Sobrestamento não finaliza o processamento por si só.

    // NOVO HANDLER: Processar Renovações Anteriores
    // Deve ser chamado após a possível identificação da concessão e antes dos handlers de nulidade.
    if (fluxoEstado.dataConcessao) { // Só processa se já houve uma concessão identificada
        const resultadoRenovacoes = processarRenovacoesAnterioresHandler({...handlerParamsBase, timelineAnterior: timeline, estadoAnterior: fluxoEstado });
        timeline = resultadoRenovacoes.timelineAtualizada;
        fluxoEstado = resultadoRenovacoes.estadoAtualizado;
        // Este handler não finaliza o processamento, apenas registra renovações passadas.
    }

    // NOVO HANDLER: Instauração de Nulidade (após concessão)
    // Deve ser chamado ANTES de Gerenciar Exigências, pois uma nulidade pode invalidar uma exigência pendente (ou o contexto dela muda)
    // E também antes de outros handlers que dependem do estado "normal" do processo.
    if (fluxoEstado.dataConcessao && !fluxoEstado.arquivadoPorFaltaPagamentoTaxa && !timeline.some(e => e.idOriginal === 'ARQUIVAMENTO_01' && e.status === 'ATUAL')) {
        const resultadoInstNulidade = processarInstauracaoNulidadeHandler({...handlerParamsBase, timelineAnterior: timeline, estadoAnterior: fluxoEstado });
        timeline = resultadoInstNulidade.timelineAtualizada;
        fluxoEstado = resultadoInstNulidade.estadoAtualizado;
        if (resultadoInstNulidade.finalizarProcessamento) return limparTimelineFinal(timeline, processo, temDataDeposito);
    }

    // NOVO HANDLER: Manifestação sobre Nulidade
    const etapaAtualParaManifestacaoNulidade = timeline.find(e => e.status === 'ATUAL');
    if (etapaAtualParaManifestacaoNulidade?.idOriginal === 'PRAZO_MANIFESTACAO_NULIDADE_01') {
        const resultadoManifestacaoNulidade = processarManifestacaoNulidadeHandler({...handlerParamsBase, timelineAnterior: timeline, estadoAnterior: fluxoEstado });
        timeline = resultadoManifestacaoNulidade.timelineAtualizada;
        fluxoEstado = resultadoManifestacaoNulidade.estadoAtualizado;
        if (resultadoManifestacaoNulidade.finalizarProcessamento) return limparTimelineFinal(timeline, processo, temDataDeposito);
    }

    // NOVO HANDLER: Decisão sobre Nulidade
    // Alterado: Chama se o DESPACHO ATUAL é uma decisão de nulidade e o processo já foi concedido.
    if (
      (isDespachoDeConcessaoMantidaPosNulidade(despacho) || isDespachoDeRegistroAnulado(despacho)) &&
      fluxoEstado.dataConcessao 
    ) {
        const resultadoDecisaoNulidade = processarDecisaoNulidadeHandler({...handlerParamsBase, timelineAnterior: timeline, estadoAnterior: fluxoEstado });
        timeline = resultadoDecisaoNulidade.timelineAtualizada;
        fluxoEstado = resultadoDecisaoNulidade.estadoAtualizado;
        if (resultadoDecisaoNulidade.finalizarProcessamento) return limparTimelineFinal(timeline, processo, temDataDeposito);
    }

    // 3. HANDLER: Gerenciar Cumprimento/Nova Exigência
    const resultadoExigencias = gerenciarExigenciasHandler({...handlerParamsBase, timelineAnterior: timeline, estadoAnterior: fluxoEstado});
    timeline = resultadoExigencias.timelineAtualizada; 
    fluxoEstado = resultadoExigencias.estadoAtualizado;
    // Gerenciar exigências não finaliza. (o finalizarProcessamento é condicional, mas etapaAtualIdentificada pode ser true)

    // 4. HANDLER: Atualizar estado de Deferimento e Pagamento
    const resultadoEstadoDeferPagto = atualizarEstadoDeferimentoPagamentoHandler({...handlerParamsBase, timelineAnterior: timeline, estadoAnterior: fluxoEstado});
    fluxoEstado = resultadoEstadoDeferPagto.estadoAtualizado; 

    // HANDLER: Tentar processar Indeferimento do Pedido
    // Este handler pode definir etapaAtualIdentificada = true se encontrar um indeferimento.
    // Chamará mesmo se etapaAtualIdentificada for true por um handler anterior (ex: exigência),
    // pois o IndeferimentoHandler tem sua própria lógica para determinar se deve atuar.
    if (!fluxoEstado.dataConcessao && !fluxoEstado.arquivadoPorFaltaPagamentoTaxa) { 
        const resultadoIndeferimento = processarIndeferimentoHandler({...handlerParamsBase, timelineAnterior: timeline, estadoAnterior: fluxoEstado});
        timeline = resultadoIndeferimento.timelineAtualizada;
        fluxoEstado = resultadoIndeferimento.estadoAtualizado;
        // O finalizarProcessamento do Indeferimento é false, permitindo que ApresentacaoRecurso seja verificado para o mesmo despacho ou subsequentes.
        if (resultadoIndeferimento.finalizarProcessamento) return limparTimelineFinal(timeline, processo, temDataDeposito);
    }

    // HANDLER: Tentar processar Apresentação de Recurso contra Indeferimento
    // Chamará mesmo se etapaAtualIdentificada for true pelo IndeferimentoHandler,
    // pois o ApresentacaoRecursoHandler tem lógica para verificar se a etapa ATUAL é PRAZO_RECURSO.
    if (!fluxoEstado.dataConcessao && !fluxoEstado.arquivadoPorFaltaPagamentoTaxa) { 
        const resultadoApresentacaoRecurso = processarApresentacaoRecursoIndeferimentoHandler({...handlerParamsBase, timelineAnterior: timeline, estadoAnterior: fluxoEstado});
        timeline = resultadoApresentacaoRecurso.timelineAtualizada;
        fluxoEstado = resultadoApresentacaoRecurso.estadoAtualizado;
        if (resultadoApresentacaoRecurso.finalizarProcessamento) return limparTimelineFinal(timeline, processo, temDataDeposito);
    }

    // HANDLER: Tentar processar Decisão sobre Recurso contra Indeferimento
    // Similarmente, chamará mesmo se etapaAtualIdentificada for true,
    // pois o DecisaoRecursoHandler verifica se a etapa ATUAL é ANALISE_RECURSO.
    if (!fluxoEstado.dataConcessao && !fluxoEstado.arquivadoPorFaltaPagamentoTaxa) { 
      const resultadoDecisaoRecurso = processarDecisaoRecursoHandler({...handlerParamsBase, timelineAnterior: timeline, estadoAnterior: fluxoEstado});
      timeline = resultadoDecisaoRecurso.timelineAtualizada;
      fluxoEstado = resultadoDecisaoRecurso.estadoAtualizado;
      if (resultadoDecisaoRecurso.finalizarProcessamento) return limparTimelineFinal(timeline, processo, temDataDeposito);
    }

    // HANDLER: Tentar processar Concessão
    if (!fluxoEstado.dataConcessao && !fluxoEstado.arquivadoPorFaltaPagamentoTaxa) { 
        const resultadoConcessao = processarConcessaoRegistroHandler({...handlerParamsBase, timelineAnterior: timeline, estadoAnterior: fluxoEstado});
        timeline = resultadoConcessao.timelineAtualizada;
        fluxoEstado = resultadoConcessao.estadoAtualizado;
        if (resultadoConcessao.finalizarProcessamento) return limparTimelineFinal(timeline, processo, temDataDeposito);
    }

    // HANDLER: Tentar processar Arquivamento por Falta de Pagamento (despacho)
    if (!fluxoEstado.dataConcessao && !fluxoEstado.arquivadoPorFaltaPagamentoTaxa && !fluxoEstado.etapaAtualIdentificada) { 
        const resultadoArqTaxa = processarArquivamentoFaltaPagamentoHandler({...handlerParamsBase, timelineAnterior: timeline, estadoAnterior: fluxoEstado});
        timeline = resultadoArqTaxa.timelineAtualizada;
        fluxoEstado = resultadoArqTaxa.estadoAtualizado;
        if (resultadoArqTaxa.finalizarProcessamento) return limparTimelineFinal(timeline, processo, temDataDeposito);
    }
  } // Fim do loop de despachos

  // Lógica Pós-Loop

  // HANDLER PÓS-LOOP 0.5: Adicionar Análise de Recurso Mesmo Sem Apresentação
  if (!fluxoEstado.dataConcessao && !fluxoEstado.arquivadoPorFaltaPagamentoTaxa) {
    const etapaAtualNoMomento = timeline.find(e => e.status === 'ATUAL');
    if (etapaAtualNoMomento?.idOriginal === 'PRAZO_RECURSO_INDEFERIMENTO_01') {
      const posLoopParamsAnaliseRecurso = {
        processo,
        despachosOrdenados,
        timelineAnterior: timeline,
        estadoAtual: fluxoEstado,
        temDataDeposito,
        hoje
      };
      const resultadoAnaliseRecurso = processarAnaliseRecursoSemApresentacaoPosLoopHandler(posLoopParamsAnaliseRecurso);
      timeline = resultadoAnaliseRecurso.timelineAtualizada;
      fluxoEstado = resultadoAnaliseRecurso.estadoAtualizado;
      // Note: não verifica finalizarProcessamento pois este handler não finaliza
    }
  }

  // HANDLER PÓS-LOOP 1: Arquivamento por Recurso Não Apresentado
  if (!fluxoEstado.dataConcessao && !fluxoEstado.arquivadoPorFaltaPagamentoTaxa && !fluxoEstado.etapaAtualIdentificada) {
    const etapaAtualNoMomento = timeline.find(e => e.status === 'ATUAL');
    if (etapaAtualNoMomento?.idOriginal === 'PRAZO_RECURSO_INDEFERIMENTO_01' || !fluxoEstado.etapaAtualIdentificada) {
      const posLoopParamsArqSemRecurso = {
        processo,
        despachosOrdenados,
        timelineAnterior: timeline,
        estadoAtual: fluxoEstado,
        temDataDeposito,
        hoje
      };
      const resultadoArqSemRecurso = processarArquivamentoPorRecursoNaoApresentadoPosLoopHandler(posLoopParamsArqSemRecurso);
      timeline = resultadoArqSemRecurso.timelineAtualizada;
      fluxoEstado = resultadoArqSemRecurso.estadoAtualizado;
      if (resultadoArqSemRecurso.finalizarProcessamento) return limparTimelineFinal(timeline, processo, temDataDeposito);
    }
  }

  // HANDLER PÓS-LOOP 2: Prazos de Pagamento de Concessão (se deferido e não pago)
  if (fluxoEstado.dataDeferimento && !fluxoEstado.taxaConcessaoPaga && !fluxoEstado.dataConcessao && !fluxoEstado.arquivadoPorFaltaPagamentoTaxa) {
    const posLoopParamsPagamento = {
      processo,
      despachosOrdenados,
      timelineAnterior: timeline,
      estadoAtual: fluxoEstado,
      temDataDeposito,
      hoje
    };
    const resultadoPrazosPagamento = processarPrazosPagamentoPosLoopHandler(posLoopParamsPagamento);
    timeline = resultadoPrazosPagamento.timelineAtualizada;
    fluxoEstado = resultadoPrazosPagamento.estadoAtualizado;
    if (resultadoPrazosPagamento.finalizarProcessamento) return limparTimelineFinal(timeline, processo, temDataDeposito);
  }
  
  // HANDLER PÓS-LOOP 3: Lidar com expiração do prazo de nulidade SEM instauração
  if (fluxoEstado.dataConcessao && 
      !fluxoEstado.arquivadoPorFaltaPagamentoTaxa && 
      !timeline.some(e => e.idOriginal === 'PROCESSO_NULIDADE_INSTAURADO_01') &&
      !timeline.some(e => e.idOriginal === 'FIM_PRAZO_NULIDADE_01' && (e.status === 'ATUAL' || e.status === 'CONCLUIDA')) &&
      !timeline.some(e => e.idOriginal === 'ARQUIVAMENTO_01' && e.status === 'ATUAL')
      ) { 

    const fimPrazoNulidadeEtapa = timeline.find(e => e.idOriginal === 'FIM_PRAZO_NULIDADE_01' && e.status === 'PROXIMA_EXATA');
    if (fimPrazoNulidadeEtapa && fimPrazoNulidadeEtapa.dataInicio && hoje > fimPrazoNulidadeEtapa.dataInicio) {
        const resultadoFimPrazoNul = 
            processarFimPrazoNulidadeSemInstauracaoHandler({
                processo,
                despachosOrdenados,
                timelineAnterior: timeline,
                estadoAtual: fluxoEstado,
                temDataDeposito,
                hoje
            });
        timeline = resultadoFimPrazoNul.timelineAtualizada;
        fluxoEstado = resultadoFimPrazoNul.estadoAtualizado;
        if (fluxoEstado.etapaAtualIdentificada || resultadoFimPrazoNul.finalizarProcessamento) return limparTimelineFinal(timeline, processo, temDataDeposito); 
    }
  }

  // HANDLER PÓS-LOOP 4: Lidar com expiração do prazo de manifestação sobre nulidade SEM manifestação
  if (!fluxoEstado.etapaAtualIdentificada) {
      const prazoManifestNulAtiva = timeline.find(e => e.idOriginal === 'PRAZO_MANIFESTACAO_NULIDADE_01' && e.status === 'ATUAL');
      if (prazoManifestNulAtiva && prazoManifestNulAtiva.dataFim && hoje > prazoManifestNulAtiva.dataFim &&
          !timeline.some(e => e.idOriginal === 'MANIFESTACAO_NULIDADE_APRESENTADA_01' && e.dataInicio && prazoManifestNulAtiva.dataFim && e.dataInicio <= prazoManifestNulAtiva.dataFim)
        ) {
          const resultadoExpManifestNul = 
              processarExpiracaoPrazoManifestacaoNulidadeHandler({
                  processo,
                  despachosOrdenados,
                  timelineAnterior: timeline,
                  estadoAtual: fluxoEstado,
                  temDataDeposito,
                  hoje
              });
          timeline = resultadoExpManifestNul.timelineAtualizada;
          fluxoEstado = resultadoExpManifestNul.estadoAtualizado;
          if (fluxoEstado.etapaAtualIdentificada || resultadoExpManifestNul.finalizarProcessamento) return limparTimelineFinal(timeline, processo, temDataDeposito);
      }
  }
  
  // HANDLER PÓS-LOOP 5: Lidar com arquivamento após Recurso de Nulidade Negado (decurso de prazo)
  if (!fluxoEstado.etapaAtualIdentificada) { // Só roda se nenhuma etapa ATUAL definitiva foi encontrada antes
    const recursoNulidadeNegadoAtiva = timeline.find(e => e.idOriginal === 'RECURSO_NULIDADE_NEGADO_01' && e.status === 'ATUAL');
    if (recursoNulidadeNegadoAtiva) {
        const resultadoArqPosRecNulNeg = 
            processarArquivamentoPosRecursoNulidadeNegadoHandler({
                processo,
                despachosOrdenados,
                timelineAnterior: timeline,
                estadoAtual: fluxoEstado,
                temDataDeposito,
                hoje
            });
        timeline = resultadoArqPosRecNulNeg.timelineAtualizada;
        fluxoEstado = resultadoArqPosRecNulNeg.estadoAtualizado;
        if (fluxoEstado.etapaAtualIdentificada || resultadoArqPosRecNulNeg.finalizarProcessamento) return limparTimelineFinal(timeline, processo, temDataDeposito);
    }
  }
  
  // Se nenhuma etapa ATUAL foi identificada pelos handlers de despacho ou de prazos pós-loop,
  // aciona o handler de fluxo padrão (Protocolo, Publicação, Oposição, Mérito, Fallback etc.)
  if (!fluxoEstado.etapaAtualIdentificada) {
    const fluxoPadraoParams: FluxoPadraoParams = {
        processo,
        despachosOrdenados,
        timelineAnterior: timeline, 
        estadoAtual: fluxoEstado,   
        temDataDeposito,
        hoje,
        estimativasDb
    };
    const resultadoFluxoPadrao = processarFluxoPadraoTimelineHandler(fluxoPadraoParams);
    timeline = resultadoFluxoPadrao.timelineAtualizada;
    fluxoEstado = resultadoFluxoPadrao.estadoAtualizado;
    
    if (resultadoFluxoPadrao.finalizarProcessamento || fluxoEstado.etapaAtualIdentificada) {
       return limparTimelineFinal(timeline, processo, temDataDeposito);
    }
  }
  
  if (!fluxoEstado.etapaAtualIdentificada && !timeline.some(e => e.status === 'ATUAL')) {
    const definicaoFallback = encontrarDefinicaoEtapaPorId('LOGICA_EM_DESENVOLVIMENTO_01'); 
    if (definicaoFallback) {
      timeline.push({
        idOriginal: definicaoFallback.id,
        etapaDefinicao: definicaoFallback,
        status: 'ATUAL',
        dataInicio: hoje,
        observacaoGerada: "Lógica da timeline não determinou estado atual (fallback geral da função principal)."
      });
    }
  }

  return limparTimelineFinal(timeline, processo, temDataDeposito);
}

// Função auxiliar para limpar e organizar a timeline antes de retornar
function limparTimelineFinal(
    timeline: EtapaTimelineGerada[],
    processo: ProcessoParaTimeline,
    temDataDeposito: boolean
  ): EtapaTimelineGerada[] {
    
    const timelineProcessada = [...timeline];

    const etapasConcluidasVistas = new Set<string>();
    const timelineSemDuplicatasConcluidas: EtapaTimelineGerada[] = [];
    
    const atual = timelineProcessada.find(e => e.status === 'ATUAL');
    const proximas = timelineProcessada.filter(e => e.status === 'PROXIMA_EXATA' || e.status === 'PROXIMA_ESTIMADA').sort((a,b) => (a.dataInicio?.getTime() || 0) - (b.dataInicio?.getTime() || 0));
    const concluidas = timelineProcessada.filter(e => e.status === 'CONCLUIDA')
        .sort((a,b) => (a.dataInicio?.getTime() || 0) - (b.dataInicio?.getTime() || 0));

    let timelineFinal: EtapaTimelineGerada[] = [];

    concluidas.forEach(etapa => {
        if (!etapasConcluidasVistas.has(etapa.idOriginal)) {
            timelineFinal.push(etapa);
            etapasConcluidasVistas.add(etapa.idOriginal);
        }
    });

    if (atual) {
        const idxAtualEmFinal = timelineFinal.findIndex(e => e.idOriginal === atual.idOriginal);
        if (idxAtualEmFinal > -1) {
            timelineFinal.splice(idxAtualEmFinal, 1);
        }
        timelineFinal.push(atual);
    }
    proximas.forEach(p => {
        if (!timelineFinal.some(e => e.idOriginal === p.idOriginal)) {
             timelineFinal.push(p);
        }
    });
    
    // Adicionar "Análise de Mérito" como próxima se a etapa atual for Sobrestamento
    // e "Análise de Mérito" ainda não estiver listada como próxima.
    // Mas apenas se o mérito ainda não foi processado (não há deferimento/indeferimento/concessão/arquivamento).
    if (atual && atual.idOriginal === 'SOBRESTAMENTO_01' && !jaMeritoFoiProcessado(timelineFinal)) {
      const proximaEtapaRef = atual.etapaDefinicao.proximaEtapaNomeReferencia;
      if (proximaEtapaRef) {
        // Tentativa de mapear a referência genérica "Análise de mérito" para um ID específico.
        // Isso é uma simplificação. O ideal seria ter uma lógica mais robusta para escolher
        // qual "Análise de Mérito" (com/sem oposição/manifestação) é a correta aqui,
        // possivelmente baseada no estado ANTES do sobrestamento.
        // Por agora, vamos assumir que "ANALISE_MERITO_SEM_OPOSICAO_01" é um placeholder aceitável
        // ou que o nome da referência em etapasConfig para SOBRESTAMENTO_01.proximaEtapaNomeReferencia
        // seja um ID direto de uma das análises de mérito.
        // Para esta implementação, vamos usar o nome da referência diretamente, assumindo que ele é um ID.
        const definicaoProxima = encontrarDefinicaoEtapaPorId(proximaEtapaRef);

        if (definicaoProxima && !proximas.some(p => p.idOriginal === definicaoProxima.id)) {
            // Verifica se já não existe uma "próxima" etapa com este ID, 
            // para evitar duplicatas se alguma lógica anterior já a adicionou.
            const jaExisteProximaComMesmoId = timelineFinal.some(e => 
                (e.status === 'PROXIMA_EXATA' || e.status === 'PROXIMA_ESTIMADA') && 
                e.idOriginal === definicaoProxima.id
            );

            if (!jaExisteProximaComMesmoId) {
                timelineFinal.push({
                    idOriginal: definicaoProxima.id,
                    etapaDefinicao: definicaoProxima,
                    status: 'PROXIMA_ESTIMADA',
                    dataInicio: null, 
                    observacaoGerada: atual.etapaDefinicao.prazoProximaEtapaDescricao || "Próxima etapa após sobrestamento."
                });
            }
        }
      }
    }
    
    if (temDataDeposito && processo.dataDeposito) {
        const idxProtocolo = timelineFinal.findIndex(e => e.idOriginal === 'PROTOCOLO_01');
        if (idxProtocolo > -1) {
            const etapaProtocolo = timelineFinal.splice(idxProtocolo, 1)[0];
            if (etapaProtocolo.status === 'CONCLUIDA') { 
                 timelineFinal.unshift(etapaProtocolo);
            } else {
                 timelineFinal.push(etapaProtocolo); 
            }
        } else if (!timelineFinal.find(e => e.idOriginal === 'PROTOCOLO_01')) { 
            const definicaoProtocolo = encontrarDefinicaoEtapaPorId('PROTOCOLO_01');
            if (definicaoProtocolo) {
                timelineFinal.unshift({
                    idOriginal: definicaoProtocolo.id,
                    etapaDefinicao: definicaoProtocolo,
                    status: 'CONCLUIDA', 
                    dataInicio: new Date(processo.dataDeposito),
                });
            }
        }
    }
    
    const temOutraAtual = timelineFinal.some(e => e.status === 'ATUAL' && e.idOriginal !== 'LOGICA_EM_DESENVOLVIMENTO_01');
    if (temOutraAtual) {
        timelineFinal = timelineFinal.filter(e => e.idOriginal !== 'LOGICA_EM_DESENVOLVIMENTO_01');
    }

    return timelineFinal;
}

// Definição de LOGICA_EM_DESENVOLVIMENTO_01 deve estar em etapasConfig.ts
// Exemplo:
// {
//   id: 'LOGICA_EM_DESENVOLVIMENTO_01',
//   nomeOriginal: 'Lógica da Timeline em Desenvolvimento',
//   tipo: 'ACONTECIMENTO',
//   statusSimples: 'Em Andamento',
//   statusDetalhado: 'A lógica para determinar a etapa atual deste processo ainda está em desenvolvimento ou o estado do processo é inesperado.',
// } 