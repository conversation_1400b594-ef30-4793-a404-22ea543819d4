import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getAdminFromRequest, requireAdminRole } from '@/lib/adminAuth';

export async function GET(request: NextRequest) {
  try {
    // Verificar autenticação e role MASTER
    const user = await getAdminFromRequest(request);
    if (!user || !requireAdminRole(['MASTER'])(user)) {
      return NextResponse.json(
        { error: 'Acesso negado. Apenas usuários MASTER podem acessar esta funcionalidade.' },
        { status: 403 }
      );
    }
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const search = searchParams.get('search') || '';
    const orderBy = searchParams.get('orderBy') || 'id_desc';
    const exactMatch = searchParams.get('exactMatch') === 'true';

    const skip = (page - 1) * limit;

    // Função auxiliar para criar condição de busca baseada no tipo (exata ou radical)
    const createSearchCondition = (value: string) => {
      if (exactMatch) {
        return { equals: value, mode: 'insensitive' as const };
      } else {
        return { contains: value, mode: 'insensitive' as const };
      }
    };

    // Filtros para busca
    const whereClause = {
      autoLoginUrl: {
        not: null,
      },
      ...(search && {
        OR: [
          { nome: createSearchCondition(search) },
          { identificador: createSearchCondition(search) },
          { autoLoginUrl: createSearchCondition(search) },
        ],
      }),
    };

    // Buscar clientes com paginação
    const [clientes, totalCount] = await Promise.all([
      prisma.cliente.findMany({
        where: whereClause,
        select: {
          id: true,
          nome: true,
          identificador: true,
          autoLoginUrl: true,
          crmId: true,
          _count: {
            select: {
              SessionLog: true,
              ProtocoloDownloadLog: true,
            }
          }
        },
        orderBy: (() => {
          switch (orderBy) {
            case 'id_asc':
              return { id: 'asc' as const };
            case 'nome_asc':
              return { nome: 'asc' as const };
            case 'nome_desc':
              return { nome: 'desc' as const };
            case 'id_desc':
            default:
              return { id: 'desc' as const };
          }
        })(),
        skip,
        take: limit,
      }),
      prisma.cliente.count({
        where: whereClause,
      }),
    ]);

    const totalPages = Math.ceil(totalCount / limit);

    return NextResponse.json({
      clientes,
      pagination: {
        currentPage: page,
        totalPages,
        totalCount,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1,
      },
    });

  } catch (error) {
    console.error('Erro ao buscar clientes com autoLogin:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
} 