const fs = require('fs');
const path = require('path');

const processosDir = path.join(__dirname, '..', 'src', 'private-assets', 'processos');

console.log('🔍 Analisando imagens duplicadas na pasta:', processosDir);

if (!fs.existsSync(processosDir)) {
  console.error('❌ Pasta de processos não encontrada:', processosDir);
  process.exit(1);
}

const files = fs.readdirSync(processosDir);
const imageMap = new Map();
let duplicatesRemoved = 0;
let totalFiles = 0;

// Agrupar arquivos por nome base (sem extensão)
files.forEach(file => {
  const ext = path.extname(file).toLowerCase();
  const baseName = path.basename(file, ext);
  
  if (ext === '.jpg' || ext === '.jpeg' || ext === '.png') {
    totalFiles++;
    if (!imageMap.has(baseName)) {
      imageMap.set(baseName, []);
    }
    imageMap.get(baseName).push({
      file,
      ext,
      path: path.join(processosDir, file)
    });
  }
});

console.log(`📊 Total de imagens encontradas: ${totalFiles}`);

// Processar duplicatas
imageMap.forEach((files, baseName) => {
  if (files.length > 1) {
    console.log(`\n🔄 Processando duplicatas para: ${baseName}`);
    
    // Ordenar por prioridade: jpg/jpeg primeiro, depois png
    files.sort((a, b) => {
      const priorityA = (a.ext === '.jpg' || a.ext === '.jpeg') ? 1 : 0;
      const priorityB = (b.ext === '.jpg' || b.ext === '.jpeg') ? 1 : 0;
      return priorityB - priorityA;
    });
    
    const keepFile = files[0];
    const removeFiles = files.slice(1);
    
    console.log(`   ✅ Mantendo: ${keepFile.file}`);
    
    removeFiles.forEach(file => {
      try {
        fs.unlinkSync(file.path);
        console.log(`   🗑️  Removido: ${file.file}`);
        duplicatesRemoved++;
      } catch (error) {
        console.error(`   ❌ Erro ao remover ${file.file}:`, error.message);
      }
    });
  }
});

console.log(`\n✨ Processo concluído!`);
console.log(`📈 Arquivos removidos: ${duplicatesRemoved}`);
console.log(`📈 Arquivos restantes: ${totalFiles - duplicatesRemoved}`);

// Listar extensões restantes para relatório
const finalFiles = fs.readdirSync(processosDir);
const extensionCount = {};
finalFiles.forEach(file => {
  const ext = path.extname(file).toLowerCase();
  if (ext === '.jpg' || ext === '.jpeg' || ext === '.png') {
    extensionCount[ext] = (extensionCount[ext] || 0) + 1;
  }
});

console.log('\n📋 Resumo das extensões restantes:');
Object.entries(extensionCount).forEach(([ext, count]) => {
  console.log(`   ${ext}: ${count} arquivos`);
}); 