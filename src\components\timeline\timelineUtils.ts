import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import React from 'react';

// Interfaces
export interface EstimativasDb {
  dataPublicacaoEstimada?: string;
  dataFimOposicaoEstimado?: string;
  dataAnaliseMeritoEstimada?: string;
}

export interface EtapaTimeline {
  idOriginal: string;
  etapaDefinicao: {
    nomeOriginal: string;
    statusSimples: string;
    tipo?: string;
  };
  status: 'CONCLUIDA' | 'ATUAL' | 'PROXIMA_ESTIMADA' | 'PROXIMA_EXATA' | 'ALERTA_ACAO';
  dataInicio?: Date | null;
  dataFim?: Date | null;
  observacaoGerada?: string;
}

export interface MarcadorTimeline extends EtapaTimeline {
  posicao: number;
  posicaoPixels: number;
  isAlcancada: boolean;
  isPrazoPendente: boolean;
  isSobrestamento: boolean;
  isUltimaAnalise: boolean;
  isProtocolo: boolean;
  isArquivamento: boolean;
  isIndeferimento: boolean;
  isDeferimento: boolean;
  isConcessao: boolean;
  isRecursoExpirado: boolean;
}

export interface DataFormatada {
  tipo: 'confirmada' | 'previsao' | 'estimativa';
  texto?: string;
  linha1?: string;
  linha2?: string;
}

/**
 * Verifica se uma etapa é um prazo expirado
 */
export const isPrazoExpirado = (etapa: EtapaTimeline): boolean => {
  if (etapa.status === 'CONCLUIDA' || !etapa.dataFim) return false;
  
  // Exceção: Prazo para Manifestação de Nulidade nunca é considerado expirado
  if (etapa.idOriginal === 'PRAZO_MANIFESTACAO_NULIDADE_01') return false;
  
  return new Date(etapa.dataFim) < new Date();
};

/**
 * Verifica se é um recurso expirado sem apresentação
 */
export const isRecursoExpiradoSemApresentacao = (etapa: EtapaTimeline, etapasLimpas: EtapaTimeline[]): boolean => {
  const isRecurso = etapa.etapaDefinicao.nomeOriginal.toLowerCase().includes('recurso');
  if (!isRecurso) return false;
  
  const prazoExpirado = isPrazoExpirado(etapa);
  if (!prazoExpirado) return false;
  
  // Verificar se não há etapa de recurso apresentado
  const temRecursoApresentado = etapasLimpas.some(e => 
    e.idOriginal === 'RECURSO_INDEFERIMENTO_APRESENTADO_01'
  );
  
  return !temRecursoApresentado;
};

/**
 * Verifica se há sobrestamento na timeline
 */
export const temSobrestamento = (etapasLimpas: EtapaTimeline[]): boolean => {
  return etapasLimpas.some(e => 
    e.idOriginal === 'SOBRESTAMENTO_01' || 
    e.etapaDefinicao.nomeOriginal.toLowerCase().includes('sobrestamento')
  );
};

/**
 * Formatar data para exibição na timeline
 */
export const formatarDataExibicao = (etapa: EtapaTimeline, etapasLimpas: EtapaTimeline[]): DataFormatada | null => {
  // CASO ESPECIAL: Etapas estimadas de processo não publicado
  if (etapa.idOriginal === 'PUBLICACAO_ESTIMADA_01') {
    return {
      tipo: 'estimativa',
      linha1: '15 a 20 dias',
      linha2: 'após o protocolo'
    };
  }
  
  if (etapa.idOriginal === 'FIM_OPOSICAO_ESTIMADO_01') {
    return {
      tipo: 'estimativa',
      linha1: '60 dias após',
      linha2: 'a publicação'
    };
  }

  // Se há sobrestamento e esta é uma etapa de análise de mérito, omitir a data
  const isAnaliseMetito = etapa.etapaDefinicao.nomeOriginal.toLowerCase().includes('análise de mérito');
  if (temSobrestamento(etapasLimpas) && isAnaliseMetito) {
    return null; // Não mostrar data para análise de mérito quando há sobrestamento
  }

  // Usar função utilitária para determinar qual data usar
  const dataParaExibir = deveUsarDataFim(etapa) ? etapa.dataFim : etapa.dataInicio;
  
  if (!dataParaExibir) return null;

  const data = new Date(dataParaExibir);
  const nome = etapa.etapaDefinicao.nomeOriginal.toLowerCase();
  
  // Verificar se é uma etapa de análise (que tem datas estimadas)
  const isEtapaAnalise = nome.includes('análise de mérito') || 
                        nome.includes('análise do recurso') || 
                        nome.includes('análise de recurso');
  
  // Verificar se são etapas estimadas criadas pelo componente (formatação mais discreta)
  const isConcessaoEstimada = etapa.idOriginal === 'CONCESSAO_ESTIMADA_01';
  const isFimPrazoNulidadeEstimado = etapa.idOriginal === 'FIM_PRAZO_NULIDADE_ESTIMADO_01';
  const isAnaliseEstimada = etapa.idOriginal === 'ANALISE_MERITO_ESTIMADA_01';
  
  // Mostrar "Previsão" apenas para etapas de análise (independente do status se não for CONCLUIDA)
  if ((isEtapaAnalise || isAnaliseEstimada) && etapa.status !== 'CONCLUIDA') {
    const mes = format(data, 'MMM', { locale: ptBR });
    const ano = format(data, 'yyyy');
    return {
      tipo: 'previsao',
      linha1: "Previsão:",
      linha2: `${mes.charAt(0).toUpperCase() + mes.slice(1)}, ${ano}`
    };
  }
  
  // Para etapas estimadas, usar formatação mais discreta
  if (isConcessaoEstimada || isFimPrazoNulidadeEstimado) {
    const textoData = format(data, 'dd/MM/yyyy');
    
    // Apenas a concessão é realmente uma estimativa
    if (isConcessaoEstimada) {
      return {
        tipo: "estimativa",
        linha1: "(previsão)",
        linha2: textoData,
      };
    }
    
    // Fim do prazo de nulidade é um cálculo exato, usar formatação normal
    return {
      tipo: 'confirmada',
      texto: textoData
    };
  }

  // Data confirmada para todas as outras etapas
  const textoData = format(data, 'dd/MM/yyyy');
  return {
    tipo: 'confirmada',
    texto: textoData
  };
};

/**
 * Formatar data para exibição na versão mobile
 */
export const formatarDataExibicaoMobile = (etapa: EtapaTimeline, etapasLimpas: EtapaTimeline[]): DataFormatada | null => {
  // CASO ESPECIAL: Etapas estimadas de processo não publicado
  if (etapa.idOriginal === 'PUBLICACAO_ESTIMADA_01') {
    return {
      tipo: 'estimativa',
      texto: '15 a 20 dias após o protocolo'
    };
  }
  
  if (etapa.idOriginal === 'FIM_OPOSICAO_ESTIMADO_01') {
    return {
      tipo: 'estimativa',
      texto: '60 dias após a publicação'
    };
  }

  // Se há sobrestamento e esta é uma etapa de análise de mérito, omitir a data
  const isAnaliseMetito = etapa.etapaDefinicao.nomeOriginal.toLowerCase().includes('análise de mérito');
  if (temSobrestamento(etapasLimpas) && isAnaliseMetito) {
    return null;
  }

  // Usar função utilitária para determinar qual data usar
  const dataParaExibir = deveUsarDataFim(etapa) ? etapa.dataFim : etapa.dataInicio;
  
  if (!dataParaExibir) return null;

  const data = new Date(dataParaExibir);
  const nome = etapa.etapaDefinicao.nomeOriginal.toLowerCase();
  
  // Verificar se é uma etapa de análise (que tem datas estimadas)
  const isEtapaAnalise = nome.includes('análise de mérito') || 
                        nome.includes('análise do recurso') || 
                        nome.includes('análise de recurso');
  
  // Verificar se é concessão estimada ou fim de prazo de nulidade estimado
  const isConcessaoEstimada = etapa.idOriginal === 'CONCESSAO_ESTIMADA_01';
  const isFimPrazoNulidadeEstimado = etapa.idOriginal === 'FIM_PRAZO_NULIDADE_ESTIMADO_01';
  const isAnaliseEstimada = etapa.idOriginal === 'ANALISE_MERITO_ESTIMADA_01';
  
  // Mostrar "Previsão" para etapas de análise (independente do status se não for CONCLUIDA)
  if ((isEtapaAnalise && etapa.status !== 'CONCLUIDA') || isConcessaoEstimada || isFimPrazoNulidadeEstimado || isAnaliseEstimada) {
    const mes = format(data, 'MMM', { locale: ptBR });
    const ano = format(data, 'yyyy');
    return {
      tipo: 'previsao',
      texto: `Previsão: ${mes.charAt(0).toUpperCase() + mes.slice(1)}, ${ano}`
    };
  }

  // Data confirmada - adicionar contexto para prazos e recursos
  const textoData = format(data, 'dd/MM/yyyy');
  const isEtapaPrazo = etapa.etapaDefinicao.tipo === 'PRAZO' || nome.includes('prazo');
  const isEtapaRecurso = nome.includes('recurso');
  const prefixo = (isEtapaPrazo || isEtapaRecurso) && etapa.status === 'ATUAL' ? 'Até: ' : '';

  return {
    tipo: 'confirmada',
    texto: prefixo + textoData
  };
};

/**
 * Determinar se um marcador está alcançado e se é prazo pendente
 */
export const calcularStatusMarcador = (etapa: EtapaTimeline, etapasLimpasParaSobrestamento?: EtapaTimeline[]) => {
  const isPrazo = etapa.etapaDefinicao.tipo === 'PRAZO' || 
                  etapa.etapaDefinicao.nomeOriginal.toLowerCase().includes('prazo');
  const isRecurso = etapa.etapaDefinicao.nomeOriginal.toLowerCase().includes('recurso');
  
  // Identificar etapas de cumprir exigência
  const isCumprirExigencia = etapa.idOriginal === 'INDICADOR_EXIGENCIA_ATIVA_01' || 
                            etapa.etapaDefinicao.nomeOriginal.toLowerCase().includes('cumprir exigência') ||
                            etapa.etapaDefinicao.nomeOriginal.toLowerCase().includes('exigência');
  
  let isAlcancada = false;
  let isPrazoPendente = false;

  // Correção para Análise de Mérito com Sobrestamento
  const isAnaliseMetito = etapa.etapaDefinicao.nomeOriginal.toLowerCase().includes('análise de mérito');
  if (isAnaliseMetito && etapasLimpasParaSobrestamento && temSobrestamento(etapasLimpasParaSobrestamento)) {
    return { isAlcancada: false, isPrazoPendente: false };
  }
  
  if (etapa.status === 'CONCLUIDA') {
    isAlcancada = true;
  } else if (etapa.status === 'ATUAL') {
    // Para prazos, recursos e etapas de cumprir exigência, verificar se a data limite já passou
    if ((isPrazo || isRecurso || isCumprirExigencia) && etapa.dataFim) {
      const dataLimite = new Date(etapa.dataFim);
      const hoje = new Date();
      hoje.setHours(0, 0, 0, 0);
      dataLimite.setHours(0, 0, 0, 0);
      
      if (hoje >= dataLimite) {
        isAlcancada = true;
      } else {
        isPrazoPendente = true;
      }
    } else {
      // Para etapas que não são prazos, recursos ou exigências, status ATUAL significa alcançado
      isAlcancada = true;
    }
  }
  
  return { isAlcancada, isPrazoPendente };
};

/**
 * Filtrar etapas inválidas
 */
export const filtrarEtapasLimpas = (etapas: EtapaTimeline[]): EtapaTimeline[] => {
  let etapasProcessadas = [...etapas];

  // Filtrar "Em Andamento" quando há outras etapas válidas
  if (etapas.length > 1) {
    etapasProcessadas = etapasProcessadas.filter(etapa => 
      etapa.idOriginal !== 'LOGICA_EM_DESENVOLVIMENTO_01'
    );
  }

  // Filtrar PRAZO_MANIFESTACAO_NULIDADE_01 quando há decisão final de nulidade
  const temDecisaoFinalNulidade = etapasProcessadas.some(etapa => 
    etapa.idOriginal === 'CONCESSAO_MANTIDA_POS_NULIDADE_01' || 
    etapa.idOriginal === 'REGISTRO_ANULADO_01'
  );
  
  if (temDecisaoFinalNulidade) {
    etapasProcessadas = etapasProcessadas.filter(etapa => 
      etapa.idOriginal !== 'PRAZO_MANIFESTACAO_NULIDADE_01'
    );
  }

  // Filtrar INDICADOR_EXIGENCIA_ATIVA_01 quando há exigência cumprida
  const temExigenciaCumprida = etapasProcessadas.some(etapa => 
    etapa.idOriginal === 'EXIGENCIA_PETICAO_CUMPRIDA_01'
  );
  
  if (temExigenciaCumprida) {
    etapasProcessadas = etapasProcessadas.filter(etapa => 
      etapa.idOriginal !== 'INDICADOR_EXIGENCIA_ATIVA_01'
    );
  }

  // Se após filtrar não há etapas, manter as originais
  return etapasProcessadas.length > 0 ? etapasProcessadas : etapas;
};

// ==================== CONSTANTES ====================

export const TIMELINE_CONSTANTS = {
  DISTANCIA_MINIMA: 140, // Ajustada para 140px para evitar sobreposição de textos
  DISTANCIA_MINIMA_PROXIMOS: 150, // Também 140px para eventos próximos - não pode ser menor
  LARGURA_BASE: 600, // Largura base da timeline em pixels
  DISTANCIA_FINAL_ESPECIAIS: -145, // Distância do final para marcadores especiais (valor negativo)
  LARGURA_MINIMA: 600, // Largura mínima garantida
  LIMITE_PROGRESSO_ADICIONAL: 1, // 150% adicional máximo após última etapa
  LIMITE_TEMPORAL_PROXIMIDADE: 30, // Limiar em dias para considerar eventos "próximos" temporalmente
} as const;

// ==================== CONFIGURAÇÕES DE INTERPOLAÇÃO INTELIGENTE ====================

export const INTERPOLACAO_CONFIG = {
  GAP_LONGO_ANOS: 2,           // Quando considerar gap "longo" (2+ anos)
  FATOR_ACELERACAO_MAX: 1.8,   // Máximo de aceleração para gaps longos
  PROGRESSO_MINIMO_TEMPORAL: 0.6, // Progresso mínimo garantido por tempo decorrido
  BONUS_TEMPO_DECORRIDO: 0.1,  // Bonus adicional por tempo já decorrido
  LIMITE_INTERPOLACAO: 0.9,    // Nunca passar de 90% na interpolação
  ACELERACAO_INICIAL: 0.4,     // Aceleração nos primeiros 6 meses
  TEMPO_ACELERACAO_INICIAL: 0.5, // 6 meses em anos
  
  // NOVA CONFIGURAÇÃO: Aceleração por proximidade temporal
  PROXIMIDADE_TEMPORAL: {
    ANOS_CONSIDERADOS_PROXIMOS: 1.5,  // Etapas até 1.5 anos são "próximas"
    ACELERACAO_PROXIMIDADE: 1.8,      // Fator de aceleração para etapas próximas
    ANOS_CONSIDERADOS_DISTANTES: 2,   // Etapas a partir de 4 anos são "distantes"
    DESACELERACAO_DISTANCIA_INICIAL: 0.8, // Fator de desaceleração para etapas a 4 anos (era DESACELERACAO_DISTANCIA)
    ANOS_CONSIDERADOS_MUITO_DISTANTES: 4, // Novo: Limiar para desaceleração máxima
    DESACELERACAO_DISTANCIA_MAXIMA: 0.20   // Novo: Fator de desaceleração para etapas a 8+ anos
  }
} as const;

// ==================== CONFIGURAÇÕES DE PROGRESSO PÓS-ALCANCE ====================

export const PROGRESSO_POS_ALCANCE_CONFIG = {
  DIAS_PROGRESSO_EXTRA: 30,    // Quantos dias continuar progredindo após alcançar etapa
  PROGRESSO_MAXIMO_EXTRA: 0.25, // Máximo 25% de progresso adicional após o marcador (aumentado de 15%)
  CURVA_DESACELERACAO: 2,      // Curva de desaceleração (2 = quadrática suave)
  APLICAR_TODAS_ALCANCADAS: true, // Aplicar a TODAS as etapas alcançadas (não só as últimas)
  PROGRESSO_MINIMO_GARANTIDO: 0.20// Mínimo 5% de progresso além do marcador se data já passou
} as const;

// ==================== INTERFACES ADICIONAIS ====================

export interface PosicaoCalculada {
  etapa: EtapaTimeline;
  posicaoPixels: number;
  index: number;
  tipo: 'fixo' | 'temporal' | 'final';
  dataReferencia?: Date;
}

export interface ResultadoCalculoTimeline {
  marcadores: MarcadorTimeline[];
  progressWidth: number;
}

export interface ConfiguracaoTimeline {
  tipo: 'normal' | 'indeferimento' | 'arquivamento' | 'deferimento' | 'concessao';
  etapas: EtapaTimeline[];
  usarEscalaLogaritmica?: boolean;
}

// ==================== FUNÇÕES DE IDENTIFICAÇÃO ====================

/**
 * Verifica se uma etapa deve usar dataFim em vez de dataInicio
 */
export const deveUsarDataFim = (etapa: EtapaTimeline): boolean => {
  const nome = etapa.etapaDefinicao.nomeOriginal.toLowerCase();
  const id = etapa.idOriginal;
  
  // Etapas de cumprir exigência sempre usam dataFim (prazo limite para cumprimento)
  const isCumprirExigencia = id === 'INDICADOR_EXIGENCIA_ATIVA_01' || 
                            nome.includes('cumprir exigência') ||
                            nome.includes('exigência');
  if (isCumprirExigencia && etapa.dataFim) {
    return true;
  }
  
  // Recurso apresentado sempre usa dataFim (data de apresentação)
  const isRecursoApresentado = nome.includes('recurso publicado') || nome.includes('recurso interposto');
  if (isRecursoApresentado && etapa.dataFim) {
    return true;
  }
  
  // Para etapa "Recurso" (prazo), verificar se dataInicio e dataFim são diferentes
  // Se forem iguais, significa que não temos o prazo limite correto, então usar dataInicio
  const isRecursoPrazo = nome === 'recurso' && etapa.etapaDefinicao.tipo === 'PRAZO';
  if (isRecursoPrazo && etapa.dataInicio && etapa.dataFim) {
    const dataInicioTime = new Date(etapa.dataInicio).getTime();
    const dataFimTime = new Date(etapa.dataFim).getTime();
    // Se as datas são muito próximas (menos de 7 dias), provavelmente dataFim não é o prazo limite correto
    const diferencaDias = Math.abs(dataFimTime - dataInicioTime) / (1000 * 60 * 60 * 24);
    if (diferencaDias < 7) {
      return false; // Usar dataInicio
    }
    return true; // Usar dataFim (prazo limite)
  }
  
  // Outros prazos usam dataFim se disponível
  const isEtapaPrazo = etapa.etapaDefinicao.tipo === 'PRAZO' || nome.includes('prazo');
  const isEtapaRecurso = nome.includes('recurso') && !isRecursoApresentado;
  
  return (isEtapaPrazo || isEtapaRecurso) && !!etapa.dataFim;
};

/**
 * Obtém a data de referência para uma etapa
 */
export const obterDataReferencia = (etapa: EtapaTimeline): Date | null => {
  const usarDataFim = deveUsarDataFim(etapa);
  const dataReferencia = usarDataFim ? etapa.dataFim : etapa.dataInicio;
  return dataReferencia ? new Date(dataReferencia) : null;
};

/**
 * Identifica se uma etapa é um marcador especial que deve ficar no final
 */
export const isMarcadorEspecial = (etapa: EtapaTimeline): boolean => {
  const nome = etapa.etapaDefinicao.nomeOriginal.toLowerCase();
  return (
    nome.includes('análise do recurso') || 
    nome.includes('análise de recurso') || 
    nome.includes('análise de mérito') ||
    etapa.idOriginal === 'ANALISE_RECURSO_INDEFERIMENTO_01'
  );
};

/**
 * Identifica o tipo de etapa para posicionamento
 */
export const getTipoEtapa = (etapa: EtapaTimeline): string => {
  const nome = etapa.etapaDefinicao.nomeOriginal.toLowerCase();
  const id = etapa.idOriginal;
  
  // Verificar IDs específicos primeiro (mais confiável)
  if (id === 'PROTOCOLO_01') return 'protocolo';
  if (id === 'PUBLICACAO_01') return 'publicacao';
  if (id === 'INDEFERIMENTO_PEDIDO_01') return 'indeferimento';
  if (id === 'ARQUIVAMENTO_01') return 'arquivamento';
  if (id === 'CONCESSAO_REGISTRO_01') return 'concessao';
  if (id === 'DEFERIMENTO_PEDIDO_01' || id === 'DEFERIMENTO_POS_RECURSO_01') return 'deferimento';
  
  // Verificações por nome (mais específicas primeiro)
  if (nome === 'protocolo') return 'protocolo';
  if (nome === 'publicação') return 'publicacao';
  if (nome === 'indeferimento') return 'indeferimento';
  if (nome === 'arquivamento') return 'arquivamento';
  if (nome === 'concessão do registro' || nome === 'concessão') return 'concessao';
  if (nome === 'deferimento do pedido' || nome === 'deferimento') return 'deferimento';
  
  // Verificações mais genéricas (cuidado com falso positivos)
  if (nome.includes('protocolo')) return 'protocolo';
  if (nome.includes('publicação')) return 'publicacao';
  if (nome.includes('indeferimento')) return 'indeferimento';
  if (nome.includes('arquivamento')) return 'arquivamento';
  if (nome.includes('fim') && nome.includes('oposição')) return 'fim_oposicao';
  if (nome.includes('prazo') && nome.includes('oposição')) return 'fim_oposicao';
  if (nome.includes('manifestação')) return 'manifestacao';
  
  // NOVA DETECÇÃO: Análise de mérito e similares
  if (nome.includes('análise de mérito') || nome.includes('analise de merito')) return 'analise';
  if (nome.includes('análise do recurso') || nome.includes('analise do recurso')) return 'analise';
  if (nome.includes('análise') || nome.includes('analise')) return 'analise';
  
  // Verificações específicas para deferimento e concessão (evitar conflitos)
  if (nome.includes('deferimento') && !nome.includes('concessão')) return 'deferimento';
  if (nome.includes('concessão') && !nome.includes('pagamento') && !nome.includes('taxa')) return 'concessao';
  
  return 'outros';
};

// ==================== FUNÇÕES DE CÁLCULO DE POSIÇÕES ====================

/**
 * Separa etapas com e sem datas válidas
 */
export const separarEtapasPorDatas = (etapas: EtapaTimeline[]) => {
  const etapasComDatas = etapas
    .filter(etapa => !!obterDataReferencia(etapa))
    .map((etapa, _, array) => ({
      etapa,
      dataReferencia: obterDataReferencia(etapa)!,
      index: array.indexOf(etapa)
    }))
    .sort((a, b) => a.dataReferencia.getTime() - b.dataReferencia.getTime());

  const etapasSemDatas = etapas.filter(etapa => !obterDataReferencia(etapa));

  return { etapasComDatas, etapasSemDatas };
};

/**
 * Calcula posições temporais proporcionais
 * Com lógica inteligente que preserva gaps temporais grandes
 */
export const calcularPosicoesTemporais = (
  etapasComDatas: Array<{ etapa: EtapaTimeline; dataReferencia: Date; index: number }>,
  configuracao: { usarEscalaLogaritmica?: boolean; larguraBase?: number } = {}
): PosicaoCalculada[] => {
  const { usarEscalaLogaritmica = false, larguraBase = TIMELINE_CONSTANTS.LARGURA_BASE } = configuracao;
  const { DISTANCIA_MINIMA } = TIMELINE_CONSTANTS;

  if (etapasComDatas.length === 0) {
    return [];
  }

  const primeiraData = etapasComDatas[0].dataReferencia;
  const ultimaData = etapasComDatas[etapasComDatas.length - 1].dataReferencia;
  const tempoTotal = ultimaData.getTime() - primeiraData.getTime();

  // Calcular posições temporais básicas
  const posicoesTemporais = etapasComDatas.map((item, index) => {
    let posicaoPixels = 0;
    
    if (index === 0) {
      posicaoPixels = 0;
    } else if (tempoTotal > 0) {
      const tempoDecorrido = item.dataReferencia.getTime() - primeiraData.getTime();
      
      if (usarEscalaLogaritmica) {
        // Escala logarítmica para timeline de indeferimento
        const proporcaoLinear = tempoDecorrido / tempoTotal;
        const proporcaoLog = Math.log(1 + proporcaoLinear * 9) / Math.log(10);
        posicaoPixels = proporcaoLog * larguraBase;
      } else {
        // Escala linear normal
        const proporcao = tempoDecorrido / tempoTotal;
        posicaoPixels = proporcao * larguraBase;
      }
    } else {
      posicaoPixels = index * DISTANCIA_MINIMA;
    }

    return {
      etapa: item.etapa,
      posicaoPixels,
      index: item.index,
      tipo: 'temporal' as const,
      dataReferencia: item.dataReferencia
    };
  });

  // NOVA LÓGICA: Aplicar distância mínima inteligente que preserva gaps grandes
  for (let i = 1; i < posicoesTemporais.length; i++) {
    const anterior = posicoesTemporais[i - 1];
    const atual = posicoesTemporais[i];
    
    // Calcular diferença temporal em anos
    const diferencaMs = atual.dataReferencia.getTime() - anterior.dataReferencia.getTime();
    const diferencaAnos = diferencaMs / (1000 * 60 * 60 * 24 * 365.25);
    
    // Calcular distância atual em pixels
    const distanciaAtualPixels = atual.posicaoPixels - anterior.posicaoPixels;
    
    // Se é um gap temporal muito grande (>3 anos), usar escala especial
    if (diferencaAnos > 3) {
      // Para gaps muito grandes, garantir representação proporcional mínima
      // Cada ano acima de 3 deve ter pelo menos 50px extras
      const anosExtras = diferencaAnos - 3;
      const pixelsMinimosParaGap = DISTANCIA_MINIMA + (anosExtras * 50);
      
      if (distanciaAtualPixels < pixelsMinimosParaGap) {
        atual.posicaoPixels = anterior.posicaoPixels + pixelsMinimosParaGap;
      }
    } else if (diferencaAnos > 1) {
      // Para gaps médios (1-3 anos), usar distância proporcional
      const pixelsMinimosParaGap = DISTANCIA_MINIMA + (diferencaAnos - 1) * 30;
      
      if (distanciaAtualPixels < pixelsMinimosParaGap) {
        atual.posicaoPixels = anterior.posicaoPixels + pixelsMinimosParaGap;
      }
    } else {
      // Para gaps pequenos (<1 ano), aplicar distância mínima normal apenas se necessário
      if (distanciaAtualPixels < DISTANCIA_MINIMA) {
        atual.posicaoPixels = anterior.posicaoPixels + DISTANCIA_MINIMA;
      }
    }
  }

  return posicoesTemporais;
};

/**
 * Calcula posições para etapas sem datas
 */
export const calcularPosicoesSemDatas = (
  etapasSemDatas: EtapaTimeline[],
  larguraComDatas: number,
  etapasOriginais: EtapaTimeline[]
): PosicaoCalculada[] => {
  const { DISTANCIA_MINIMA, DISTANCIA_FINAL_ESPECIAIS } = TIMELINE_CONSTANTS;

  return etapasSemDatas.map((etapa, index) => {
    let posicaoPixels = larguraComDatas + (index * DISTANCIA_MINIMA);
    
    // Marcadores especiais ficam a distância fixa do final
    if (isMarcadorEspecial(etapa)) {
      posicaoPixels = DISTANCIA_FINAL_ESPECIAIS; // Valor negativo indica distância do final
    }

    return {
      etapa,
      posicaoPixels,
      index: etapasOriginais.indexOf(etapa),
      tipo: 'final' as const
    };
  });
};

/**
 * Função principal para calcular todas as posições de uma timeline
 */
export const calcularPosicoesTimeline = (
  etapas: EtapaTimeline[],
  configuracao: ConfiguracaoTimeline
): PosicaoCalculada[] => {
  const { etapasComDatas, etapasSemDatas } = separarEtapasPorDatas(etapas);

  // Se não há etapas com datas, usar posicionamento fixo
  if (etapasComDatas.length === 0) {
    return etapas.map((etapa, index) => ({
      etapa,
      posicaoPixels: index * TIMELINE_CONSTANTS.DISTANCIA_MINIMA,
      index,
      tipo: 'fixo' as const
    }));
  }

  // Calcular posições temporais
  const posicoesTemporais = calcularPosicoesTemporais(etapasComDatas, {
    usarEscalaLogaritmica: configuracao.usarEscalaLogaritmica,
    larguraBase: TIMELINE_CONSTANTS.LARGURA_BASE
  });

  // Calcular largura ocupada pelas etapas com datas
  const larguraComDatas = posicoesTemporais.length > 0 
    ? Math.max(...posicoesTemporais.map(p => p.posicaoPixels)) + TIMELINE_CONSTANTS.DISTANCIA_MINIMA
    : 0;

  // Calcular posições para etapas sem datas
  const posicoesSemDatas = calcularPosicoesSemDatas(etapasSemDatas, larguraComDatas, etapas);

  // Combinar todas as posições
  const todasPosicoes: PosicaoCalculada[] = [...posicoesTemporais, ...posicoesSemDatas];
  
  // Aplicar distância mínima global para evitar sobreposições
  return aplicarDistanciaMinimaGlobal(todasPosicoes);
};

/**
 * Detecta se a timeline precisa de ajuste de escala temporal
 * e sugere uso de escala logarítmica se necessário
 */
export const detectarNecessidadeEscalaLogaritmica = (
  etapasComDatas: Array<{ etapa: EtapaTimeline; dataReferencia: Date; index: number }>
): boolean => {
  if (etapasComDatas.length < 2) return false;
  
  const primeiraData = etapasComDatas[0].dataReferencia;
  const ultimaData = etapasComDatas[etapasComDatas.length - 1].dataReferencia;
  const tempoTotalAnos = Math.abs(ultimaData.getTime() - primeiraData.getTime()) / (1000 * 60 * 60 * 24 * 365.25);
  
  // Condição 1: Timeline muito longa (>8 anos) - quase sempre precisa de escala logarítmica
  if (tempoTotalAnos > 8) {
    return true;
  }
  
  // Condição 2: Timeline longa (>3 anos) com eventos concentrados no início
  if (tempoTotalAnos > 3) {
    // Verificar se há muitos eventos concentrados no primeiro terço
    const primeiroTerco = primeiraData.getTime() + (ultimaData.getTime() - primeiraData.getTime()) / 3;
    const eventosNoPrimeiroTerco = etapasComDatas.filter(e => e.dataReferencia.getTime() <= primeiroTerco).length;
    
    // Se mais de 60% dos eventos estão no primeiro terço, usar escala logarítmica
    if ((eventosNoPrimeiroTerco / etapasComDatas.length) > 0.6) {
      return true;
    }
  }
  
  // Condição 3: Verificar se há um gap muito grande entre eventos consecutivos
  for (let i = 1; i < etapasComDatas.length; i++) {
    const anterior = etapasComDatas[i - 1];
    const atual = etapasComDatas[i];
    const gapAnos = (atual.dataReferencia.getTime() - anterior.dataReferencia.getTime()) / (1000 * 60 * 60 * 24 * 365.25);
    
    // Se há um gap de mais de 5 anos entre eventos, usar escala logarítmica
    if (gapAnos > 5) {
      return true;
    }
  }
  
  return false;
};

// ==================== FUNÇÕES DE CONVERSÃO E LARGURA ====================

/**
 * Aplica distância mínima entre TODOS os marcadores para evitar sobreposição
 * Com lógica inteligente que preserva representação temporal já calculada
 */
export const aplicarDistanciaMinimaGlobal = (
  posicoes: PosicaoCalculada[]
): PosicaoCalculada[] => {
  const { DISTANCIA_MINIMA } = TIMELINE_CONSTANTS;
  
  // Ordenar por posição de pixels
  const posicoesOrdenadas = [...posicoes].sort((a, b) => a.posicaoPixels - b.posicaoPixels);
  
  // Aplicar ajustes apenas quando há sobreposição real
  for (let i = 1; i < posicoesOrdenadas.length; i++) {
    const anterior = posicoesOrdenadas[i - 1];
    const atual = posicoesOrdenadas[i];
    
    // Calcular distância atual
    const distanciaAtual = atual.posicaoPixels - anterior.posicaoPixels;
    
    // Verificar se são eventos temporais com diferença temporal
    const saoAmbosTemporais = anterior.tipo === 'temporal' && atual.tipo === 'temporal';
    let diferencaAnos = 0;
    
    if (saoAmbosTemporais && anterior.dataReferencia && atual.dataReferencia) {
      const diferencaMs = atual.dataReferencia.getTime() - anterior.dataReferencia.getTime();
      diferencaAnos = diferencaMs / (1000 * 60 * 60 * 24 * 365.25);
    }
    
    // SÓ ajustar se há sobreposição real (menos de 140px) E não é um gap temporal grande
    if (distanciaAtual < DISTANCIA_MINIMA) {
      // Para gaps temporais grandes (>2 anos), aplicar ajuste muito conservador
      if (diferencaAnos > 2) {
        // Apenas um pequeno ajuste para evitar sobreposição
        atual.posicaoPixels = anterior.posicaoPixels + Math.max(distanciaAtual + 20, DISTANCIA_MINIMA * 0.9);
      } 
      // Para gaps médios (6 meses - 2 anos), ajuste moderado
      else if (diferencaAnos > 0.5) {
        atual.posicaoPixels = anterior.posicaoPixels + Math.max(distanciaAtual + 30, DISTANCIA_MINIMA * 0.95);
      }
      // Para gaps pequenos (<6 meses), aplicar distância mínima completa
      else {
        atual.posicaoPixels = anterior.posicaoPixels + DISTANCIA_MINIMA;
      }
    }
  }
  
  return posicoesOrdenadas;
};

/**
 * Calcula a largura total da timeline considerando posições especiais
 */
export const calcularLarguraTotal = (posicoes: PosicaoCalculada[]): number => {
  const { LARGURA_MINIMA } = TIMELINE_CONSTANTS;
  
  // Filtrar posições positivas (excluir marcadores especiais com valores negativos)
  const posicoesPositivas = posicoes.filter(p => p.posicaoPixels >= 0);
  
  const larguraBasica = posicoesPositivas.length > 0 
    ? Math.max(...posicoesPositivas.map(p => p.posicaoPixels)) + 150
    : TIMELINE_CONSTANTS.LARGURA_BASE;
  
  // Garantir largura mínima para acomodar marcadores especiais
  return Math.max(larguraBasica, LARGURA_MINIMA);
};

/**
 * Converte posições negativas para posições reais (distância do final)
 */
export const converterPosicoesNegativas = (
  posicoes: PosicaoCalculada[], 
  larguraTotal: number
): PosicaoCalculada[] => {
  return posicoes.map(pos => ({
    ...pos,
    posicaoPixels: pos.posicaoPixels < 0 
      ? larguraTotal + pos.posicaoPixels // -50 vira larguraTotal - 50
      : pos.posicaoPixels
  }));
};

/**
 * Converte posições de pixels para marcadores da timeline
 */
export const converterParaMarcadores = (
  etapas: EtapaTimeline[],
  posicoes: PosicaoCalculada[],
  larguraTotal: number,
  configuracao: { tipo?: 'normal' | 'indeferimento' | 'arquivamento' | 'deferimento' | 'concessao' } = {}
): MarcadorTimeline[] => {
  return etapas.map((etapa, index) => {
    const itemAjustado = posicoes.find(p => p.index === index);
    let posicaoPixels = itemAjustado?.posicaoPixels || 0;
    
    // CORREÇÃO: Garantir que o primeiro marcador sempre tenha posição 0
    if (index === 0) {
      posicaoPixels = 0;
    }
    
    // Verificar se há arquivamento virtual na timeline (caso especial de recurso + arquivamento)
    const temArquivamentoVirtual = etapas.some(e => e.idOriginal === 'ARQUIVAMENTO_VIRTUAL_01');
    
    // Caso especial: Recurso expirado sem apresentação vai para o final
    // EXCETO quando há arquivamento virtual (nesse caso, usar posição temporal)
    if (isRecursoExpiradoSemApresentacao(etapa, etapas) && !temArquivamentoVirtual) {
      posicaoPixels = larguraTotal - 50;
    }
    
    let posicaoPercent = (posicaoPixels / larguraTotal) * 100;
    
    // CORREÇÃO: Garantir que o primeiro marcador sempre tenha posição 0%
    if (index === 0) {
      posicaoPercent = 0;
    }
    
    // Caso especial: Recurso expirado sem apresentação vai para 100%
    // EXCETO quando há arquivamento virtual (nesse caso, usar posição temporal)
    if (isRecursoExpiradoSemApresentacao(etapa, etapas) && !temArquivamentoVirtual) {
      posicaoPercent = 100;
    }

    // AJUSTE CONDICIONAL PARA SOBRESTAMENTO: Move um pouco para a esquerda apenas se necessário
    const isSobrestamento = etapa.idOriginal === 'SOBRESTAMENTO_01' || 
                           etapa.etapaDefinicao.nomeOriginal.toLowerCase().includes('sobrestamento');
    if (isSobrestamento && posicaoPercent > 10) {
      // Verificar se a distância de datas entre sobrestamento e marcador anterior é superior a 2 anos
      let deveAplicarDeslocamento = false;
      
      const dataSobrestamento = obterDataReferencia(etapa);
      if (dataSobrestamento && index > 0) {
        // Encontrar o marcador anterior com data
        for (let i = index - 1; i >= 0; i--) {
          const etapaAnterior = etapas[i];
          const dataAnterior = obterDataReferencia(etapaAnterior);
          
          if (dataAnterior) {
            const diferencaMs = dataSobrestamento.getTime() - dataAnterior.getTime();
            const diferencaAnos = diferencaMs / (1000 * 60 * 60 * 24 * 365.25); // Considera anos bissextos
            
            if (diferencaAnos > 1.2) {
              deveAplicarDeslocamento = true;
            }
            break; // Para após encontrar o primeiro marcador anterior com data
          }
        }
      }
      
      if (deveAplicarDeslocamento) {
        posicaoPercent = Math.max(10, posicaoPercent - 15); // Move 15% para a esquerda, mínimo 10%
      }
    }

    const isAnaliseMetito = etapa.etapaDefinicao.nomeOriginal.toLowerCase().includes('análise de mérito');
    
    // Verificar se há sobrestamento na timeline
    const temSobrestamentoNaTimeline = temSobrestamento(etapas);

    // Calcular status do marcador
    let { isAlcancada, isPrazoPendente } = calcularStatusMarcador(etapa);
    
    // Correção: etapas de análise de mérito não devem ser consideradas alcançadas quando há sobrestamento
    if (isAnaliseMetito && temSobrestamentoNaTimeline) {
      isAlcancada = false;
      isPrazoPendente = false;
    }

    // Para recursos expirados, usar 100%, para outros limitar a 95%
    // EXCETO quando há arquivamento virtual (usar posição calculada)
    const posicaoFinal = isRecursoExpiradoSemApresentacao(etapa, etapas) && !temArquivamentoVirtual
      ? posicaoPercent 
      : Math.max(0, Math.min(95, posicaoPercent));

    const isDeferimento = configuracao.tipo === 'deferimento' && index === 0;
    
    return {
      ...etapa,
      posicao: posicaoFinal,
      posicaoPixels,
      isAlcancada,
      isPrazoPendente,
      isSobrestamento,
      isUltimaAnalise: temSobrestamentoNaTimeline && isAnaliseMetito,
      isProtocolo: getTipoEtapa(etapa) === 'protocolo',
      isArquivamento: getTipoEtapa(etapa) === 'arquivamento',
      isIndeferimento: configuracao.tipo === 'indeferimento' && index === 0,
      isDeferimento,
      isConcessao: configuracao.tipo === 'concessao' && index === 0,
      isRecursoExpirado: isRecursoExpiradoSemApresentacao(etapa, etapas)
    };
  });
};

// ==================== FUNÇÕES DE CÁLCULO DE PROGRESSO ====================

/**
 * Prepara marcadores com datas para cálculo de progresso
 */
export const prepararMarcadoresComDatas = (marcadores: MarcadorTimeline[]): Array<MarcadorTimeline & { dataReferencia: Date }> => {
  return marcadores
    .filter(m => !!obterDataReferencia(m))
    .map(m => ({
      ...m,
      dataReferencia: obterDataReferencia(m)!,
    }))
    .sort((a, b) => a.dataReferencia.getTime() - b.dataReferencia.getTime());
};

/**
 * Calcula interpolação inteligente baseada no contexto temporal
 * Resolve o problema de progresso "congelado" em gaps temporais longos
 * NOVA: Considera proximidade temporal da próxima etapa
 */
export const calcularInterpolacaoInteligente = (
  atual: MarcadorTimeline & { dataReferencia: Date },
  proximo: MarcadorTimeline & { dataReferencia: Date },
  hoje: Date
): number => {
  const tempoTotal = proximo.dataReferencia.getTime() - atual.dataReferencia.getTime();
  const tempoDecorrido = hoje.getTime() - atual.dataReferencia.getTime();
  const proporcaoLinear = tempoDecorrido / tempoTotal;
  
  const tempoTotalAnos = tempoTotal / (1000 * 60 * 60 * 24 * 365.25);
  const tempoDecorridoAnos = tempoDecorrido / (1000 * 60 * 60 * 24 * 365.25);
  
  // NOVA LÓGICA: Calcular proximidade temporal da próxima etapa
  const tempoRestanteAnos = (proximo.dataReferencia.getTime() - hoje.getTime()) / (1000 * 60 * 60 * 24 * 365.25);
  const { PROXIMIDADE_TEMPORAL } = INTERPOLACAO_CONFIG;
  
  let proporcaoFinal = proporcaoLinear;
  const debugInfo = {
    proporcaoLinear,
    tempoTotalAnos: Math.round(tempoTotalAnos * 10) / 10,
    tempoDecorridoAnos: Math.round(tempoDecorridoAnos * 10) / 10,
    tempoRestanteAnos: Math.round(tempoRestanteAnos * 10) / 10,
    estrategiasAplicadas: [] as string[]
  };
  
  // ESTRATÉGIA 1: Aceleração/Desaceleração por proximidade temporal
  let fatorProximidade = 1; // Neutro por padrão
  let debugTipoFator = "😐 Neutro";

  if (tempoRestanteAnos <= PROXIMIDADE_TEMPORAL.ANOS_CONSIDERADOS_PROXIMOS) {
    fatorProximidade = PROXIMIDADE_TEMPORAL.ACELERACAO_PROXIMIDADE;
    debugTipoFator = "🚀 Acel. Próximo";
  } else if (tempoRestanteAnos < PROXIMIDADE_TEMPORAL.ANOS_CONSIDERADOS_DISTANTES) {
    const posicaoNaFaixaMedia = (tempoRestanteAnos - PROXIMIDADE_TEMPORAL.ANOS_CONSIDERADOS_PROXIMOS) /
                              (PROXIMIDADE_TEMPORAL.ANOS_CONSIDERADOS_DISTANTES - PROXIMIDADE_TEMPORAL.ANOS_CONSIDERADOS_PROXIMOS);
    fatorProximidade = PROXIMIDADE_TEMPORAL.ACELERACAO_PROXIMIDADE * (1 - posicaoNaFaixaMedia) +
                      PROXIMIDADE_TEMPORAL.DESACELERACAO_DISTANCIA_INICIAL * posicaoNaFaixaMedia;
    debugTipoFator = "⚡ Interp. Médio";
  } else if (tempoRestanteAnos < PROXIMIDADE_TEMPORAL.ANOS_CONSIDERADOS_MUITO_DISTANTES) {
    const posicaoNaFaixaDistante = (tempoRestanteAnos - PROXIMIDADE_TEMPORAL.ANOS_CONSIDERADOS_DISTANTES) /
                                 (PROXIMIDADE_TEMPORAL.ANOS_CONSIDERADOS_MUITO_DISTANTES - PROXIMIDADE_TEMPORAL.ANOS_CONSIDERADOS_DISTANTES);
    fatorProximidade = PROXIMIDADE_TEMPORAL.DESACELERACAO_DISTANCIA_INICIAL * (1 - posicaoNaFaixaDistante) +
                      PROXIMIDADE_TEMPORAL.DESACELERACAO_DISTANCIA_MAXIMA * posicaoNaFaixaDistante;
    debugTipoFator = "🐌 Desac. Distante";
  } else { // tempoRestanteAnos >= PROXIMIDADE_TEMPORAL.ANOS_CONSIDERADOS_MUITO_DISTANTES
    fatorProximidade = PROXIMIDADE_TEMPORAL.DESACELERACAO_DISTANCIA_MAXIMA;
    debugTipoFator = "🧊 Desac. Máxima";
  }
  
  // Fórmula unificada para aceleração/desaceleração
  // Se proporcaoLinear for 0, o resultado de Math.pow(0, exp) é 0 (para exp > 0) ou Infinity (para exp < 0).
  // 1/fatorProximidade pode ser < 0 se fatorProximidade for negativo, mas nossos fatores são positivos.
  // Se fatorProximidade é muito pequeno (e.g., 0.2), 1/fatorProximidade é grande (e.g., 5).
  // Math.pow(0, 5) é 0. Math.pow(0, 0.5) é 0.
  if (proporcaoLinear === 0) {
    proporcaoFinal = 0;
  } else if (proporcaoLinear === 1) {
    proporcaoFinal = 1;
  } else {
    proporcaoFinal = Math.pow(proporcaoLinear, 1 / fatorProximidade);
  }
  debugInfo.estrategiasAplicadas.push(`${debugTipoFator} (fator: ${fatorProximidade.toFixed(2)}, exp: ${(1/fatorProximidade).toFixed(2)} -> propLin: ${proporcaoLinear.toFixed(3)} result: ${proporcaoFinal.toFixed(3)})`);
  
  // ESTRATÉGIA 2: Aceleração em gaps temporais longos (mantida da lógica anterior)
  if (tempoTotalAnos > INTERPOLACAO_CONFIG.GAP_LONGO_ANOS) {
    const intensidadeGap = Math.min(INTERPOLACAO_CONFIG.FATOR_ACELERACAO_MAX, 
      1 + (tempoTotalAnos - INTERPOLACAO_CONFIG.GAP_LONGO_ANOS) * 0.3);
    const proporcaoGapLongo = 1 - Math.pow(1 - proporcaoLinear, intensidadeGap);
    
    // Usar o maior entre a estratégia de proximidade e gap longo
    if (proporcaoGapLongo > proporcaoFinal) {
      proporcaoFinal = proporcaoGapLongo;
      debugInfo.estrategiasAplicadas.push(`Aceleração Gap Longo (${intensidadeGap.toFixed(2)}x)`);
    }
    
    // ESTRATÉGIA 3: Progresso mínimo garantido baseado no tempo decorrido
    const progressoMinimoTemporal = Math.min(
      INTERPOLACAO_CONFIG.PROGRESSO_MINIMO_TEMPORAL, 
      tempoDecorridoAnos / tempoTotalAnos * 1.2
    );
    // Aplicar apenas se não estivermos no caso de "muito distante" onde uma forte desaceleração é intencional
    if (progressoMinimoTemporal > proporcaoFinal && tempoRestanteAnos < PROXIMIDADE_TEMPORAL.ANOS_CONSIDERADOS_MUITO_DISTANTES) {
      proporcaoFinal = progressoMinimoTemporal;
      debugInfo.estrategiasAplicadas.push(`Progresso Mínimo Temporal (${(progressoMinimoTemporal * 100).toFixed(1)}%)`);
    }
  }
  
  // ESTRATÉGIA 4: Bonus por tempo já decorrido (para gaps muito longos)
  // Aplicar apenas se não estivermos no caso de "muito distante"
  if (tempoDecorridoAnos > 1 && tempoTotalAnos > 3 && tempoRestanteAnos < PROXIMIDADE_TEMPORAL.ANOS_CONSIDERADOS_MUITO_DISTANTES) {
    const bonusTempoDecorrido = Math.min(0.3, 
      (tempoDecorridoAnos - 1) * INTERPOLACAO_CONFIG.BONUS_TEMPO_DECORRIDO);
    // Considerar adicionar ao valor já possivelmente ajustado pelo mínimo temporal ou o original da interpolação
    const proporcaoComBonus = Math.min(0.85, proporcaoFinal + bonusTempoDecorrido); // Adiciona ao proporcaoFinal corrente
    if (proporcaoComBonus > proporcaoFinal) { // Apenas se realmente aumentar
        proporcaoFinal = proporcaoComBonus;
        debugInfo.estrategiasAplicadas.push(`Bonus Tempo Decorrido (+${(bonusTempoDecorrido * 100).toFixed(1)}%)`);
    }
  }
  
  // ESTRATÉGIA 5: Aceleração inicial para gaps específicos (publicação -> análise)
  const tipoAtual = getTipoEtapa(atual);
  const tipoProximo = getTipoEtapa(proximo);
  
  if (tipoAtual === 'publicacao' && tipoProximo === 'analise' && 
      tempoDecorridoAnos < INTERPOLACAO_CONFIG.TEMPO_ACELERACAO_INICIAL) {
    const progressoInicialRapido = tempoDecorridoAnos * INTERPOLACAO_CONFIG.ACELERACAO_INICIAL;
    if (progressoInicialRapido > proporcaoFinal) {
      proporcaoFinal = progressoInicialRapido;
      debugInfo.estrategiasAplicadas.push(`Aceleração Inicial Pub->Análise (${(progressoInicialRapido * 100).toFixed(1)}%)`);
    }
  }
  
  const resultado = Math.max(0, Math.min(INTERPOLACAO_CONFIG.LIMITE_INTERPOLACAO, proporcaoFinal));
  
  // DEBUG: Log para monitorar o comportamento (apenas em desenvolvimento)
  if (process.env.NODE_ENV === 'development' && debugInfo.estrategiasAplicadas.length > 0) {
    console.log('🔍 Interpolação Inteligente:', {
      gap: `${tipoAtual} → ${tipoProximo}`,
      ...debugInfo,
      resultado: `${(resultado * 100).toFixed(1)}%`,
      melhoria: `${((resultado - proporcaoLinear) * 100) >= 0 ? '+' : ''}${((resultado - proporcaoLinear) * 100).toFixed(1)}%`
    });
  }
  
  return resultado;
};

/**
 * Calcula progresso quando há apenas um marcador com data
 */
export const calcularProgressoUnicoMarcador = (
  marcador: MarcadorTimeline & { dataReferencia: Date },
  hoje: Date
): number => {
  // CORREÇÃO: Para um único marcador, também aplicar progresso pós-alcance
  if (hoje >= marcador.dataReferencia) {
    console.log('📍 Único marcador - aplicando progresso pós-alcance');
    return calcularProgressoPosAlcance(marcador, null, hoje, 0, 1);
  } else {
    return 0;
  }
};

/**
 * Calcula progresso quando há múltiplos marcadores
 * Agora com interpolação inteligente para melhorar UX em gaps temporais longos
 */
export const calcularProgressoMultiplosMarcadores = (
  marcadoresComDatas: Array<MarcadorTimeline & { dataReferencia: Date }>,
  hoje: Date
): number => {
  const { LIMITE_PROGRESSO_ADICIONAL } = TIMELINE_CONSTANTS;
  
  // Verificar se estamos antes da primeira data
  if (hoje < marcadoresComDatas[0].dataReferencia) {
    return 0;
  }
  
  // Verificar se estamos após a última data
  if (hoje >= marcadoresComDatas[marcadoresComDatas.length - 1].dataReferencia) {
    const ultimoMarcador = marcadoresComDatas[marcadoresComDatas.length - 1];
    const penultimoMarcador = marcadoresComDatas[marcadoresComDatas.length - 2];
    
    const deltaPixels = (ultimoMarcador.posicaoPixels || 0) - (penultimoMarcador.posicaoPixels || 0);
    const deltaTempo = ultimoMarcador.dataReferencia.getTime() - penultimoMarcador.dataReferencia.getTime();
    
    if (deltaTempo > 0) {
      const tempoAposUltimo = hoje.getTime() - ultimoMarcador.dataReferencia.getTime();
      const velocidadePixelsPorMs = deltaPixels / deltaTempo;
      const pixelsAdicionais = velocidadePixelsPorMs * tempoAposUltimo;
      const limiteDeltaPixels = deltaPixels * LIMITE_PROGRESSO_ADICIONAL;
      const pixelsLimitados = Math.min(pixelsAdicionais, limiteDeltaPixels);
      
      return (ultimoMarcador.posicaoPixels || 0) + pixelsLimitados;
    } else {
      return ultimoMarcador.posicaoPixels || 0;
    }
  }
  
  // Estamos entre dois marcadores: usar interpolação inteligente
  for (let i = 0; i < marcadoresComDatas.length - 1; i++) {
    const atual = marcadoresComDatas[i];
    const proximo = marcadoresComDatas[i + 1];
    
    if (hoje >= atual.dataReferencia && hoje < proximo.dataReferencia) {
      const tempoTotal = proximo.dataReferencia.getTime() - atual.dataReferencia.getTime();
      const tempoDecorrido = hoje.getTime() - atual.dataReferencia.getTime();
      
      if (tempoTotal > 0) {
        // NOVA LÓGICA: Usar interpolação inteligente
        const proporcaoInteligente = calcularInterpolacaoInteligente(atual, proximo, hoje);
        const deltaPixels = (proximo.posicaoPixels || 0) - (atual.posicaoPixels || 0);
        return (atual.posicaoPixels || 0) + (deltaPixels * proporcaoInteligente);
      } else {
        return atual.posicaoPixels || 0;
      }
    }
  }
  
  return 0;
};

/**
 * Verifica se deve parar progresso no sobrestamento
 */
export const verificarParadaSobrestamento = (
  marcadores: MarcadorTimeline[]
): { deveParar: boolean; progressWidth: number } => {
  // Encontrar marcador de sobrestamento
  const marcadorSobrestamento = marcadores.find(m => 
    m.idOriginal === 'SOBRESTAMENTO_01' || 
    m.etapaDefinicao.nomeOriginal.toLowerCase().includes('sobrestamento')
  );
  
  if (!marcadorSobrestamento) {
    return { deveParar: false, progressWidth: 0 };
  }
  
  if (marcadorSobrestamento.status !== 'CONCLUIDA' && marcadorSobrestamento.status !== 'ATUAL') {
    return { deveParar: false, progressWidth: 0 };
  }

  // Verificar se há etapas relevantes após o sobrestamento (excluindo análise de mérito)
  const indiceSobrestamento = marcadores.findIndex(m => 
    m.idOriginal === marcadorSobrestamento.idOriginal || 
    (m.etapaDefinicao.nomeOriginal === marcadorSobrestamento.etapaDefinicao.nomeOriginal)
  );
  
  const etapasAposSobrestamento = marcadores.slice(indiceSobrestamento + 1).filter(m => {
    const isAnaliseMetito = m.etapaDefinicao.nomeOriginal.toLowerCase().includes('análise de mérito');
    if (isAnaliseMetito) return false;
    
    return m.status === 'CONCLUIDA' || m.status === 'ATUAL' || 
           m.status === 'PROXIMA_ESTIMADA' || m.status === 'PROXIMA_EXATA';
  });
  
  if (etapasAposSobrestamento.length === 0) {
    // Usar a posição em porcentagem em vez de pixels para a barra de progresso
    return { 
      deveParar: true, 
      progressWidth: marcadorSobrestamento.posicao // Usar posição em % diretamente
    };
  }
  
  return { deveParar: false, progressWidth: 0 };
};

/**
 * Calcula o progresso da barra azul
 * Agora com progresso pós-alcance para evitar barra "grudada" no marcador
 */
export const calcularProgressoBarra = (
  marcadores: MarcadorTimeline[],
  larguraTotal: number
): number => {
  const hoje = new Date();
  hoje.setHours(0, 0, 0, 0);
  
  console.log('🎯 calcularProgressoBarra INICIADO:', {
    totalMarcadores: marcadores.length,
    larguraTotal: Math.round(larguraTotal),
    hoje: hoje.toISOString().split('T')[0]
  });
  
  // Verificar caso especial de sobrestamento
  const { deveParar, progressWidth: progressSobrestamento } = verificarParadaSobrestamento(marcadores);
  
  if (deveParar) {
    console.log('⏸️ Parou no sobrestamento:', progressSobrestamento + '%');
    // progressSobrestamento já está em porcentagem, retornar diretamente
    return Math.max(0, Math.min(100, progressSobrestamento));
  }
  
  // Calcular progresso baseado em datas
  const marcadoresComDatas = prepararMarcadoresComDatas(marcadores);
  console.log('📊 Marcadores com datas:', marcadoresComDatas.length);
  
  let progressoAte = 0;
  
  if (marcadoresComDatas.length === 0) {
    console.log('❌ Nenhum marcador com data');
    progressoAte = 0;
  } else if (marcadoresComDatas.length === 1) {
    console.log('1️⃣ Um único marcador');
    // Um marcador: verificar progresso pós-alcance
    const marcador = marcadoresComDatas[0];
    if (hoje >= marcador.dataReferencia) {
      progressoAte = calcularProgressoPosAlcance(marcador, null, hoje, 0, 1);
      console.log('✅ Progresso único marcador:', Math.round(progressoAte), 'pixels');
  } else {
      progressoAte = 0;
      console.log('⏳ Data futura - sem progresso');
    }
  } else {
    console.log('🔢 Múltiplos marcadores');
    // Múltiplos marcadores: usar lógica inteligente + pós-alcance
    progressoAte = calcularProgressoMultiplosMarcadoresComPosAlcance(marcadoresComDatas, hoje);
    console.log('✅ Progresso múltiplos marcadores:', Math.round(progressoAte), 'pixels');
  }
  
  const progressWidth = larguraTotal > 0 ? (progressoAte / larguraTotal) * 100 : 0;
  console.log('🎯 RESULTADO FINAL:', {
    progressoAte: Math.round(progressoAte),
    larguraTotal: Math.round(larguraTotal),
    progressWidth: progressWidth.toFixed(2) + '%'
  });
  
  return Math.max(0, Math.min(100, progressWidth));
};

// ==================== FUNÇÕES DE ESTILOS ====================

/**
 * Determina os estilos de posicionamento para um marcador
 */
export const obterEstilosPosicionamento = (
  marcador: MarcadorTimeline,
  isAnaliseRecurso: boolean,
  isAnaliseMetito: boolean
): React.CSSProperties => {
  // Agora todos os marcadores usam posicionamento por porcentagem
  // O alinhamento à esquerda é controlado pelo CSS
  return { left: `${marcador.posicao}%` };
};

// ==================== FUNÇÃO PRINCIPAL ====================

/**
 * Função principal que calcula timeline completa
 */
export const calcularTimelineCompleta = (
  etapas: EtapaTimeline[],
  configuracao: ConfiguracaoTimeline & { estimativasDb?: EstimativasDb }
): ResultadoCalculoTimeline => {
  // Verificar se é um processo não publicado
  const temPublicacaoEstimada = etapas.some(etapa => etapa.idOriginal === 'PUBLICACAO_ESTIMADA_01');
  
  if (temPublicacaoEstimada && configuracao.estimativasDb) {
    return calcularTimelineNaoPublicado(etapas, configuracao.estimativasDb);
  }

  // Usar as funções refatoradas para melhor representação temporal
  const { etapasComDatas, etapasSemDatas } = separarEtapasPorDatas(etapas);

  // Se não há etapas com datas, usar posicionamento fixo
  if (etapasComDatas.length === 0) {
    return {
      marcadores: etapas.map((etapa, index) => ({
        ...etapa,
        posicao: index * 20, // Espaçamento fixo simples
        posicaoPixels: index * TIMELINE_CONSTANTS.DISTANCIA_MINIMA,
        ...calcularStatusMarcador(etapa),
        isSobrestamento: false,
        isUltimaAnalise: false,
        isProtocolo: getTipoEtapa(etapa) === 'protocolo',
        isArquivamento: getTipoEtapa(etapa) === 'arquivamento',
        isIndeferimento: configuracao.tipo === 'indeferimento' && index === 0,
        isDeferimento: configuracao.tipo === 'deferimento' && index === 0,
        isConcessao: configuracao.tipo === 'concessao' && index === 0,
        isRecursoExpirado: isRecursoExpiradoSemApresentacao(etapa, etapas)
      })),
      progressWidth: 0
    };
  }

  // Detectar automaticamente se deve usar escala logarítmica
  const usarEscalaAutoDetectada = configuracao.usarEscalaLogaritmica || 
    detectarNecessidadeEscalaLogaritmica(etapasComDatas);

  // Calcular posições usando as funções refatoradas
  const configuracaoCalculos: ConfiguracaoTimeline = {
    ...configuracao,
    usarEscalaLogaritmica: usarEscalaAutoDetectada
  };
  
  const posicoes = calcularPosicoesTimeline(etapas, configuracaoCalculos);
  const larguraTotal = calcularLarguraTotal(posicoes);
  const posicoesAjustadas = converterPosicoesNegativas(posicoes, larguraTotal);

  // Criar marcadores finais
  const marcadores = converterParaMarcadores(etapas, posicoesAjustadas, larguraTotal, configuracao);

  // Calcular progresso da barra azul
  const progressWidth = calcularProgressoBarra(marcadores, larguraTotal);

  return { marcadores, progressWidth };
};

/**
 * Calcula timeline específica para processo não publicado
 */
export const calcularTimelineNaoPublicado = (
  etapas: EtapaTimeline[],
  estimativasDb: EstimativasDb
): ResultadoCalculoTimeline => {
  // Identificar etapas relevantes
  const etapaProtocolo = etapas.find(e => getTipoEtapa(e) === 'protocolo');
  const etapaPublicacao = etapas.find(e => e.idOriginal === 'PUBLICACAO_ESTIMADA_01');
  const etapaFimOposicao = etapas.find(e => e.idOriginal === 'FIM_OPOSICAO_ESTIMADO_01');
  const etapaAnaliseMerito = etapas.find(e => e.idOriginal === 'ANALISE_MERITO_ESTIMADA_01');

  if (!etapaProtocolo || !etapaPublicacao || !etapaFimOposicao || !etapaAnaliseMerito) {
    return { marcadores: [], progressWidth: 0 };
  }

  // Datas base
  const dataProtocolo = etapaProtocolo.dataInicio ? new Date(etapaProtocolo.dataInicio) : null;
  const dataPublicacao = estimativasDb.dataPublicacaoEstimada ? new Date(estimativasDb.dataPublicacaoEstimada) : null;
  const dataFimOposicao = estimativasDb.dataFimOposicaoEstimado ? new Date(estimativasDb.dataFimOposicaoEstimado) : null;
  const dataAnaliseMerito = estimativasDb.dataAnaliseMeritoEstimada ? new Date(estimativasDb.dataAnaliseMeritoEstimada) : null;

  if (!dataProtocolo || !dataPublicacao || !dataFimOposicao || !dataAnaliseMerito) {
    return { marcadores: [], progressWidth: 0 };
  }

  // Cálculo dos ms totais
  const msTotal = dataAnaliseMerito.getTime() - dataProtocolo.getTime();
  const msPublicacao = dataPublicacao.getTime() - dataProtocolo.getTime();
  const msFimOposicao = dataFimOposicao.getTime() - dataProtocolo.getTime();
  const msAnaliseMerito = dataAnaliseMerito.getTime() - dataProtocolo.getTime();

  // Posições percentuais proporcionais
  let posProtocolo = 0;
  let posPublicacao = (msPublicacao / msTotal) * 100 ;
  let posFimOposicao = (msFimOposicao / msTotal ) * 100;
  posFimOposicao += 4; // Adiciona manualmente mais 4% ao marcador de fim da fase de oposição
  let posAnaliseMerito = 85; // Agora fixo em 85%

  // Garantir distância mínima percentual entre os três primeiros marcadores
  const DISTANCIA_MINIMA_PERCENTUAL = 15; // mínimo de 12% entre marcadores

  // 1. Protocolo -> Publicação: garantir distância mínima
  if (posPublicacao - posProtocolo < DISTANCIA_MINIMA_PERCENTUAL) {
    const delta = DISTANCIA_MINIMA_PERCENTUAL - (posPublicacao - posProtocolo);
    posPublicacao += delta;
    posFimOposicao += delta;
  }

  // 2. Publicação -> Fim da oposição: garantir apenas a distância mínima, mantendo proporcionalidade temporal
  if (posFimOposicao - posPublicacao < DISTANCIA_MINIMA_PERCENTUAL) {
    const delta = DISTANCIA_MINIMA_PERCENTUAL - (posFimOposicao - posPublicacao);
    posFimOposicao += delta;
  }
  // Garantir que fim de oposição nunca ultrapasse 85%
  if (posFimOposicao > 85) posFimOposicao = 85;

  // Montar marcadores
  const marcadores: MarcadorTimeline[] = [
    {
      ...etapaProtocolo,
      posicao: posProtocolo,
      posicaoPixels: 0,
      ...calcularStatusMarcador(etapaProtocolo),
      isSobrestamento: false,
      isUltimaAnalise: false,
      isProtocolo: true,
      isArquivamento: false,
      isIndeferimento: false,
      isDeferimento: false,
      isConcessao: false,
      isRecursoExpirado: false
    },
    {
      ...etapaPublicacao,
      posicao: posPublicacao,
      posicaoPixels: (posPublicacao / 100) * TIMELINE_CONSTANTS.LARGURA_BASE,
      ...calcularStatusMarcador(etapaPublicacao),
      isSobrestamento: false,
      isUltimaAnalise: false,
      isProtocolo: false,
      isArquivamento: false,
      isIndeferimento: false,
      isDeferimento: false,
      isConcessao: false,
      isRecursoExpirado: false
    },
    {
      ...etapaFimOposicao,
      posicao: posFimOposicao,
      posicaoPixels: (posFimOposicao / 100) * TIMELINE_CONSTANTS.LARGURA_BASE,
      ...calcularStatusMarcador(etapaFimOposicao),
      isSobrestamento: false,
      isUltimaAnalise: false,
      isProtocolo: false,
      isArquivamento: false,
      isIndeferimento: false,
      isDeferimento: false,
      isConcessao: false,
      isRecursoExpirado: false
    },
    {
      ...etapaAnaliseMerito,
      posicao: posAnaliseMerito,
      posicaoPixels: (posAnaliseMerito / 100) * TIMELINE_CONSTANTS.LARGURA_BASE,
      ...calcularStatusMarcador(etapaAnaliseMerito),
      isSobrestamento: false,
      isUltimaAnalise: true,
      isProtocolo: false,
      isArquivamento: false,
      isIndeferimento: false,
      isDeferimento: false,
      isConcessao: false,
      isRecursoExpirado: false
    }
  ];

  // Calcular progresso da barra azul
  const hoje = new Date();
  hoje.setHours(0, 0, 0, 0);
  let progressWidth = 0;
  if (hoje < dataPublicacao) {
    progressWidth = posProtocolo;
  } else if (hoje < dataFimOposicao) {
    progressWidth = posPublicacao;
  } else if (hoje < dataAnaliseMerito) {
    progressWidth = posFimOposicao;
  } else {
    progressWidth = posAnaliseMerito;
  }

  return { marcadores, progressWidth };
};

/**
 * Calcula timeline específica para casos de arquivamento
 */
export const calcularTimelineArquivamento = (etapas: EtapaTimeline[]): ResultadoCalculoTimeline => {
  const etapaProtocolo = etapas.find(etapa => getTipoEtapa(etapa) === 'protocolo');
  const etapaArquivamento = etapas.find(etapa => getTipoEtapa(etapa) === 'arquivamento');

  const etapasSimplificadas = [etapaProtocolo, etapaArquivamento].filter(Boolean) as EtapaTimeline[];
  
  const marcadores = etapasSimplificadas.map((etapa, index) => {
    const isProtocolo = getTipoEtapa(etapa) === 'protocolo';
    const isArquivamento = !isProtocolo;
    
    // Calcular status do marcador usando função utilitária
    const { isAlcancada, isPrazoPendente } = calcularStatusMarcador(etapa);
    
    return {
      ...etapa,
      posicao: isProtocolo ? 0 : 100, // Protocolo: 0%, Arquivamento: 100%
      posicaoPixels: isProtocolo ? 0 : 400,
      isAlcancada,
      isPrazoPendente,
      isSobrestamento: false,
      isUltimaAnalise: false,
      isProtocolo,
      isArquivamento,
      isIndeferimento: false,
      isDeferimento: false,
      isConcessao: false,
      isRecursoExpirado: false
    };
  });

  // Calcular progresso baseado na data atual
  let progressWidth = 0;
  
  if (etapaArquivamento?.status === 'CONCLUIDA') {
    progressWidth = 100;
  } else if (etapaProtocolo && etapaArquivamento?.dataInicio) {
    const hoje = new Date();
    hoje.setHours(0, 0, 0, 0);
    
    const dataProtocolo = etapaProtocolo.dataInicio ? new Date(etapaProtocolo.dataInicio) : null;
    const dataArquivamento = new Date(etapaArquivamento.dataInicio);
    
    if (dataProtocolo) {
      dataProtocolo.setHours(0, 0, 0, 0);
      dataArquivamento.setHours(0, 0, 0, 0);
      
      const tempoTotal = dataArquivamento.getTime() - dataProtocolo.getTime();
      const tempoDecorrido = hoje.getTime() - dataProtocolo.getTime();
      
      if (tempoTotal > 0 && tempoDecorrido >= 0) {
        progressWidth = Math.min(100, (tempoDecorrido / tempoTotal) * 100);
      }
    }
  }

  return { marcadores, progressWidth };
};

/**
 * Calcula a próxima terça-feira útil a partir de uma data
 */
export const calcularProximaTercaUtil = (data: Date): Date => {
  const novaData = new Date(data);
  
  // Encontrar a próxima terça-feira (dia da semana 2)
  const diasParaTerca = (2 - novaData.getDay() + 7) % 7;
  if (diasParaTerca === 0 && novaData.getDay() === 2) {
    // Se já é terça-feira, usar a próxima terça
    novaData.setDate(novaData.getDate() + 7);
  } else {
    novaData.setDate(novaData.getDate() + diasParaTerca);
  }
  
  return novaData;
};

/**
 * Calcula a data estimada de concessão baseada no prazo de pagamento da taxa
 */
export const calcularDataEstimadaConcessao = (etapas: EtapaTimeline[]): Date | null => {
  // Procurar pelas etapas de prazo de pagamento da taxa
  const prazoOrdinario = etapas.find(e => e.idOriginal === 'PRAZO_PAGAMENTO_TAXA_CONCESSAO_ORDINARIO_01');
  const prazoExtraordinario = etapas.find(e => e.idOriginal === 'PRAZO_PAGAMENTO_TAXA_CONCESSAO_EXTRAORDINARIO_01');
  
  let dataBasePrazo: Date | null = null;
  
  // Se há prazo extraordinário ativo, usar como base
  if (prazoExtraordinario && prazoExtraordinario.status === 'ATUAL' && prazoExtraordinario.dataFim) {
    dataBasePrazo = new Date(prazoExtraordinario.dataFim);
  }
  // Senão, se há prazo ordinário, usar como base
  else if (prazoOrdinario && prazoOrdinario.dataFim) {
    dataBasePrazo = new Date(prazoOrdinario.dataFim);
  }
  
  if (!dataBasePrazo) return null;
  
  // Adicionar 45 dias à data base
  const dataEstimada = new Date(dataBasePrazo);
  dataEstimada.setDate(dataEstimada.getDate() + 45);
  
  // Ajustar para a próxima terça-feira útil
  return calcularProximaTercaUtil(dataEstimada);
};

/**
 * Verifica se uma etapa é de taxa de concessão e retorna formatação especial
 */
export const formatarEtapaTaxaConcessao = (etapa: EtapaTimeline): { isTaxaConcessao: boolean; textoFormatado?: { principal: string; subtexto: string } } => {
  const id = etapa.idOriginal;
  const nome = etapa.etapaDefinicao.nomeOriginal.toLowerCase();
  
  // Verificar se é etapa de taxa de concessão
  const isTaxaOrdinaria = id === 'PRAZO_PAGAMENTO_TAXA_CONCESSAO_ORDINARIO_01';
  const isTaxaExtraordinaria = id === 'PRAZO_PAGAMENTO_TAXA_CONCESSAO_EXTRAORDINARIO_01';
  
  if (isTaxaOrdinaria || isTaxaExtraordinaria) {
    return {
      isTaxaConcessao: true,
      textoFormatado: {
        principal: 'Taxa de Concessão',
        subtexto: isTaxaOrdinaria ? 'Prazo ordinário' : 'Prazo extraordinário'
      }
    };
  }
  
  return { isTaxaConcessao: false };
};

/**
 * Calcula a data estimada do fim do prazo de nulidade (180 dias após a concessão)
 */
export const calcularDataEstimadaFimPrazoNulidade = (dataConcessao: Date): Date => {
  const dataFimPrazo = new Date(dataConcessao);
  dataFimPrazo.setDate(dataFimPrazo.getDate() + 180); // 180 dias após a concessão
  return dataFimPrazo;
};

/**
 * Verifica se é etapa de prazo de manifestação de nulidade
 */
export const isPrazoManifestacao = (etapa: EtapaTimeline): boolean => {
  return etapa.idOriginal === 'PRAZO_MANIFESTACAO_NULIDADE_01';
};

/**
 * Verifica se é etapa de exigência ativa
 */
export const isExigenciaAtiva = (etapa: EtapaTimeline): boolean => {
  return etapa.idOriginal === 'INDICADOR_EXIGENCIA_ATIVA_01';
};

/**
 * Verifica se é etapa de taxa de concessão
 */
export const isTaxaConcessao = (etapa: EtapaTimeline): boolean => {
  const id = etapa.idOriginal;
  return id === 'PRAZO_PAGAMENTO_TAXA_CONCESSAO_ORDINARIO_01' || 
         id === 'PRAZO_PAGAMENTO_TAXA_CONCESSAO_EXTRAORDINARIO_01';
};

/**
 * Conta quantas renovações já foram concluídas e determina o número da próxima
 */
export const calcularNumeroRenovacao = (etapas: EtapaTimeline[], etapaAtual: EtapaTimeline): string => {
  // Verificar se a etapa atual é especificamente RENOVACAO_01
  if (etapaAtual.idOriginal !== 'RENOVACAO_01') {
    return etapaAtual.etapaDefinicao.nomeOriginal; // Retorna o nome original para todas as outras etapas
  }
  
  // Contar renovações concluídas
  const renovacoesConcluidas = etapas.filter(etapa => 
    (etapa.idOriginal === 'RENOVACAO_CONCLUIDA_ORDINARIA_01' || 
     etapa.idOriginal === 'RENOVACAO_CONCLUIDA_EXTRAORDINARIA_01') &&
    (etapa.status === 'CONCLUIDA' || etapa.status === 'ATUAL')
  ).length;
  
  // Se não há renovações concluídas, manter o nome original
  if (renovacoesConcluidas === 0) {
    return etapaAtual.etapaDefinicao.nomeOriginal;
  }
  
  // Calcular o número da próxima renovação
  const proximoNumero = renovacoesConcluidas + 1;
  
  // Formatação ordinal em português
  const formatarOrdinal = (num: number): string => {
    switch (num) {
      case 1: return '1ª';
      case 2: return '2ª';
      case 3: return '3ª';
      case 4: return '4ª';
      case 5: return '5ª';
      case 6: return '6ª';
      case 7: return '7ª';
      case 8: return '8ª';
      case 9: return '9ª';
      case 10: return '10ª';
      default: return `${num}ª`;
    }
  };
  
  return `${formatarOrdinal(proximoNumero)} Renovação`;
};

/**
 * Calcula progresso adicional após uma etapa ser alcançada
 * Resolve o problema da barra "grudando" no marcador após alcançá-lo
 * CORRIGIDO: Só aplica progresso mínimo se a data JÁ PASSOU
 */
export const calcularProgressoPosAlcance = (
  marcadorAlcancado: MarcadorTimeline & { dataReferencia: Date },
  proximoMarcador: (MarcadorTimeline & { dataReferencia: Date }) | null,
  hoje: Date,
  indexMarcador: number,
  totalMarcadoresAlcancados: number
): number => {
  const { 
    DIAS_PROGRESSO_EXTRA, 
    PROGRESSO_MAXIMO_EXTRA, 
    CURVA_DESACELERACAO,
    APLICAR_TODAS_ALCANCADAS,
    PROGRESSO_MINIMO_GARANTIDO
  } = PROGRESSO_POS_ALCANCE_CONFIG;
  
  // Se não aplicar a todas, usar lógica anterior (só últimas 2)
  if (!APLICAR_TODAS_ALCANCADAS) {
    const distanciaDoFinal = totalMarcadoresAlcancados - indexMarcador;
    if (distanciaDoFinal > 2) {
      return marcadorAlcancado.posicaoPixels || 0;
    }
  }
  
  // CORREÇÃO: Garantir que as datas sejam normalizadas (sem horas)
  const hojeNormalizado = new Date(hoje);
  hojeNormalizado.setHours(0, 0, 0, 0);
  
  const dataReferenciaNormalizada = new Date(marcadorAlcancado.dataReferencia);
  dataReferenciaNormalizada.setHours(0, 0, 0, 0);
  
  // Calcular dias decorridos desde a data da etapa
  const diasDesdeAlcance = Math.floor(
    (hojeNormalizado.getTime() - dataReferenciaNormalizada.getTime()) / (1000 * 60 * 60 * 24)
  );
  
  const posicaoBase = marcadorAlcancado.posicaoPixels || 0;
  
  // DEBUG: Log sempre ativo para entender o que está acontecendo
  console.log('🔍 DEBUG Progresso Pós-Alcance:', {
    etapa: marcadorAlcancado.etapaDefinicao.nomeOriginal,
    dataReferencia: dataReferenciaNormalizada.toISOString().split('T')[0],
    hoje: hojeNormalizado.toISOString().split('T')[0],
    diasDesdeAlcance,
    posicaoBase: Math.round(posicaoBase),
    isAlcancada: marcadorAlcancado.isAlcancada,
    status: marcadorAlcancado.status
  });
  
  // CORREÇÃO PRINCIPAL: Diferentes comportamentos baseados no tempo decorrido
  if (diasDesdeAlcance < 0) {
    // Data ainda não chegou - retornar posição original (barra fica atrás)
    console.log('🔴 Data no futuro - barra fica atrás do marcador');
    return posicaoBase;
  }
  
  // CASO ESPECIAL: Data é exatamente hoje (0 dias) - progresso muito sutil
  if (diasDesdeAlcance === 0) {
    // Apenas um pequeno avanço para mostrar que "alcançou" a etapa
    let resultado = posicaoBase;
    if (proximoMarcador) {
      const distanciaProximo = (proximoMarcador.posicaoPixels || 0) - posicaoBase;
      resultado = posicaoBase + (distanciaProximo * 0.02); // Apenas 2% da distância
    } else {
      resultado = posicaoBase + (TIMELINE_CONSTANTS.DISTANCIA_MINIMA * 0.02);
    }
    console.log('🟡 Data é hoje - pequeno avanço:', Math.round(resultado - posicaoBase), 'pixels');
    return resultado;
  }
  
  // CASO ESPECIAL: Poucos dias após a data (1-3 dias) - progresso gradual
  if (diasDesdeAlcance <= 3) {
    const fatorGradual = diasDesdeAlcance / 3; // 0.33, 0.66, 1.0
    let resultado = posicaoBase;
    if (proximoMarcador) {
      const distanciaProximo = (proximoMarcador.posicaoPixels || 0) - posicaoBase;
      resultado = posicaoBase + (distanciaProximo * 0.05 * fatorGradual); // 0% a 5% gradual
    } else {
      resultado = posicaoBase + (TIMELINE_CONSTANTS.DISTANCIA_MINIMA * 0.05 * fatorGradual);
    }
    console.log('🟠 Poucos dias após:', diasDesdeAlcance, 'dias, avanço:', Math.round(resultado - posicaoBase), 'pixels');
    return resultado;
  }
  
  // Aqui a data JÁ PASSOU há mais de 3 dias - aplicar progresso mínimo garantido completo
  let progressoMinimo = posicaoBase;
  if (proximoMarcador) {
    const distanciaProximo = (proximoMarcador.posicaoPixels || 0) - posicaoBase;
    progressoMinimo = posicaoBase + (distanciaProximo * PROGRESSO_MINIMO_GARANTIDO);
  } else {
    progressoMinimo = posicaoBase + (TIMELINE_CONSTANTS.DISTANCIA_MINIMA * PROGRESSO_MINIMO_GARANTIDO);
  }
  
  // Se passou muito tempo (>30 dias), retornar apenas o mínimo garantido
  if (diasDesdeAlcance > DIAS_PROGRESSO_EXTRA) {
    console.log('🔵 Muito tempo após:', diasDesdeAlcance, 'dias, usando mínimo garantido:', Math.round(progressoMinimo - posicaoBase), 'pixels');
    return progressoMinimo;
  }
  
  // Progresso extra nos primeiros 30 dias após passar a data
  const proporcaoPeriodo = diasDesdeAlcance / DIAS_PROGRESSO_EXTRA;
  const proporcaoSuavizada = 1 - Math.pow(1 - proporcaoPeriodo, CURVA_DESACELERACAO);
  
  // Calcular o máximo de pixels que pode avançar
  let maxPixelsExtra = 0;
  
  if (proximoMarcador) {
    const distanciaProximo = (proximoMarcador.posicaoPixels || 0) - posicaoBase;
    maxPixelsExtra = distanciaProximo * PROGRESSO_MAXIMO_EXTRA;
  } else {
    maxPixelsExtra = TIMELINE_CONSTANTS.DISTANCIA_MINIMA * PROGRESSO_MAXIMO_EXTRA;
  }
  
  const pixelsExtras = maxPixelsExtra * proporcaoSuavizada;
  const resultadoCompleto = posicaoBase + pixelsExtras;
  
  // Retornar o maior entre o resultado completo e o mínimo garantido
  const resultado = Math.max(resultadoCompleto, progressoMinimo);
  
  console.log('🟢 Progresso normal:', {
    diasDesdeAlcance,
    proporcaoSuavizada: (proporcaoSuavizada * 100).toFixed(1) + '%',
    pixelsExtras: Math.round(pixelsExtras),
    progressoMinimo: Math.round(progressoMinimo - posicaoBase),
    resultadoFinal: Math.round(resultado - posicaoBase),
    progresso: `+${((resultado - posicaoBase) / TIMELINE_CONSTANTS.LARGURA_BASE * 100).toFixed(2)}%`
  });
  
  return resultado;
};

/**
 * Calcula progresso para múltiplos marcadores com lógica pós-alcance integrada
 * Combina interpolação inteligente + progresso pós-alcance
 */
export const calcularProgressoMultiplosMarcadoresComPosAlcance = (
  marcadoresComDatas: Array<MarcadorTimeline & { dataReferencia: Date }>,
  hoje: Date
): number => {
  const { LIMITE_PROGRESSO_ADICIONAL } = TIMELINE_CONSTANTS;
  
  // Verificar se estamos antes da primeira data
  if (hoje < marcadoresComDatas[0].dataReferencia) {
    return 0;
  }
  
  // Verificar se estamos após a última data
  if (hoje >= marcadoresComDatas[marcadoresComDatas.length - 1].dataReferencia) {
    const ultimoMarcador = marcadoresComDatas[marcadoresComDatas.length - 1];
    const penultimoMarcador = marcadoresComDatas[marcadoresComDatas.length - 2];
    
    // NOVA LÓGICA: Aplicar progresso pós-alcance ao último marcador
    const progressoComPosAlcance = calcularProgressoPosAlcance(
      ultimoMarcador, 
      null, 
      hoje, 
      marcadoresComDatas.length - 1, 
      marcadoresComDatas.length
    );
    
    // Se houve progresso pós-alcance, usar esse valor
    if (progressoComPosAlcance > (ultimoMarcador.posicaoPixels || 0)) {
      return progressoComPosAlcance;
    }
    
    // Senão, usar lógica original de progresso adicional
    const deltaPixels = (ultimoMarcador.posicaoPixels || 0) - (penultimoMarcador.posicaoPixels || 0);
    const deltaTempo = ultimoMarcador.dataReferencia.getTime() - penultimoMarcador.dataReferencia.getTime();
    
    if (deltaTempo > 0) {
      const tempoAposUltimo = hoje.getTime() - ultimoMarcador.dataReferencia.getTime();
      const velocidadePixelsPorMs = deltaPixels / deltaTempo;
      const pixelsAdicionais = velocidadePixelsPorMs * tempoAposUltimo;
      const limiteDeltaPixels = deltaPixels * LIMITE_PROGRESSO_ADICIONAL;
      const pixelsLimitados = Math.min(pixelsAdicionais, limiteDeltaPixels);
      
      return (ultimoMarcador.posicaoPixels || 0) + pixelsLimitados;
    } else {
      return ultimoMarcador.posicaoPixels || 0;
    }
  }
  
  // Estamos entre dois marcadores: usar interpolação inteligente
  for (let i = 0; i < marcadoresComDatas.length - 1; i++) {
    const atual = marcadoresComDatas[i];
    const proximo = marcadoresComDatas[i + 1];
    
    if (hoje >= atual.dataReferencia && hoje < proximo.dataReferencia) {
      const tempoTotal = proximo.dataReferencia.getTime() - atual.dataReferencia.getTime();
      
      if (tempoTotal > 0) {
        // Usar interpolação inteligente
        const proporcaoInteligente = calcularInterpolacaoInteligente(atual, proximo, hoje);
        const deltaPixels = (proximo.posicaoPixels || 0) - (atual.posicaoPixels || 0);
        let resultadoInterpolacao = (atual.posicaoPixels || 0) + (deltaPixels * proporcaoInteligente);
        
        // NOVA LÓGICA: Se o marcador atual foi alcançado, verificar progresso pós-alcance
        if (hoje >= atual.dataReferencia) {
          const totalMarcadoresAlcancados = marcadoresComDatas.filter(m => hoje >= m.dataReferencia).length;
          const progressoComPosAlcance = calcularProgressoPosAlcance(
            atual, 
            proximo, 
            hoje, 
            i, 
            totalMarcadoresAlcancados
          );
          
          // Usar o maior entre interpolação e progresso pós-alcance
          resultadoInterpolacao = Math.max(resultadoInterpolacao, progressoComPosAlcance);
        }
        
        return resultadoInterpolacao;
      } else {
        return atual.posicaoPixels || 0;
      }
    }
  }
  
  return 0;
}; 