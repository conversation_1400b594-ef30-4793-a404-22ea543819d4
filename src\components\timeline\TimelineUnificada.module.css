.timelineContainer {
  position: relative;
  width: 100%;
  height: 120px;
  margin: 10px 0;
}
.timelineContainerOposicao {
  position: relative;
  width: 100%;
  height: 120px;
  margin: -18px 0;
}

/* Linha base cinza */
.linhaBase {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 2px;
  background-color: #C3C3C3;
  transform: translateY(-50%);
}

/* Linha de progresso azul */
.linhaProgresso {
  position: absolute;
  top: 50%;
  left: 0;
  height: 7px;
  background-color: #4597B5;
  transform: translateY(-50%);
  transition: width 0.3s ease;
  z-index: 1;
}

/* Container do marcador */
.marcadorContainer {
  position: absolute;
  top: 50%;
  transform: translate(0, -50%);
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  z-index: 2;
}

/* Marcador circular */
.marcador {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: white;
  border: 2px solid #C3C3C3;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 3;
  cursor: pointer;
  transition: all 0.2s ease;
}

/* Marcador alcançado */
.marcadorAlcancado {
  background-color: #4597B5;
  border-color: #4597B5;
}

/* Marcador de sobrestamento */
.marcadorSobrestamento {
  background-color: #848484;
  border-color: #848484;
  position: relative;
}

.marcadorSobrestamento::before {
  content: '';
  position: absolute;
  width: 8px;
  height: 8px;
  background-color: white;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border-radius: 0;
}

/* Marcador de prazo pendente (data não alcançada) */
.marcadorPrazoPendente {
  background-color: white;
  border-color: #C3C3C3;
}

/* Marcador de prazo de manifestação de nulidade */
.marcadorPrazoManifestacao {
  background-color: white;
  border-color: #e6d72c;
}

/* Marcador de exigência ativa */
.marcadorExigenciaAtiva {
  background-color: white;
  border-color: #e6d72c;
}

/* Marcador de taxa de concessão */
.marcadorTaxaConcessao {
  background-color: white;
  border-color: #e6d72c;
}

/* Marcador de recurso expirado sem apresentação (apenas para o círculo) */
.marcadorRecursoExpiradoCirculo {
  background-color: white;
  border-color: #FF0000;
  border-width: 2px;
}

/* Marcador opaco (última análise com sobrestamento) */
.marcadorOpaco {
  opacity: 0.3;
}

/* Ícone de check */
.checkIcon {
  color: white;
  font-size: 12px;
  font-weight: bold;
}

/* Container da data */
.dataContainer {
  position: absolute;
  bottom: 100%;
  margin-bottom: 8px;
  white-space: nowrap;
  font-size: 12px;
  text-align: left;
  left: 0;
  transform: translateX(0);
}

/* Data confirmada */
.dataConfirmada {
    color: #A3A3A3;
      text-align: left;
      font-size: 12px;
      font-weight: 500;
}

/* Data prevista */
.dataPrevisao {
  color: #45B063;
  text-align: left;
}

.dataPrevisaoLabel {
  font-weight: bold;
}

.dataPrevisaoValor {
  font-style: normal;
}

/* Estimativas (etapas criadas pelo sistema) */
.dataEstimativa {
  color: #A3A3A3;
  text-align: left;
  font-size: 12px;
  font-weight: 500;
}

.dataEstimativaLabel {
  font-weight: 500;
  line-height: 1.5;
}

.dataEstimativaValor {
  font-style: normal;
  font-weight: 500;
  line-height: 1.2;
}

/* Nome da etapa */
.nomeEtapa {
  position: absolute;
  top: 100%;
  margin-top: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #000000;
  text-align: left;
  min-width: 145px;
  word-wrap: break-word;
  line-height: 1.2;
  left: 0;
  transform: translateX(0);
}

/* Nome da etapa opaco (análise de mérito quando há sobrestamento) */
.nomeEtapaOpaco {
  opacity: 0.3;
  font-size: 13px;
}

/* Estilos especiais para Taxa de Concessão */
.nomeEtapaTaxaConcessao {
  position: absolute;
  top: 100%;
  margin-top: 8px;
  color: #FF0000 !important;
  font-size: 14px;
  font-weight: 600;
  text-align: left;
  min-width: 145px;
  word-wrap: break-word;
  line-height: 1.2;
}

.subtextoTaxaConcessao {
  color: #969696;
  font-size: 14px;
  font-style: italic;
  font-weight: normal;
  margin-top: 2px;
  text-align: left;
}

/* Prazo expirado */
.prazoExpirado {
  color: #FF0000;
}

/* Responsividade */
@media (max-width: 1279px) {
  .dataContainer {
    left: 0;
    transform: translateX(0);
    text-align: left;
    font-size: 11px;
    white-space: normal;
    max-width: 90px;
  }

  .nomeEtapa {
    left: 0;
    transform: translateX(0);
    text-align: left;
    font-size: 12px;
    min-width: 80px;
    max-width: 90px;
  }

  .nomeEtapaTaxaConcessao {
    left: 0;
    transform: translateX(0);
    text-align: left;
    font-size: 12px;
    min-width: 80px;
    max-width: 90px;
  }

  .subtextoTaxaConcessao {
    text-align: left;
  }
}

@media (max-width: 768px) {
  .timelineContainer {
    height: 140px;
  }
  
  .nomeEtapa {
    font-size: 12px;
    max-width: 100px;
  }
  
  .dataContainer {
    font-size: 11px;
  }
}

/* Alinhamentos especiais para protocolo */
.marcadorProtocolo {
  left: 0 !important; /* Força posição no início da linha */
  transform: translate(0, -50%); /* Remove centralização horizontal, mantém vertical */
}

/* Alinhamentos especiais para indeferimento */
.marcadorIndeferimento {
  left: 0 !important; /* Força posição no início da linha */
  transform: translate(0, -50%); /* Remove centralização horizontal, mantém vertical */
}

/* Alinhamentos especiais para deferimento */
.marcadorDeferimento {
  left: 0 !important; /* Força posição no início da linha */
  transform: translate(0, -50%); /* Remove centralização horizontal, mantém vertical */
}

/* Alinhamentos especiais para concessão */
.marcadorConcessao {
  left: 0 !important; /* Força posição no início da linha */
  transform: translate(0, -50%); /* Remove centralização horizontal, mantém vertical */
}

/* Marcadores que ficam no final da linha mantêm o comportamento padrão */
.marcadorArquivamento {
  transform: translate(-100%, -50%); /* Move totalmente para a esquerda do ponto final */
}

.marcadorArquivamento .dataContainer {
  text-align: right; /* Alinha à direita, terminando no marcador */
  right: 0; /* Posiciona à direita do marcador */
  left: auto; /* Remove posicionamento à esquerda */
  transform: translateX(0);
}

.marcadorArquivamento .nomeEtapa {
  text-align: right; /* Alinha à direita, terminando no marcador */
  right: 0; /* Posiciona à direita do marcador */
  left: auto; /* Remove posicionamento à esquerda */
  transform: translateX(0);
}

/* Container e alinhamento para recurso expirado */
.marcadorRecursoExpirado {
  transform: translate(-100%, -50%); /* Move totalmente para a esquerda do ponto final */
}

.marcadorRecursoExpirado .dataContainer {
  text-align: left; /* Alinha à direita, terminando no marcador */
  left  : 0; /* Posiciona à direita do marcador */
  right: auto; /* Remove posicionamento à esquerda */
  transform: translateX(0);
}

.marcadorRecursoExpirado .nomeEtapa {
  text-align: left; /* Alinha à direita, terminando no marcador */
  left  : 0; /* Posiciona à direita do marcador */
  right: auto; /* Remove posicionamento à esquerda */
  transform: translateX(0);
}

/* Marcadores de processo não publicado */
.marcadorPublicacaoEstimada {
  transform: translate(0, -50%); /* Alinhamento à esquerda como protocolo */
}

.marcadorPublicacaoEstimada .dataContainer {
  text-align: left;
  transform: translateX(0);
}

.marcadorPublicacaoEstimada .nomeEtapa {
  text-align: left;
  transform: translateX(0);
  color: #666666; /* Cor mais sutil para estimativa */
}

.marcadorFimOposicaoEstimado {
  transform: translate(0, -50%); /* Alinhamento à esquerda */
}

.marcadorFimOposicaoEstimado .dataContainer {
  text-align: left;
  transform: translateX(0);
}

.marcadorFimOposicaoEstimado .nomeEtapa {
  text-align: left;
  transform: translateX(0);
  color: #666666; /* Cor mais sutil para estimativa */
}

.marcadorAnaliseEstimada {
  transform: translate(0, -50%); /* Alinhamento à esquerda */
}

.marcadorAnaliseEstimada .dataContainer {
  text-align: left;
  transform: translateX(0);
}

.marcadorAnaliseEstimada .nomeEtapa {
  text-align: left;
  transform: translateX(0);
  color: #666666; /* Cor mais sutil para estimativa */
}

/* Estilos específicos para textos de previsão de processo não publicado */
.marcadorPublicacaoEstimada .dataContainer,
.marcadorFimOposicaoEstimado .dataContainer {
    font-size: 12px !important;
  font-weight: 500 !important;
  color: #45B063 !important;
}

.marcadorPublicacaoEstimada .dataEstimativa,
.marcadorFimOposicaoEstimado .dataEstimativa {
  color: #45B063 !important;
  font-size: 12px !important;
  font-weight: 500 !important;
}

.marcadorPublicacaoEstimada .dataEstimativaLabel,
.marcadorFimOposicaoEstimado .dataEstimativaLabel,
.marcadorPublicacaoEstimada .dataEstimativaValor,
.marcadorFimOposicaoEstimado .dataEstimativaValor {
  color: #45B063 !important;
  font-size: 12px !important;
  font-weight: 500 !important;
}

/* Ajuste para o último marcador de análise de mérito */
.marcadorAnaliseEstimada {
  left: 90% !important; /* Posiciona 10% antes do fim da linha */
  transform: translate(-50%, -50%); /* Centraliza horizontalmente */
}

/* Ajuste da barra de progresso para processo não publicado */
.timelineContainer:has(.marcadorPublicacaoEstimada) .linhaProgresso {
  transition: width 0.5s ease-in-out;
  background: linear-gradient(90deg, #4597B5 0%, #4597B5 100%);
  background-size: 200% 100%;
  animation: progressAnimation 2s linear infinite;
}

@keyframes progressAnimation {
  0% {
    background-position: 100% 0;
  }
  100% {
    background-position: -100% 0;
  }
} 