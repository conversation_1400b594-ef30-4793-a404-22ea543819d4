import React, { useMemo, useState, useEffect } from 'react';
import styles from './TimelineUnificada.module.css';

// Mapeamento das descrições das etapas para tooltips
const DESCRICOES_ETAPAS: Record<string, string> = {
  'Análise de mérito': 'Momento em que o examinador analisa o pedido de registro e decide se ele cumpre os requisitos para a aprovação.',
  'Análise do recurso': 'Aguardando análise do recurso contra indeferimento',
  'Anulado': 'Pedido de anulação acatado pelo INPI.',
  'Arquivamento': 'Pedido arquivado pelo INPI',
  'Caducidade': '',
  'Concessão': 'Confirmação da concessão do registro pelo INPI.',
  'Cumprir Exigê<PERSON>': '',
  'Deferimento': 'Pedido aprovado pelo INPI.',
  'Extinção': '',
  'Indeferimento': 'Pedido recusado pelo INPI',
  'Manifestação': 'Prazo para se manifestar contra a oposição apresentada.',
  'Mantidada concessão': 'Pedido de anulação rejeitado pelo INPI.',
  'Prazo para nulidade': 'Fase em que terceiros podem solicitar anulação da concessão',
  'Prazo para oposição': 'Prazo final para que terceiros possam apresentar manifestação formal contra o pedido de registro.',
  'Protocolo': 'Pedido de registro foi depositado no sistema do INPI e está aguardando pela publicação oficial.',
  'Publicação': 'O pedido de registro se torna público. Terceiros tem prazo de 60 dias para se opor.',
  'Recurso': 'Prazo para apresentação de recurso contra o indeferimento',
  'Recurso Aceito': 'Recurso contra indeferimento foi aceito pelo INPI',
  'Recurso Negado': 'Recurso contra indeferimento não foi aceito pelo INPI',
  'Sobrestamento': '',
  'Taxa de Concessão (Prazo extraordinário)': 'Prazo extra para pagamento da taxa de concessão ao INPI.',
  'Taxa de Concessão (Prazo ordinário)': 'Primeiro prazo para pagamento da taxa de concessão ao INPI.'
};
import {
  EtapaTimeline,
  filtrarEtapasLimpas,
  formatarDataExibicao,
  isPrazoExpirado,
  temSobrestamento,
  getTipoEtapa,
  calcularTimelineCompleta,
  calcularTimelineArquivamento,
  obterEstilosPosicionamento,
  calcularDataEstimadaConcessao,
  formatarEtapaTaxaConcessao,
  calcularDataEstimadaFimPrazoNulidade,
  isPrazoManifestacao,
  isExigenciaAtiva,
  isTaxaConcessao,
  calcularNumeroRenovacao
} from './timelineUtils';

interface TimelineUnificadaProps {
  etapas: EtapaTimeline[];
  showOposicaoBadge: boolean;
  estimativasDb?: {
    mediaSemIntervencoes?: number | null;
    mediaComOposicao?: number | null;
    mediaComSobrestamento?: number | null;
    mediaComExigencia?: number | null;
  } | null;
  processo?: any;
}

// Função para verificar se é um processo não publicado (sem despachos ou sem publicação)
const isProcessoNaoPublicado = (etapas: EtapaTimeline[], processo?: any): boolean => {
  // Se o processo não tem despachos, é considerado não publicado
  return !processo?.Despacho || processo.Despacho.length === 0;
};

// Função para verificar e adicionar etapa de análise da nulidade
const adicionarAnaliseNulidade = (etapas: EtapaTimeline[]): EtapaTimeline[] => {
  // Verificar se existem as etapas necessárias
  const etapaProcessoNulidade = etapas.find(etapa => 
    etapa.idOriginal === 'PROCESSO_NULIDADE_INSTAURADO_01'
  );
  
  const etapaPrazoManifestacao = etapas.find(etapa => 
    etapa.idOriginal === 'PRAZO_MANIFESTACAO_NULIDADE_01'
  );
  
  // Se não existem ambas as etapas, retornar as etapas originais
  if (!etapaProcessoNulidade || !etapaPrazoManifestacao) {
    return etapas;
  }
  
  // Verificar se já existe a etapa de análise da nulidade
  const jaTemAnaliseNulidade = etapas.some(etapa => 
    etapa.idOriginal === 'ANALISE_NULIDADE_ESTIMADA_01'
  );
  
  if (jaTemAnaliseNulidade) {
    return etapas;
  }
  
  // Calcular data de análise da nulidade (30 meses após prazo de manifestação)
  const dataPrazoManifestacao = etapaPrazoManifestacao.dataInicio || etapaPrazoManifestacao.dataFim;
  
  if (!dataPrazoManifestacao) {
    return etapas;
  }
  
  const dataAnaliseNulidade = new Date(dataPrazoManifestacao);
  dataAnaliseNulidade.setMonth(dataAnaliseNulidade.getMonth() + 50);
  
  // Criar etapa de análise da nulidade
  const analiseNulidadeEstimada: EtapaTimeline = {
    idOriginal: 'ANALISE_NULIDADE_ESTIMADA_01',
    etapaDefinicao: {
      nomeOriginal: 'Análise da nulidade',
      statusSimples: 'Aguardando Análise da Nulidade',
      tipo: 'ESTIMATIVA'
    },
    status: 'PROXIMA_ESTIMADA',
    dataInicio: dataAnaliseNulidade,
    dataFim: null,
    observacaoGerada: 'Estimativa de análise da nulidade (30 meses após prazo de manifestação)'
  };
  
  // Encontrar posição para inserir (após o prazo de manifestação)
  const indicePrazoManifestacao = etapas.findIndex(etapa => 
    etapa.idOriginal === 'PRAZO_MANIFESTACAO_NULIDADE_01'
  );
  
  // Criar nova lista de etapas com a análise da nulidade inserida
  const novasEtapas = [...etapas];
  novasEtapas.splice(indicePrazoManifestacao + 1, 0, analiseNulidadeEstimada);
  
  return novasEtapas;
};

// Função para criar timeline de processo não publicado
const criarTimelineNaoPublicada = (etapas: EtapaTimeline[], estimativasDb?: any): { marcadores: any[], progressWidth: number } => {
  // Encontrar a etapa de protocolo
  const etapaProtocolo = etapas.find(etapa => getTipoEtapa(etapa) === 'protocolo');
  
  // Se não há protocolo, criar um baseado em dataDeposito (se disponível)
  const dataProtocolo = etapaProtocolo?.dataInicio || etapaProtocolo?.dataFim;
  
  if (!dataProtocolo) {
    // Se não há data de protocolo, não podemos criar a timeline estimada
    return { marcadores: [], progressWidth: 0 };
  }

  // Criar as etapas estimadas
  const etapasEstimadas: any[] = [];
  
  // 1. Protocolo (real)
  etapasEstimadas.push({
    ...etapaProtocolo,
    posicao: 0,
    posicaoPixels: 0,
    isAlcancada: true,
    isPrazoPendente: false,
    isSobrestamento: false,
    isUltimaAnalise: false,
    isProtocolo: true,
    isArquivamento: false,
    isIndeferimento: false,
    isDeferimento: false,
    isConcessao: false,
    isRecursoExpirado: false,
    isPublicacaoEstimada: false,
    isFimOposicaoEstimado: false,
    isAnaliseEstimada: false
  });

  // 2. Publicação (estimada)
  const publicacaoEstimada = {
    idOriginal: 'PUBLICACAO_ESTIMADA_01',
    etapaDefinicao: {
      nomeOriginal: 'Publicação',
      statusSimples: 'Aguardando Publicação',
      tipo: 'ESTIMATIVA'
    },
    status: 'PROXIMA_ESTIMADA' as const,
    dataInicio: dataProtocolo ? new Date(new Date(dataProtocolo).getTime() + 1000 * 60 * 60 * 24 * 17) : undefined,
    dataFim: null,
    posicao: 33.33,
    posicaoPixels: 200,
    isAlcancada: false,
    isPrazoPendente: false,
    isSobrestamento: false,
    isUltimaAnalise: false,
    isProtocolo: false,
    isArquivamento: false,
    isIndeferimento: false,
    isDeferimento: false,
    isConcessao: false,
    isRecursoExpirado: false,
    observacaoGerada: undefined
  };
  etapasEstimadas.push(publicacaoEstimada);

  // 3. Fim da fase de oposição (estimado)
  const fimOposicaoEstimado = {
    idOriginal: 'FIM_OPOSICAO_ESTIMADO_01',
    etapaDefinicao: {
      nomeOriginal: 'Fim da fase de oposição',
      statusSimples: 'Aguardando Fim da Oposição',
      tipo: 'ESTIMATIVA'
    },
    status: 'PROXIMA_ESTIMADA' as const,
    dataInicio: publicacaoEstimada.dataInicio ? new Date(new Date(publicacaoEstimada.dataInicio).getTime() + 1000 * 60 * 60 * 24 * 60) : undefined,
    dataFim: null,
    posicao: 66.66,
    posicaoPixels: 400,
    isAlcancada: false,
    isPrazoPendente: false,
    isSobrestamento: false,
    isUltimaAnalise: false,
    isProtocolo: false,
    isArquivamento: false,
    isIndeferimento: false,
    isDeferimento: false,
    isConcessao: false,
    isRecursoExpirado: false,
    observacaoGerada: undefined
  };
  etapasEstimadas.push(fimOposicaoEstimado);

  // 4. Análise de mérito (estimada)
  const hoje = new Date();
  let dataEstimadaAnalise = new Date(dataProtocolo);
  
  // Usar estimativas de mérito se disponíveis
  if (estimativasDb?.mediaSemIntervencoes) {
    dataEstimadaAnalise.setDate(dataEstimadaAnalise.getDate() + estimativasDb.mediaSemIntervencoes);
  } else {
    // Fallback: 450 dias (média aproximada)
    dataEstimadaAnalise.setDate(dataEstimadaAnalise.getDate() + 450);
  }

  const analiseEstimada = {
    idOriginal: 'ANALISE_MERITO_ESTIMADA_01',
    etapaDefinicao: {
      nomeOriginal: 'Análise de mérito',
      statusSimples: 'Aguardando Análise de Mérito',
      tipo: 'ESTIMATIVA'
    },
    status: 'PROXIMA_ESTIMADA' as const,
    dataInicio: dataEstimadaAnalise,
    dataFim: null,
    posicao: 100,
    posicaoPixels: 600,
    isAlcancada: false,
    isPrazoPendente: false,
    isSobrestamento: false,
    isUltimaAnalise: false,
    isProtocolo: false,
    isArquivamento: false,
    isIndeferimento: false,
    isDeferimento: false,
    isConcessao: false,
    isRecursoExpirado: false,
    isPublicacaoEstimada: false,
    isFimOposicaoEstimado: false,
    isAnaliseEstimada: true,
    observacaoGerada: undefined
  };
  etapasEstimadas.push(analiseEstimada);

  // Calcular progresso (apenas protocolo está concluído)
  const progressWidth = etapaProtocolo?.status === 'CONCLUIDA' ? 0 : 0;

  return { marcadores: etapasEstimadas, progressWidth };
};

const TimelineUnificada: React.FC<TimelineUnificadaProps> = ({ etapas, showOposicaoBadge, estimativasDb, processo }) => {
  // Estado para detectar se estamos na faixa de breakpoint 1024-1279px
  const [isTabletBreakpoint, setIsTabletBreakpoint] = useState(false);

  // Estado para controlar tooltips
  const [tooltipVisivel, setTooltipVisivel] = useState<string | null>(null);

  // Função para obter descrição da etapa
  const obterDescricaoEtapa = (nomeEtapa: string): string => {
    // Normalizar o nome da etapa para busca
    const nomeNormalizado = nomeEtapa.trim();

    // Buscar correspondência exata primeiro
    if (DESCRICOES_ETAPAS[nomeNormalizado]) {
      return DESCRICOES_ETAPAS[nomeNormalizado];
    }

    // Buscar correspondência parcial para casos especiais
    const chaves = Object.keys(DESCRICOES_ETAPAS);
    for (const chave of chaves) {
      if (nomeNormalizado.toLowerCase().includes(chave.toLowerCase()) ||
          chave.toLowerCase().includes(nomeNormalizado.toLowerCase())) {
        return DESCRICOES_ETAPAS[chave];
      }
    }

    return ''; // Retorna vazio se não encontrar descrição
  };

  // Hook para detectar mudanças no tamanho da tela
  useEffect(() => {
    const checkScreenSize = () => {
      const width = window.innerWidth;
      setIsTabletBreakpoint(width >= 1024 && width <= 1279);
    };

    // Verificar tamanho inicial
    checkScreenSize();

    // Adicionar listener para mudanças
    window.addEventListener('resize', checkScreenSize);

    // Cleanup
    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);

  // Filtrar etapas inválidas (como "Em Andamento" quando há outras etapas válidas)
  const etapasLimpas = useMemo(() => filtrarEtapasLimpas(etapas), [etapas]);

  // Verificar se há sobrestamento na timeline
  const temSobrestamentoNaTimeline = useMemo(() => temSobrestamento(etapasLimpas), [etapasLimpas]);

  // Calcular as posições dos marcadores usando as funções refatoradas
  const { marcadores, progressWidth } = useMemo(() => {
    if (!etapasLimpas || etapasLimpas.length === 0) return { marcadores: [], progressWidth: 0 };

    // Aplicar lógica de análise da nulidade antes de processar as etapas
    const etapasComAnaliseNulidade = adicionarAnaliseNulidade(etapasLimpas);

    // CASO ESPECIAL: Processo não publicado
    if (isProcessoNaoPublicado(etapasComAnaliseNulidade, processo)) {
      // Montar etapas estimadas (protocolo + estimadas)
      const etapaProtocolo = etapasComAnaliseNulidade.find(e => getTipoEtapa(e) === 'protocolo');
      if (!etapaProtocolo) return { marcadores: [], progressWidth: 0 };
      // Datas base
      const dataProtocolo = etapaProtocolo.dataInicio || etapaProtocolo.dataFim;
      // Estimativas
      const publicacaoEstimada = {
        idOriginal: 'PUBLICACAO_ESTIMADA_01',
        etapaDefinicao: {
          nomeOriginal: 'Publicação',
          statusSimples: 'Aguardando Publicação',
          tipo: 'ESTIMATIVA'
        },
        status: 'PROXIMA_ESTIMADA' as const,
        dataInicio: dataProtocolo ? new Date(new Date(dataProtocolo).getTime() + 1000 * 60 * 60 * 24 * 17) : undefined,
        dataFim: null,
        posicao: 33.33,
        posicaoPixels: 200,
        isAlcancada: false,
        isPrazoPendente: false,
        isSobrestamento: false,
        isUltimaAnalise: false,
        isProtocolo: false,
        isArquivamento: false,
        isIndeferimento: false,
        isDeferimento: false,
        isConcessao: false,
        isRecursoExpirado: false,
        observacaoGerada: undefined
      };
      const fimOposicaoEstimado = {
        idOriginal: 'FIM_OPOSICAO_ESTIMADO_01',
        etapaDefinicao: {
          nomeOriginal: 'Fim da fase de oposição',
          statusSimples: 'Aguardando Fim da Oposição',
          tipo: 'ESTIMATIVA'
        },
        status: 'PROXIMA_ESTIMADA' as const,
        dataInicio: publicacaoEstimada.dataInicio ? new Date(new Date(publicacaoEstimada.dataInicio).getTime() + 1000 * 60 * 60 * 24 * 60) : undefined,
        dataFim: null,
        posicao: 66.66,
        posicaoPixels: 400,
        isAlcancada: false,
        isPrazoPendente: false,
        isSobrestamento: false,
        isUltimaAnalise: false,
        isProtocolo: false,
        isArquivamento: false,
        isIndeferimento: false,
        isDeferimento: false,
        isConcessao: false,
        isRecursoExpirado: false,
        observacaoGerada: undefined
      };
      const analiseMeritoEstimado = {
        idOriginal: 'ANALISE_MERITO_ESTIMADA_01',
        etapaDefinicao: {
          nomeOriginal: 'Análise de mérito',
          statusSimples: 'Aguardando Análise de Mérito',
          tipo: 'ESTIMATIVA'
        },
        status: 'PROXIMA_ESTIMADA' as const,
        dataInicio: dataProtocolo ? new Date(new Date(dataProtocolo).getTime() + 1000 * 60 * 60 * 24 * 600) : undefined,
        dataFim: null,
        posicao: 100,
        posicaoPixels: 600,
        isAlcancada: false,
        isPrazoPendente: false,
        isSobrestamento: false,
        isUltimaAnalise: false,
        isProtocolo: false,
        isArquivamento: false,
        isIndeferimento: false,
        isDeferimento: false,
        isConcessao: false,
        isRecursoExpirado: false,
        observacaoGerada: undefined
      };
      const etapasEstimadas: EtapaTimeline[] = [
        etapaProtocolo,
        publicacaoEstimada,
        fimOposicaoEstimado,
        analiseMeritoEstimado
      ];
      // Montar objeto EstimativasDb
      const estimativasDbFormatado = {
        dataPublicacaoEstimada: publicacaoEstimada.dataInicio ? publicacaoEstimada.dataInicio.toISOString() : undefined,
        dataFimOposicaoEstimado: fimOposicaoEstimado.dataInicio ? fimOposicaoEstimado.dataInicio.toISOString() : undefined,
        dataAnaliseMeritoEstimada: analiseMeritoEstimado.dataInicio ? analiseMeritoEstimado.dataInicio.toISOString() : undefined
      };
      return calcularTimelineCompleta(etapasEstimadas, {
        tipo: 'normal',
        etapas: etapasEstimadas,
        estimativasDb: estimativasDbFormatado,
        usarEscalaLogaritmica: false
      });
    }

    // Verificar tipo de timeline
    const etapaArquivamento = etapasComAnaliseNulidade.find(etapa => getTipoEtapa(etapa) === 'arquivamento');
    const etapaIndeferimento = etapasComAnaliseNulidade.find(etapa => getTipoEtapa(etapa) === 'indeferimento');
    const etapaDeferimento = etapasComAnaliseNulidade.find(etapa => getTipoEtapa(etapa) === 'deferimento');
    const etapaConcessao = etapasComAnaliseNulidade.find(etapa => getTipoEtapa(etapa) === 'concessao');

    // CASO ESPECIAL: Timeline de arquivamento
    if (etapaArquivamento) {
      return calcularTimelineArquivamento(etapasComAnaliseNulidade);
    }

    // CASO ESPECIAL: Timeline de indeferimento
    if (etapaIndeferimento) {
      const indiceIndeferimento = etapasComAnaliseNulidade.findIndex(etapa => etapa.idOriginal === etapaIndeferimento.idOriginal);
      let etapasApartirIndeferimento = etapasComAnaliseNulidade.slice(indiceIndeferimento);
      
      // Primeiro, calcular a timeline normal para verificar se há recurso expirado
      const timelineNormal = calcularTimelineCompleta(etapasApartirIndeferimento, {
        tipo: 'indeferimento',
        etapas: etapasApartirIndeferimento,
        usarEscalaLogaritmica: true
      });
      
      // AJUSTE ESPECIAL: Aplicar apenas se há marcador.isRecursoExpirado na timeline resultante
      const temMarcadorRecursoExpirado = timelineNormal.marcadores.some(marcador => marcador.isRecursoExpirado);

      if (temMarcadorRecursoExpirado) {
        // Ajustar data do recurso para 2 meses após indeferimento
        const dataIndeferimento = etapaIndeferimento.dataInicio || etapaIndeferimento.dataFim;
        if (dataIndeferimento) {
          const dataRecursoAjustada = new Date(dataIndeferimento);
          dataRecursoAjustada.setMonth(dataRecursoAjustada.getMonth() + 2);
          
          etapasApartirIndeferimento = etapasApartirIndeferimento.map(etapa => {
            // Verificar se esta etapa será identificada como recurso expirado
            const seriaRecursoExpirado = timelineNormal.marcadores.find(m => 
              m.idOriginal === etapa.idOriginal && m.isRecursoExpirado
            );
            
            if (seriaRecursoExpirado) {
              return {
                ...etapa,
                dataInicio: dataRecursoAjustada,
                dataFim: dataRecursoAjustada
              };
            }
            return etapa;
          });
          
          // Adicionar arquivamento virtual para cálculo de posições (6 meses após recurso)
          const dataArquivamentoVirtual = new Date(dataRecursoAjustada);
          dataArquivamentoVirtual.setMonth(dataArquivamentoVirtual.getMonth() + 6);
          
          const arquivamentoVirtual: EtapaTimeline = {
            idOriginal: 'ARQUIVAMENTO_VIRTUAL_01',
            etapaDefinicao: {
              nomeOriginal: 'Arquivamento',
              statusSimples: 'Processo Arquivado',
              tipo: 'ESTADO_GERAL'
            },
            status: 'PROXIMA_EXATA', // Não concluído
            dataInicio: dataArquivamentoVirtual,
            dataFim: null,
            observacaoGerada: 'Arquivamento virtual para posicionamento'
          };
          
          etapasApartirIndeferimento.push(arquivamentoVirtual);
        }
      }
      
      return calcularTimelineCompleta(etapasApartirIndeferimento, {
        tipo: 'indeferimento',
        etapas: etapasApartirIndeferimento,
        usarEscalaLogaritmica: true
      });
    }

    // CASO ESPECIAL: Timeline de concessão (registro concedido)
    if (etapaConcessao) {
      // Encontrar o protocolo para incluir na timeline de concessão
      const etapaProtocolo = etapasComAnaliseNulidade.find(etapa => 
        etapa.idOriginal === 'PROTOCOLO_01' || 
        etapa.etapaDefinicao.nomeOriginal.toLowerCase().includes('protocolo')
      );
      
      // Encontrar a renovação para garantir que apareça
      const etapaRenovacao = etapasComAnaliseNulidade.find(etapa => 
        etapa.idOriginal === 'RENOVACAO_01' || 
        etapa.etapaDefinicao.nomeOriginal.toLowerCase().includes('renovação')
      );
      
      const indiceConcessao = etapasComAnaliseNulidade.findIndex(etapa => 
        etapa.idOriginal === 'CONCESSAO_REGISTRO_01' || 
        etapa.etapaDefinicao.nomeOriginal.toLowerCase().includes('concessão')
      );
      
      // Construir etapas para timeline de concessão
      let etapasConcessao: EtapaTimeline[] = [];
      
      // 1. Adicionar protocolo se existir
      if (etapaProtocolo) {
        etapasConcessao.push(etapaProtocolo);
      }
      
      // 2. Adicionar etapas a partir da concessão
      if (indiceConcessao !== -1) {
        const etapasApartirConcessao = etapasComAnaliseNulidade.slice(indiceConcessao);
        etapasConcessao.push(...etapasApartirConcessao);
      }
      
      // 3. Garantir que renovação está incluída (se não estiver já)
      if (etapaRenovacao && !etapasConcessao.some(e => e.idOriginal === etapaRenovacao.idOriginal)) {
        etapasConcessao.push(etapaRenovacao);
      }
      
      // Remover duplicatas mantendo a ordem
      const etapasConcessaoUnicas = etapasConcessao.filter((etapa, index, array) => 
        array.findIndex(e => e.idOriginal === etapa.idOriginal) === index
      );
      
      return calcularTimelineCompleta(etapasConcessaoUnicas, {
        tipo: 'concessao',
        etapas: etapasConcessaoUnicas,
        usarEscalaLogaritmica: false
      });
    }

    // CASO ESPECIAL: Timeline de deferimento (pedido deferido mas sem concessão ainda)
    if (etapaDeferimento && !etapaConcessao) {
      const indiceDeferimento = etapasComAnaliseNulidade.findIndex(etapa => 
        etapa.idOriginal === 'DEFERIMENTO_PEDIDO_01' || 
        etapa.idOriginal === 'DEFERIMENTO_POS_RECURSO_01'
      );
      
      // Se não encontrou pelo ID, tentar pelo nome
      const indiceDeferimentoPorNome = indiceDeferimento === -1 ? 
        etapasComAnaliseNulidade.findIndex(etapa => 
          etapa.etapaDefinicao.nomeOriginal.toLowerCase().includes('deferimento')
        ) : indiceDeferimento;
      
      // Se encontrou o deferimento, usar a partir dele
      if (indiceDeferimentoPorNome !== -1) {
        let etapasApartirDeferimento = [...etapasComAnaliseNulidade.slice(indiceDeferimentoPorNome)];
        
        // Verificar se já existe concessão real nas etapas
        const temConcessaoReal = etapasApartirDeferimento.some(e => 
          e.idOriginal === 'CONCESSAO_REGISTRO_01'
        );
        
        // Se não há concessão real, criar concessão estimada
        if (!temConcessaoReal) {
          const dataEstimadaConcessao = calcularDataEstimadaConcessao(etapasComAnaliseNulidade);
          
          if (dataEstimadaConcessao) {
            // Criar etapa de concessão estimada
            const concessaoEstimada: EtapaTimeline = {
              idOriginal: 'CONCESSAO_ESTIMADA_01',
              etapaDefinicao: {
                nomeOriginal: 'Concessão',
                statusSimples: 'Aguardando Concessão',
                tipo: 'ESTADO_GERAL'
              },
              status: 'PROXIMA_ESTIMADA',
              dataInicio: dataEstimadaConcessao,
              dataFim: null,
              observacaoGerada: 'Estimativa de concessão baseada no prazo de pagamento da taxa'
            };
            
            // Criar etapa de fim do prazo de nulidade estimado (180 dias após a concessão)
            const dataFimPrazoNulidade = calcularDataEstimadaFimPrazoNulidade(dataEstimadaConcessao);
            const fimPrazoNulidadeEstimado: EtapaTimeline = {
              idOriginal: 'FIM_PRAZO_NULIDADE_ESTIMADO_01',
              etapaDefinicao: {
                nomeOriginal: 'Prazo de Nulidade',
                statusSimples: 'Prazo de Nulidade',
                tipo: 'PRAZO'
              },
              status: 'PROXIMA_ESTIMADA',
              dataInicio: dataFimPrazoNulidade,
              dataFim: null,
              observacaoGerada: 'Fim do prazo de 180 dias para pedido de nulidade após a concessão'
            };
            
            // Adicionar as etapas estimadas ao final
            etapasApartirDeferimento.push(concessaoEstimada);
            etapasApartirDeferimento.push(fimPrazoNulidadeEstimado);
          }
        }
        
        return calcularTimelineCompleta(etapasApartirDeferimento, {
          tipo: 'deferimento',
          etapas: etapasApartirDeferimento,
          usarEscalaLogaritmica: false
        });
      }
    }

    // CASO NORMAL: Timeline padrão
    return calcularTimelineCompleta(etapasComAnaliseNulidade, {
      tipo: 'normal',
      etapas: etapasComAnaliseNulidade,
      usarEscalaLogaritmica: false
    });
  }, [etapasLimpas, estimativasDb, processo]);

  return (
    <div className={ showOposicaoBadge ? styles.timelineContainerOposicao : styles.timelineContainer}>
      {/* Linha base cinza */}
      <div className={styles.linhaBase} />
      
      {/* Linha de progresso azul */}
      <div 
        className={styles.linhaProgresso} 
        style={{ width: `${progressWidth}%` }}
      />
      
      {/* Marcadores */}
      {marcadores.map((marcador, index) => {
        const dataFormatada = formatarDataExibicao(marcador, etapasLimpas);
        const prazoExpirado = isPrazoExpirado(marcador);
        const isAnaliseMetito = marcador.etapaDefinicao.nomeOriginal.toLowerCase().includes('análise de mérito');
        
        // Verificar se é análise do recurso
        const isAnaliseRecurso = marcador.etapaDefinicao.nomeOriginal.toLowerCase().includes('análise do recurso') || 
                                marcador.etapaDefinicao.nomeOriginal.toLowerCase().includes('análise de recurso') || 
                                marcador.idOriginal === 'ANALISE_RECURSO_INDEFERIMENTO_01';

        // Verificar se é análise da nulidade
        const isAnaliseNulidade = marcador.idOriginal === 'ANALISE_NULIDADE_ESTIMADA_01';

        // Verificar se é arquivamento virtual (não deve exibir data nem ter estilos de concluído)
        const isArquivamentoVirtual = marcador.idOriginal === 'ARQUIVAMENTO_VIRTUAL_01';

        // Obter estilos de posicionamento usando função refatorada
        let estilosPosicionamento = obterEstilosPosicionamento(marcador, isAnaliseRecurso, isAnaliseMetito);
        
        // CASO ESPECIAL: Arquivamento virtual sempre no final da linha (100%)
        if (isArquivamentoVirtual) {
          estilosPosicionamento = { left: '100%' }; // Pequeno ajuste para não sair da linha
        }

        // Verificar se é marcador de concessão para ajuste de texto no breakpoint tablet
        const isConcessao = (marcador.etapaDefinicao.nomeOriginal.toLowerCase().includes('concessão') ||
                            marcador.idOriginal === 'CONCESSAO_REGISTRO_01' ||
                            marcador.idOriginal === 'CONCESSAO_ESTIMADA_01') &&
                           !marcador.isProtocolo &&
                           !marcador.etapaDefinicao.nomeOriginal.toLowerCase().includes('protocolo');

        // Verificar se é etapa de taxa de concessão
        const { isTaxaConcessao: isEtapaTaxaConcessao, textoFormatado } = formatarEtapaTaxaConcessao(marcador);

        // Obter descrição da etapa para tooltip
        const descricaoEtapa = obterDescricaoEtapa(marcador.etapaDefinicao.nomeOriginal);
        const tooltipId = `${marcador.idOriginal}-${index}`;

        return (
          <div
            key={`${marcador.idOriginal}-${index}`}
            className={`
              ${styles.marcadorContainer}
              ${marcador.isProtocolo ? styles.marcadorProtocolo : ''}
              ${marcador.isArquivamento || isArquivamentoVirtual ? styles.marcadorArquivamento : ''}
              ${marcador.isIndeferimento ? styles.marcadorIndeferimento : ''}
              ${marcador.isDeferimento ? styles.marcadorDeferimento : ''}
              ${marcador.isConcessao ? styles.marcadorConcessao : ''}
              ${marcador.isRecursoExpirado ? styles.marcadorRecursoExpirado : ''}
              ${marcador.idOriginal === 'PUBLICACAO_ESTIMADA_01' ? styles.marcadorPublicacaoEstimada : ''}
              ${marcador.idOriginal === 'FIM_OPOSICAO_ESTIMADO_01' ? styles.marcadorFimOposicaoEstimado : ''}
              ${isAnaliseRecurso ? styles.marcadorAnaliseRecurso : ''}
              ${isAnaliseNulidade ? styles.marcadorAnaliseNulidade : ''}
            `}
            style={estilosPosicionamento}
          >
            {/* Data acima do marcador - OCULTAR para arquivamento virtual e análise da nulidade */}
            {dataFormatada && !isArquivamentoVirtual && !isAnaliseNulidade && (
              <div 
                className={styles.dataContainer}
                style={isTabletBreakpoint && isConcessao ? {
                  left: '50%',
                  transform: 'translateX(-50%)',
                  textAlign: 'center'
                } : {}}
              >
                {dataFormatada.tipo === 'previsao' ? (
                  <div className={styles.dataPrevisao}>
                    <div className={styles.dataPrevisaoLabel}>{dataFormatada.linha1}</div>
                    <div className={styles.dataPrevisaoValor}>{dataFormatada.linha2}</div>
                  </div>
                ) : dataFormatada.tipo === 'estimativa' ? (
                  <div className={styles.dataEstimativa}>
                    {dataFormatada.linha1 && <div className={styles.dataEstimativaValor}>{dataFormatada.linha1}</div>}
                    {dataFormatada.linha2 && <div className={styles.dataEstimativaLabel}>{dataFormatada.linha2}</div>}
                  </div>
                ) : (
                  <div className={styles.dataConfirmada}>{dataFormatada.texto}</div>
                )}
              </div>
            )}
            
            {/* Marcador circular */}
            <div
              className={`
                ${styles.marcador}
                ${marcador.isAlcancada && !isArquivamentoVirtual ? styles.marcadorAlcancado : ''}
                ${marcador.isPrazoPendente ? styles.marcadorPrazoPendente : ''}
                ${marcador.isSobrestamento ? styles.marcadorSobrestamento : ''}
                ${marcador.isUltimaAnalise ? styles.marcadorOpaco : ''}
                ${marcador.isRecursoExpirado ? styles.marcadorRecursoExpiradoCirculo : ''}
                ${isPrazoManifestacao(marcador) ? styles.marcadorPrazoManifestacao : ''}
                ${isExigenciaAtiva(marcador) ? styles.marcadorExigenciaAtiva : ''}
                ${isEtapaTaxaConcessao ? styles.marcadorTaxaConcessao : ''}
              `}
              onMouseEnter={() => descricaoEtapa && setTooltipVisivel(tooltipId)}
              onMouseLeave={() => setTooltipVisivel(null)}
              style={{ position: 'relative', cursor: descricaoEtapa ? 'help' : 'default' }}
            >
              {marcador.isAlcancada && !marcador.isSobrestamento && !isArquivamentoVirtual && (
                <span className={styles.checkIcon}>✓</span>
              )}

              {/* Tooltip */}
              {descricaoEtapa && tooltipVisivel === tooltipId && (
                <div
                  style={{
                    position: 'absolute',
                    bottom: '100%',
                    left: '50%',
                    transform: 'translateX(-50%)',
                    marginBottom: '8px',
                    backgroundColor: '#333',
                    color: 'white',
                    padding: '8px 12px',
                    borderRadius: '6px',
                    fontSize: '12px',
                    lineHeight: '1.4',
                    maxWidth: '250px',
                    textAlign: 'center',
                    zIndex: 1000,
                    boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
                    whiteSpace: 'normal',
                    wordWrap: 'break-word'
                  }}
                >
                  {descricaoEtapa}
                  {/* Seta do tooltip */}
                  <div
                    style={{
                      position: 'absolute',
                      top: '100%',
                      left: '50%',
                      transform: 'translateX(-50%)',
                      width: 0,
                      height: 0,
                      borderLeft: '6px solid transparent',
                      borderRight: '6px solid transparent',
                      borderTop: '6px solid #333'
                    }}
                  />
                </div>
              )}
            </div>
            
            {/* Nome da etapa abaixo do marcador */}
            <div 
              className={`
                ${isEtapaTaxaConcessao ? styles.nomeEtapaTaxaConcessao : styles.nomeEtapa}
                ${prazoExpirado ? styles.prazoExpirado : ''}
                ${temSobrestamentoNaTimeline && isAnaliseMetito ? styles.nomeEtapaOpaco : ''}
              `}
              style={isTabletBreakpoint && isConcessao ? {
                left: '50%',
                transform: 'translateX(-50%)',
                textAlign: 'center'
              } : {}}
            >
              {isEtapaTaxaConcessao && textoFormatado ? (
                <>
                  <div>{textoFormatado.principal}</div>
                  <div className={styles.subtextoTaxaConcessao}>{textoFormatado.subtexto}</div>
                </>
              ) : marcador.etapaDefinicao.nomeOriginal === "Instauração de Nulidade" ? (
                <>
                  <div>Instauração</div>
                  <div>de Nulidade</div>
                </>
              ) : (
                calcularNumeroRenovacao(etapasLimpas, marcador)
              )}
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default TimelineUnificada; 
