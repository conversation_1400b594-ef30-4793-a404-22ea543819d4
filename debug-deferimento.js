// Script de debug para testar a detecção da etapa de deferimento

const etapas = [
  {
    "idOriginal": "PROTOCOLO_01",
    "etapaDefinicao": {
      "id": "PROTOCOLO_01",
      "nomeOriginal": "Protocolo",
      "tipo": "ACONTECIMENTO",
      "statusSimples": "Protocolado",
      "statusDetalhado": "Protocolado, aguardando a publicação.",
      "proximaEtapaNomeReferencia": "Publicação",
      "prazoProximaEtapaDescricao": "20 a 30 dias"
    },
    "status": "CONCLUIDA",
    "dataInicio": "2023-08-09T03:00:00.000Z"
  },
  {
    "idOriginal": "PUBLICACAO_01",
    "etapaDefinicao": {
      "id": "PUBLICACAO_01",
      "nomeOriginal": "Publicação",
      "tipo": "ACONTECIMENTO",
      "statusSimples": "Publicado",
      "statusDetalhado": "Publicado, aguardando o fim do prazo de oposição.",
      "proximaEtapaNomeReferencia": "Fim da fase de oposição",
      "prazoProximaEtapaDescricao": "60 dias, contados a partir da publicação do pedido de registro"
    },
    "status": "CONCLUIDA",
    "dataInicio": "2023-09-05T03:00:00.000Z"
  },
  {
    "idOriginal": "DEFERIMENTO_PEDIDO_01",
    "etapaDefinicao": {
      "id": "DEFERIMENTO_PEDIDO_01",
      "nomeOriginal": "Deferimento do Pedido",
      "tipo": "ACONTECIMENTO",
      "statusSimples": "Pedido Deferido",
      "statusDetalhado": "O pedido de registro da marca foi deferido pelo INPI.",
      "proximaEtapaNomeReferencia": "Prazo para Pagamento da Taxa de Concessão (Ordinário)",
      "prazoProximaEtapaDescricao": "60 dias corridos a partir da data do despacho de deferimento."
    },
    "status": "CONCLUIDA",
    "dataInicio": "2025-05-20T03:00:00.000Z"
  },
  {
    "idOriginal": "PRAZO_PAGAMENTO_TAXA_CONCESSAO_ORDINARIO_01",
    "etapaDefinicao": {
      "id": "PRAZO_PAGAMENTO_TAXA_CONCESSAO_ORDINARIO_01",
      "nomeOriginal": "Prazo para Pagamento da Taxa de Concessão (Ordinário)",
      "tipo": "PRAZO",
      "statusSimples": "Aguardando Pagamento Taxa (Ordinário)",
      "statusDetalhado": "Aguardando o pagamento da taxa de concessão do primeiro decênio e expedição do certificado de registro.",
      "proximaEtapaNomeReferencia": "Prazo para Pagamento da Taxa de Concessão (Extraordinário)",
      "prazoProximaEtapaDescricao": "30 dias corridos após o término do prazo ordinário (com taxa adicional)."
    },
    "status": "ATUAL",
    "dataInicio": "2025-05-20T03:00:00.000Z",
    "dataFim": "2025-07-19T03:00:00.000Z",
    "exigenciaInfo": null
  }
];

// Simular a função getTipoEtapa
const getTipoEtapa = (etapa) => {
  const nome = etapa.etapaDefinicao.nomeOriginal.toLowerCase();
  const id = etapa.idOriginal;
  
  if (nome.includes('protocolo') || id === 'PROTOCOLO_01') return 'protocolo';
  if (nome.includes('publicação') || id === 'PUBLICACAO_01') return 'publicacao';
  if (nome.includes('indeferimento') || id === 'INDEFERIMENTO_PEDIDO_01') return 'indeferimento';
  if (nome.includes('arquivamento') || id === 'ARQUIVAMENTO_01') return 'arquivamento';
  if (nome.includes('concessão') || id === 'CONCESSAO_REGISTRO_01') return 'concessao';
  if (nome.includes('deferimento') || id === 'DEFERIMENTO_PEDIDO_01' || id === 'DEFERIMENTO_POS_RECURSO_01') return 'deferimento';
  if (nome.includes('fim') && nome.includes('oposição')) return 'fim_oposicao';
  if (nome.includes('prazo') && nome.includes('oposição')) return 'fim_oposicao';
  if (nome.includes('manifestação')) return 'manifestacao';
  if (nome.includes('análise')) return 'analise';
  return 'outros';
};

console.log('=== DEBUG DETECÇÃO DE ETAPAS ===');

// Verificar todas as etapas
etapas.forEach((etapa, index) => {
  const tipo = getTipoEtapa(etapa);
  console.log(`Etapa ${index}: ${etapa.etapaDefinicao.nomeOriginal} (${etapa.idOriginal}) -> Tipo: ${tipo}`);
});

// Verificar tipos específicos
const etapaArquivamento = etapas.find(etapa => getTipoEtapa(etapa) === 'arquivamento');
const etapaIndeferimento = etapas.find(etapa => getTipoEtapa(etapa) === 'indeferimento');
const etapaDeferimento = etapas.find(etapa => getTipoEtapa(etapa) === 'deferimento');
const etapaConcessao = etapas.find(etapa => getTipoEtapa(etapa) === 'concessao');

console.log('\n=== ETAPAS ENCONTRADAS ===');
console.log('Arquivamento:', etapaArquivamento ? etapaArquivamento.etapaDefinicao.nomeOriginal : 'NÃO ENCONTRADA');
console.log('Indeferimento:', etapaIndeferimento ? etapaIndeferimento.etapaDefinicao.nomeOriginal : 'NÃO ENCONTRADA');
console.log('Deferimento:', etapaDeferimento ? etapaDeferimento.etapaDefinicao.nomeOriginal : 'NÃO ENCONTRADA');
console.log('Concessão:', etapaConcessao ? etapaConcessao.etapaDefinicao.nomeOriginal : 'NÃO ENCONTRADA');

// Simular lógica de TimelineUnificada
console.log('\n=== LÓGICA DE TIMELINE ===');

if (etapaArquivamento) {
  console.log('RESULTADO: Timeline de ARQUIVAMENTO');
} else if (etapaIndeferimento) {
  console.log('RESULTADO: Timeline de INDEFERIMENTO');
} else if (etapaConcessao) {
  console.log('RESULTADO: Timeline de CONCESSÃO');
} else if (etapaDeferimento && !etapaConcessao) {
  console.log('RESULTADO: Timeline de DEFERIMENTO');
  
  // Verificar busca do índice
  const indiceDeferimento = etapas.findIndex(etapa => 
    etapa.idOriginal === 'DEFERIMENTO_PEDIDO_01' || 
    etapa.idOriginal === 'DEFERIMENTO_POS_RECURSO_01'
  );
  
  const indiceDeferimentoPorNome = indiceDeferimento === -1 ? 
    etapas.findIndex(etapa => 
      etapa.etapaDefinicao.nomeOriginal.toLowerCase().includes('deferimento')
    ) : indiceDeferimento;
  
  console.log('Índice deferimento por ID:', indiceDeferimento);
  console.log('Índice deferimento final:', indiceDeferimentoPorNome);
  
  if (indiceDeferimentoPorNome !== -1) {
    const etapasApartirDeferimento = etapas.slice(indiceDeferimentoPorNome);
    console.log('Etapas a partir do deferimento:');
    etapasApartirDeferimento.forEach((etapa, index) => {
      console.log(`  ${index}: ${etapa.etapaDefinicao.nomeOriginal} (${etapa.idOriginal})`);
    });
  }
} else {
  console.log('RESULTADO: Timeline NORMAL');
} 