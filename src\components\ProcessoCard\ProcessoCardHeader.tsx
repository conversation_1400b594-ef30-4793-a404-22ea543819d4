import React, { useState } from 'react';
import { createPortal } from 'react-dom';
import { usePathname } from 'next/navigation';
import { useProcessoStatus } from '@/hooks/useProcessoStatus';
// Image não é mais necessário aqui

interface ProcessoCardHeaderProps {
  // logoImage não é mais necessário aqui
  nomeMarca: string;
  linkInpi: string;
  numeroProcesso: string;
  codigoNCL: string | null;
  showOposicaoBadge: boolean;
  processo: any; // Adicionar processo para calcular statusText internamente
  onToggleExpand?: (e?: React.MouseEvent) => void; // Nova prop para controlar expansão
}

const ProcessoCardHeader: React.FC<ProcessoCardHeaderProps> = ({ 
    // logoImage removido
    nomeMarca, 
    linkInpi, 
    numeroProcesso, 
    codigoNCL,
    showOposicaoBadge,
    processo, // Receber processo para calcular status
    onToggleExpand // Nova prop
}) => {
  const [showDemoModal, setShowDemoModal] = useState(false);
  const pathname = usePathname();
  
  // Verificar se estamos na página de demonstração
  const isViewPage = pathname === '/view';
  
  // Calcular statusText internamente usando a mesma lógica do debug
  const { statusText } = useProcessoStatus(processo);
  
  // Função para parar a propagação do evento de clique e abrir o link externo
  const handleLinkClick = (e: React.MouseEvent<HTMLSpanElement>) => {
    e.stopPropagation(); // Impede que o clique propague para o link do card
    window.open(linkInpi, '_blank', 'noopener,noreferrer'); // Abre o link do INPI em nova aba
  };

  // Função para download do protocolo
  const handleDownloadProtocolo = async (e: React.MouseEvent<HTMLDivElement>) => {
    e.stopPropagation(); // Impede propagação para outros handlers
    e.preventDefault(); // Previne comportamento padrão
    
    // Se estamos na página de demonstração, mostrar modal informativo
    if (isViewPage) {
      setShowDemoModal(true);
      return;
    }
    
    try {
      console.log('Iniciando download do protocolo para processo:', numeroProcesso);
      
      // URL da API para download do protocolo
      const downloadUrl = `https://api-v3-rgsys.registrese.app.br/api/protocolo/download/${numeroProcesso}`;
      
      // Fazer a requisição para baixar o arquivo
      const response = await fetch(downloadUrl);
      
      if (!response.ok) {
        throw new Error(`Erro ao baixar protocolo: ${response.status} ${response.statusText}`);
      }
      
      // Obter o blob do arquivo
      const blob = await response.blob();
      
      // Criar URL temporária para o blob
      const blobUrl = window.URL.createObjectURL(blob);
      
      // Criar link temporário para download
      const link = document.createElement('a');
      link.href = blobUrl;
      link.download = `protocolo_${numeroProcesso}.pdf`;
      
      // Adicionar ao DOM, clicar e remover
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      // Limpar a URL temporária
      window.URL.revokeObjectURL(blobUrl);
      
      console.log('Download do protocolo concluído com sucesso');
      
      // ✅ NOVO: Registrar log de download
      try {
        await fetch('/api/tracking/download', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            processoId: processo.id,
            numeroProcesso: numeroProcesso,
            fileSize: blob.size,
            success: true
          })
        });
      } catch (trackingError) {
        console.warn('Erro ao registrar log de download:', trackingError);
        // Não interrompe o fluxo se o tracking falhar
      }
      
    } catch (error) {
      console.error('Erro ao fazer download do protocolo:', error);
      
      // ✅ NOVO: Registrar log de erro de download
      try {
        await fetch('/api/tracking/download', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            processoId: processo.id,
            numeroProcesso: numeroProcesso,
            success: false,
            errorMessage: error instanceof Error ? error.message : 'Erro desconhecido'
          })
        });
      } catch (trackingError) {
        console.warn('Erro ao registrar log de erro de download:', trackingError);
      }
      
      // Aqui você pode adicionar uma notificação de erro para o usuário
      alert('Erro ao baixar o protocolo. Tente novamente mais tarde.');
    }
  };

  // Função para lidar com cliques no header (expandir card)
  const handleHeaderClick = (e: React.MouseEvent) => {
    // Verificar se o clique foi no número do processo ou botão de download
    const target = e.target as HTMLElement;
    
    // Se clicou em elementos que não devem expandir, não fazer nada
    if (target.closest('[data-no-expand]')) {
      return;
    }
    
    // Chamar a função de toggle se disponível
    if (onToggleExpand) {
      onToggleExpand();
    }
  };

  return (
    // Renderiza apenas a seção do cabeçalho (Nome, Processo, Classe)
    <div
      className="flex justify-between items-start cursor-pointer"
      onClick={handleHeaderClick}
    >
      <div>
        {" "}
        {/* Nome da Marca */}
        <h2 className="text-3xl font-bold mb-1">
          <span
            className="truncate max-w-[400px] min-w-0 inline-block align-middle select-text cursor-text"
            title={nomeMarca}
          >
            {nomeMarca}
          </span>
        </h2>
        <p className="text-gray-700 font-medium  mt-2 select-text cursor-text">
          {statusText}
        </p>
        {showOposicaoBadge && (
          <div className="mt-2">
            <span className="inline-block px-3 py-1 bg-[#7E7E7E] text-white rounded-md text-sm select-text cursor-text">
              Pedido com oposição
            </span>
          </div>
        )}
      </div>

      <div className="text-right flex gap-2">
        {" "}
        {/* Processo e Classe */}
        <div className="flex items-center mr-1">
          {/* Container unificado para texto + botão - sem borda externa */}
          <div
            className="flex items-center h-[35px]"
            onClick={handleDownloadProtocolo}
          >
            {/* Texto "Baixar protocolo" com bordas específicas */}
            <div className="h-[35px] px-3 flex items-center justify-center hover:bg-gray-50 transition-all duration-300 bg-white border-1 border-[#45B063] border-r-0 rounded-l-lg">
              <p className="text-gray-900 text-sm font-medium whitespace-nowrap mr-[0.4px]">
                Baixar protocolo
              </p>
            </div>
            {/* Botão SVG - sem modificações no SVG original */}
            <div
              className="cursor-pointer hover:opacity-80 transition-opacity h-full flex items-center -ml-1 border-y-1 rounded-r-lg border-[#45B063]"
              title="Baixar protocolo do processo"
              data-no-expand="true"
            >
              <svg
                width="37"
                height="35"
                viewBox="0 0 45 42"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                className="block"
              >
                <rect width="45" height="42" rx="7" fill="#45B063" />
                <path
                  d="M9.87097 22.5548C9.37476 22.5548 8.89887 22.752 8.54799 23.1028C8.19712 23.4537 8 23.9296 8 24.4258V26.2968C8 27.7854 8.59136 29.2131 9.64398 30.2657C10.6966 31.3183 12.1243 31.9097 13.6129 31.9097H31.3871C32.8757 31.9097 34.3034 31.3183 35.356 30.2657C36.4086 29.2131 37 27.7854 37 26.2968V24.4258C37 23.9296 36.8029 23.4537 36.452 23.1028C36.1011 22.752 35.6252 22.5548 35.129 22.5548C34.6328 22.5548 34.1569 22.752 33.8061 23.1028C33.4552 23.4537 33.2581 23.9296 33.2581 24.4258V26.2968C33.2581 26.793 33.0609 27.2689 32.7101 27.6198C32.3592 27.9706 31.8833 28.1677 31.3871 28.1677H13.6129C13.1167 28.1677 12.6408 27.9706 12.2899 27.6198C11.9391 27.2689 11.7419 26.793 11.7419 26.2968V24.4258C11.7419 23.9296 11.5448 23.4537 11.1939 23.1028C10.8431 22.752 10.3672 22.5548 9.87097 22.5548Z"
                  fill="white"
                />
                <path
                  d="M22.5 10C22.0037 10 21.5279 10.1972 21.177 10.548C20.8261 10.8989 20.629 11.3748 20.629 11.871V19.1798L17.8225 16.3734C17.5097 16.0456 17.0923 15.8373 16.6423 15.7843C16.1923 15.7314 15.7379 15.8372 15.3575 16.0834C15.1321 16.2394 14.9436 16.4431 14.8056 16.68C14.6675 16.917 14.5833 17.1813 14.5587 17.4544C14.5342 17.7276 14.57 18.0027 14.6635 18.2605C14.7571 18.5182 14.9062 18.7522 15.1003 18.946L19.6093 23.4456C20.3774 24.2114 21.4177 24.6414 22.5023 24.6414C23.5869 24.6414 24.6272 24.2114 25.3953 23.4456L29.909 18.946C30.103 18.7522 30.2521 18.5182 30.3457 18.2605C30.4393 18.0027 30.4751 17.7276 30.4506 17.4544C30.426 17.1813 30.3417 16.917 30.2037 16.68C30.0657 16.4431 29.8772 16.2394 29.6517 16.0834C29.2714 15.8372 28.817 15.7314 28.367 15.7843C27.917 15.8373 27.4996 16.0456 27.1867 16.3734L24.3803 19.1798V11.871C24.3803 11.6245 24.3316 11.3805 24.237 11.1529C24.1424 10.9252 24.0037 10.7186 23.829 10.5447C23.6543 10.3709 23.4469 10.2332 23.2188 10.1398C22.9907 10.0463 22.7464 9.99881 22.5 10Z"
                  fill="white"
                />
              </svg>
            </div>
          </div>
        </div>
        <div className="text-[20px] font-normal block w-max text-left select-text cursor-text">
          <span className="block">Processo:</span>
          <span className="block">Classe:</span>
        </div>
        <div className="text-[20px] font-bold block w-max text-left">
          <span
            className="block hover:underline cursor-pointer select-text" // Mantém select-text mesmo sendo clicável
            onClick={handleLinkClick} // Atualizado para o span
            title="Abrir processo no INPI" // Tooltip opcional
            data-no-expand="true"
          >
            {numeroProcesso}
          </span>
          <span className="block select-text cursor-text">
            {codigoNCL || "01"}
          </span>
        </div>
      </div>

      {/* Modal Informativo para Página de Demonstração usando Portal */}
      {showDemoModal &&
        typeof window !== "undefined" &&
        createPortal(
          <div
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
            style={{ zIndex: 10000 }}
            onClick={() => setShowDemoModal(false)}
          >
            <div
              className="bg-white rounded-lg max-w-md w-full p-6 shadow-xl relative"
              onClick={(e) => e.stopPropagation()}
            >
              {/* Botão X no canto superior direito */}
              <button
                onClick={() => setShowDemoModal(false)}
                className="absolute -top-2 -right-2 w-8 h-8 bg-gray-600 hover:bg-gray-700 text-white rounded-full flex items-center justify-center transition-colors shadow-lg"
                aria-label="Fechar modal"
              >
                <svg
                  className="w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>

              <div className="text-center">
                {/* Ícone de registro */}
                <div className="mx-auto mb-4 w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
                  <svg
                    className="w-8 h-8 text-green-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                </div>

                <h3 className="text-xl font-bold text-gray-900 mb-3">
                  Proteja sua marca agora!
                </h3>

                <p className="text-gray-600 mb-6 leading-relaxed">
                  Vamos protocolar seu pedido de registro de marca de forma ágil
                  e descomplicada. Nossa equipe especializada cuidará de todo o
                  processo junto ao INPI.
                </p>

                <div className="flex justify-center">
                  <button
                    onClick={() => {
                      window.open(
                        "https://registre.se/",
                        "_blank",
                        "noopener,noreferrer"
                      );
                      setShowDemoModal(false);
                    }}
                    className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium"
                  >
                    Registrar Minha Marca
                  </button>
                </div>
              </div>
            </div>
          </div>,
          document.body
        )}
    </div>
    // Estrutura de duas colunas e imagem foram removidas
  );
};

export default ProcessoCardHeader; 