'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import ProcessoCard from '@/components/ProcessoCard';
import Image from 'next/image';
import Link from 'next/link';
import { FiLogOut, FiArrowLeft, FiUsers } from 'react-icons/fi';
import { useEstimativasMerito } from '@/hooks/useEstimativasMerito';
import Breadcrumb from '@/components/Breadcrumb';

interface AdminUser {
  id: string;
  email: string;
  nome: string;
  role: 'MASTER' | 'COMUM';
}

interface BuscaParams {
  termo?: string;          // Busca geral (compatibilidade com API)
  nomeCliente?: string;    // Busca por nome do cliente  
  nomeMarca?: string;      // Busca específica por nome da marca
  telefone?: string;       // Busca por telefone do cliente
  numeroProcesso?: string; // Busca específica por número
  identificadorCliente?: string; // Busca por identificador (compatibilidade)
  clienteId?: number;      // Busca por ID do cliente (compatibilidade)
  page?: number;
  pageSize?: number;
  somenteClientes?: boolean; // Nova opção para buscar apenas clientes
}

type TipoBusca = 'nomeCliente' | 'nomeMarca' | 'telefone' | 'numeroProcesso' | 'somenteClientes';

interface BuscaResultados {
  processos: any[];
  totalProcessos: number;
  currentPage: number;
  totalPages: number;
  pageSize: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  filtrosAplicados: any;
}

// Função para formatar telefone
function formatarTelefone(telefone: string): string {
  if (!telefone) return '';
  // Remove tudo que não é número
  const numeros = telefone.replace(/\D/g, '');
  // Se começa com 55 e tem 13 dígitos (ex: 5519991131354)
  if (numeros.length === 13 && numeros.startsWith('55')) {
    const ddd = numeros.slice(2, 4);
    const parte1 = numeros.slice(4, 9);
    const parte2 = numeros.slice(9);
    return `+55 (${ddd}) ${parte1}-${parte2}`;
  }
  // Se começa com 55 e tem 12 dígitos (fixo)
  if (numeros.length === 12 && numeros.startsWith('55')) {
    const ddd = numeros.slice(2, 4);
    const parte1 = numeros.slice(4, 8);
    const parte2 = numeros.slice(8);
    return `+55 (${ddd}) ${parte1}-${parte2}`;
  }
  // Se tem 11 dígitos (celular nacional)
  if (numeros.length === 11) {
    return `(${numeros.slice(0, 2)}) ${numeros.slice(2, 7)}-${numeros.slice(7)}`;
  }
  // Se tem 10 dígitos (fixo nacional)
  if (numeros.length === 10) {
    return `(${numeros.slice(0, 2)}) ${numeros.slice(2, 6)}-${numeros.slice(6)}`;
  }
  return telefone; // Retorna original se não conseguir formatar
}

// Componente para o bloco do cliente (com ou sem processo)
function ClienteInfoBlock({ cliente, autoLoginUrl, identificador, somenteCliente = false, processo, estimativasMerito }: { 
  cliente?: any; 
  autoLoginUrl?: string; 
  identificador?: string;
  somenteCliente?: boolean;
  processo?: any;
  estimativasMerito: any;
}) {
  const [copiando, setCopiando] = useState(false);
  const [expandido, setExpandido] = useState(false);

  const copiarLink = async () => {
    if (!autoLoginUrl) return;
    
    try {
      await navigator.clipboard.writeText(autoLoginUrl);
      setCopiando(true);
      setTimeout(() => setCopiando(false), 2500);
    } catch (error) {
      console.error('Erro ao copiar link:', error);
      // Fallback para browsers mais antigos
      const textArea = document.createElement('textarea');
      textArea.value = autoLoginUrl;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      setCopiando(true);
      setTimeout(() => setCopiando(false), 2500);
    }
  };

  const linkDisplay = autoLoginUrl || identificador || 'N/A';
  const isValidUrl = autoLoginUrl && autoLoginUrl.startsWith('http');
  const linkTruncado = linkDisplay.length > 60 ? linkDisplay.substring(0, 60) + '...' : linkDisplay;

  // Dados do cliente
  const nomeCliente = cliente?.nome || 'Nome não informado';
  const emailCliente = cliente?.ContatoCliente?.[0]?.email || 'Email não informado';
  const telefoneCliente = cliente?.ContatoCliente?.[0]?.telefone || cliente?.ContatoCliente?.[0]?.telefoneSegundario || '';

  return (
    <div className="bg-gray-50 border border-gray-200 rounded-lg shadow-sm mb-8">
      <div className="p-6">
        {/* Header do cliente */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <div className="bg-blue-100 p-2 rounded-full">
              <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
            </div>
            <div>
              <h3 className="font-semibold text-gray-900">{nomeCliente}</h3>
              <p className="text-sm text-gray-500">Cliente</p>
            </div>
          </div>
          
          {isValidUrl && (
            <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full font-medium">
              Link Ativo
            </span>
          )}
        </div>

        {/* Grid de informações */}
        <div className="flex flex-col gap-4 lg:flex-row lg:gap-6">
          {/* Link de acesso */}
          <div className="flex-1 min-w-0">
            <label className="flex items-center text-sm font-medium text-gray-700 mb-2 gap-1">
              <svg className="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 010 5.656m-1.414-1.414a2 2 0 010-2.828m-2.828 2.828a4 4 0 010-5.656m1.414 1.414a2 2 0 010 2.828" />
              </svg>
              Link de Acesso
            </label>
            <div className="flex items-center bg-white rounded-lg border border-gray-200 px-3 py-2">
              <span className="flex-1 text-sm font-mono text-gray-800 truncate">
                {expandido ? linkDisplay : linkTruncado}
              </span>
              {linkDisplay.length > 60 && (
                <button
                  onClick={() => setExpandido(!expandido)}
                  className="ml-2 text-xs text-blue-600 hover:text-blue-800 underline transition-colors"
                >
                  {expandido ? '↑ Menos' : '↓ Ver completo'}
                </button>
              )}
              <div className="flex items-center space-x-2 ml-2">
                {isValidUrl ? (
                  <>
                    <button
                      onClick={copiarLink}
                      disabled={copiando}
                      className={`inline-flex items-center px-2 py-1 text-xs font-medium rounded-md transition-colors ${
                        copiando 
                          ? 'bg-green-100 text-green-800 border border-green-200' 
                          : 'bg-blue-100 text-blue-800 border border-blue-200 hover:bg-blue-200'
                      }`}
                    >
                      {copiando ? (
                        <>
                          <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          </svg>
                          Copiado!
                        </>
                      ) : (
                        <>
                          <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                          </svg>
                          Copiar
                        </>
                      )}
                    </button>
                    <button
                      onClick={() => window.open(autoLoginUrl, '_blank')}
                      className="inline-flex items-center px-2 py-1 text-xs font-medium text-gray-700 bg-gray-100 border border-gray-200 rounded-md hover:bg-gray-200 transition-colors"
                    >
                      <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                      </svg>
                      Abrir
                    </button>
                  </>
                ) : (
                  <span className="inline-flex items-center px-2 py-1 text-xs font-medium text-orange-800 bg-orange-100 border border-orange-200 rounded-md">
                    <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                    Indisponível
                  </span>
                )}
              </div>
            </div>
          </div>
          {/* Email */}
          <div className="flex-1 min-w-0">
            <label className="flex items-center text-sm font-medium text-gray-700 mb-2 gap-1">
              <svg className="w-4 h-4 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 12a4 4 0 01-8 0V8a4 4 0 018 0v4z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 16v2m0 4h.01" />
              </svg>
              Email
            </label>
            <div className="bg-white rounded-lg border border-gray-200 px-3 py-2 text-sm text-gray-800 truncate">
              {emailCliente}
            </div>
          </div>
          {/* Telefone */}
          <div className="flex-1 min-w-0">
            <label className="flex items-center text-sm font-medium text-gray-700 mb-2 gap-1">
              <svg className="w-4 h-4 text-pink-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H5a2 2 0 01-2-2V5zm0 10a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H5a2 2 0 01-2-2v-2zm10-10a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zm0 10a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
              </svg>
              Telefone
            </label>
            <div className="bg-white rounded-lg border border-gray-200 px-3 py-2 text-sm text-gray-800 font-mono truncate">
              {telefoneCliente ? formatarTelefone(telefoneCliente) : '-'}
            </div>
          </div>
        </div>

        {/* Card do processo aninhado */}
        {!somenteCliente && processo && (
          <div className="mt-0 mx-0 mb-0 lg:mx-4 lg:mb-4 lg:mt-6">
            <div className="">
              <ProcessoCard processo={processo} estimativasMerito={estimativasMerito} />
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

// Componente de loading skeleton para os cards
function ProcessoCardSkeleton() {
  return (
    <div className="animate-pulse shadow-lg rounded-lg overflow-hidden">
      {/* Skeleton do ClienteLinkBlock */}
      <div className="bg-gradient-to-br from-gray-300 via-gray-400 to-gray-300 p-5 rounded-t-lg relative overflow-hidden">
        {/* Efeito shimmer */}
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-30 -skew-x-12 animate-pulse"></div>
        
        <div className="relative flex items-center justify-between">
          <div className="flex items-center space-x-4">
            {/* Avatar skeleton */}
            <div className="relative">
              <div className="bg-gray-500 w-12 h-12 rounded-full"></div>
              <div className="absolute -top-1 -right-1 w-4 h-4 bg-gray-600 rounded-full"></div>
            </div>
            
            <div className="flex-1">
              <div className="flex items-center space-x-2 mb-2">
                <div className="bg-gray-500 h-3 w-24 rounded"></div>
                <div className="bg-gray-500 h-4 w-12 rounded-full"></div>
              </div>
              
              <div className="bg-gray-500 bg-opacity-40 rounded-lg p-3">
                <div className="bg-gray-600 h-4 w-64 rounded mb-1"></div>
                <div className="bg-gray-600 h-3 w-20 rounded"></div>
              </div>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <div className="bg-gray-500 w-12 h-12 rounded-xl"></div>
            <div className="bg-gray-500 w-12 h-12 rounded-xl"></div>
          </div>
        </div>
      </div>
      
      {/* Skeleton do ProcessoCard */}
      <div className="bg-white p-6 rounded-b-lg border border-gray-200 border-t-0">
        <div className="flex justify-between items-start mb-4">
          <div className="flex-1">
            <div className="bg-gray-300 h-6 w-3/4 rounded mb-2"></div>
            <div className="bg-gray-300 h-4 w-1/2 rounded"></div>
          </div>
          <div className="bg-gray-300 h-8 w-20 rounded"></div>
        </div>
        
        <div className="space-y-3">
          <div className="bg-gray-300 h-4 w-full rounded"></div>
          <div className="bg-gray-300 h-4 w-2/3 rounded"></div>
          <div className="bg-gray-300 h-4 w-3/4 rounded"></div>
        </div>
        
        <div className="flex gap-2 mt-4">
          <div className="bg-gray-300 h-6 w-16 rounded-full"></div>
          <div className="bg-gray-300 h-6 w-20 rounded-full"></div>
          <div className="bg-gray-300 h-6 w-24 rounded-full"></div>
        </div>
      </div>
    </div>
  );
}

export default function AdminComumPage() {
  const [user, setUser] = useState<AdminUser | null>(null);
  const [loading, setLoading] = useState(true);
  const [resultados, setResultados] = useState<BuscaResultados | null>(null);
  const [buscaLoading, setBuscaLoading] = useState(false);
  const [ultimaConsulta, setUltimaConsulta] = useState<BuscaParams | null>(null);
  const [tipoBusca, setTipoBusca] = useState<TipoBusca>('nomeCliente');
  const [termoBusca, setTermoBusca] = useState('');
  const [pageSize, setPageSize] = useState(20);
  const [apenasProcuradorRegistrese, setApenasProcuradorRegistrese] = useState(true);
  const [buscaExata, setBuscaExata] = useState(false);
  const router = useRouter();

  const { estimativas: estimativasMerito, loading: loadingEstimativas } = useEstimativasMerito();

  useEffect(() => {
    // Verificar autenticação
    const checkAuth = async () => {
      try {
        const response = await fetch('/api/admin/auth/me');
        if (response.ok) {
          const userData = await response.json();
          setUser(userData.user);
        } else {
          router.push('/admin/login');
        }
      } catch (error) {
        console.error('Erro ao verificar autenticação:', error);
        router.push('/admin/login');
      } finally {
        setLoading(false);
      }
    };

    checkAuth();
  }, [router]);

  const realizarBusca = useCallback(async (page: number = 1) => {
    if (!termoBusca.trim() && tipoBusca !== 'somenteClientes') {
      alert('Digite um termo para buscar');
      return;
    }

    setBuscaLoading(true);
    
    try {
      const params = new URLSearchParams();
      
      // Configurar parâmetros baseado no tipo de busca
      if (tipoBusca === 'somenteClientes') {
        params.append('somenteClientes', 'true');
      } else {
        params.append(tipoBusca, termoBusca.trim());
      }

      // Adicionar filtro de procurador
      if (apenasProcuradorRegistrese) {
        params.append('apenasProcuradorRegistrese', 'true');
      }

      // Adicionar filtro de busca exata
      if (buscaExata) {
        params.append('exactMatch', 'true');
      }
      
      params.append('page', String(page));
      params.append('pageSize', String(pageSize));

      console.log('🔍 Realizando busca:', { tipoBusca, termoBusca, page, apenasProcuradorRegistrese, buscaExata });

      const response = await fetch(`/api/admin/processos/busca?${params.toString()}`);
      
      if (!response.ok) {
        throw new Error(`Erro na busca: ${response.status}`);
      }
      
      const dados = await response.json();
      
      console.log('📊 Dados recebidos:', dados);
      
      setResultados(dados);
      setUltimaConsulta({ [tipoBusca]: termoBusca, page, pageSize });
      
    } catch (error) {
      console.error('❌ Erro ao buscar:', error);
      alert('Erro ao realizar busca. Tente novamente.');
    } finally {
      setBuscaLoading(false);
    }
  }, [tipoBusca, termoBusca, pageSize, apenasProcuradorRegistrese, buscaExata]);

  const handleBuscar = () => {
    realizarBusca(1);
  };

  const handleLimpar = () => {
    setTermoBusca('');
    setTipoBusca('nomeCliente');
    setResultados(null);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleBuscar();
    }
  };

  const handleChangePage = useCallback((page: number) => {
    realizarBusca(page);
  }, [realizarBusca]);

  const handleLogout = async () => {
    try {
      await fetch('/api/admin/auth/logout', { method: 'POST' });
      router.push('/admin/login');
    } catch (error) {
      console.error('Erro no logout:', error);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-backgroundCinza flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-azul mx-auto mb-4"></div>
          <p className="text-cinzaTexto">Carregando...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-backgroundCinza">
      {/* Header igual ClienteHeader */}
      <header className="bg-green-500 shadow-md fixed top-0 left-0 right-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-2 flex justify-between items-center">
          <div>
            <Image src="/logo.svg" alt="Logo Registre.se" width={150} height={40} className="h-16 w-auto" priority />
          </div>
          <button
            onClick={handleLogout}
            title="Sair"
            className="flex items-center justify-center h-10 w-10 rounded-full bg-white text-green-500 hover:bg-green-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-green-500 focus:ring-white cursor-pointer"
            aria-label="Sair"
          >
            <FiLogOut className="w-6 h-6" />
          </button>
        </div>
      </header>
      <div className="h-20" /> {/* Espaço para o header fixo */}

      <div className="max-w-7xl mx-auto px-6 py-8">
        <Breadcrumb items={[
          { label: 'Admin', href: '/admin' },
          { label: 'Busca' }
        ]} />
        
        {/* Formulário de Busca */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
          <div className="p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-lg font-semibold text-gray-900">
                Buscar Processos
              </h2>
              {user?.role === 'MASTER' && (
                <Link
                  href="/admin/clientes"
                  className="flex items-center px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors text-sm"
                >
                  <FiUsers className="w-4 h-4 mr-2" />
                  Gerenciar Clientes
                </Link>
              )}
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Tipo de busca */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Tipo de Busca
                </label>
                <div className="space-y-2">
                  {[
                    { value: 'nomeCliente', label: '👤 Nome do Cliente', placeholder: 'Digite o nome do cliente...' },
                    { value: 'nomeMarca', label: '🏷️ Nome da Marca', placeholder: 'Digite o nome da marca...' },
                    { value: 'telefone', label: '📱 Telefone', placeholder: 'Digite o telefone...' },
                    { value: 'numeroProcesso', label: '📄 Número do Processo', placeholder: 'Digite o número do processo...' }
                  ].map((opcao) => (
                    <label key={opcao.value} className="flex items-center">
                      <input
                        type="radio"
                        name="tipoBusca"
                        value={opcao.value}
                        checked={tipoBusca === opcao.value}
                        onChange={(e) => setTipoBusca(e.target.value as TipoBusca)}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                      />
                      <span className="ml-2 text-sm text-gray-700">{opcao.label}</span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Campo de busca */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Termo de Busca
                </label>
                <input
                  type="text"
                  placeholder={
                    tipoBusca === 'nomeCliente' ? 'Digite o nome do cliente...' :
                    tipoBusca === 'nomeMarca' ? 'Digite o nome da marca...' :
                    tipoBusca === 'telefone' ? 'Digite o telefone...' :
                    'Digite o número do processo...'
                  }
                  value={termoBusca}
                  onChange={(e) => setTermoBusca(e.target.value)}
                  onKeyPress={handleKeyPress}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                />
              </div>

              {/* Ações */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Ações
                </label>
                <div className="space-y-3">
                  <button
                    onClick={handleBuscar}
                    disabled={buscaLoading}
                    className="w-full inline-flex items-center justify-center px-4 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
                  >
                    {buscaLoading ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Buscando...
                      </>
                    ) : (
                      <>
                        <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                        Buscar
                      </>
                    )}
                  </button>

                  <button
                    onClick={handleLimpar}
                    disabled={buscaLoading}
                    className="w-full px-4 py-2 text-gray-700 bg-gray-100 border border-gray-300 rounded-lg hover:bg-gray-200 focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 disabled:cursor-not-allowed transition-colors"
                  >
                    Limpar
                  </button>

                  <div className="pt-2 space-y-3">
                    <label className="flex items-center space-x-2 cursor-pointer">
                        <input
                            type="checkbox"
                            checked={apenasProcuradorRegistrese}
                            onChange={(e) => setApenasProcuradorRegistrese(e.target.checked)}
                            className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                        <span className="text-sm font-medium text-gray-700">Apenas clientes Registre.se</span>
                    </label>
                    
                    <label className="flex items-center space-x-2 cursor-pointer">
                        <input
                            type="checkbox"
                            checked={buscaExata}
                            onChange={(e) => setBuscaExata(e.target.checked)}
                            className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                        <div className="flex flex-col">
                          <span className="text-sm font-medium text-gray-700">Busca exata</span>
                          <span className="text-xs text-gray-500">
                            {buscaExata ? 'Busca pelo termo exato' : 'Busca por termos que contenham o texto'}
                          </span>
                        </div>
                    </label>
                  </div>

                  <div className="pt-2">
                    <label className="block text-xs font-medium text-gray-600 mb-1">
                      Resultados por página
                    </label>
                    <select
                      value={pageSize}
                      onChange={(e) => setPageSize(parseInt(e.target.value))}
                      className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value={10}>10</option>
                      <option value={20}>20</option>
                      <option value={50}>50</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Resultados */}
        {!resultados ? (
          <div className="bg-branco p-8 rounded-lg shadow-md text-center">
            <div className="text-cinzaTexto">
              <svg
                className="mx-auto h-12 w-12 text-cinza mb-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                />
              </svg>
              <h3 className="text-lg font-medium text-TextosEscurosEBg mb-2">
                Realizar busca
              </h3>
              <p className="text-cinzaTexto">
                Use os filtros acima para buscar processos no sistema
              </p>
            </div>
          </div>
        ) : resultados.processos.length === 0 ? (
          <div className="bg-branco p-8 rounded-lg shadow-md text-center">
            <div className="text-cinzaTexto">
              <svg
                className="mx-auto h-12 w-12 text-cinza mb-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                />
              </svg>
              <h3 className="text-lg font-medium text-TextosEscurosEBg mb-2">
                Nenhum processo encontrado
              </h3>
              <p className="text-cinzaTexto">
                Tente ajustar os filtros de busca
              </p>
            </div>
          </div>
        ) : (
          <div className="space-y-6">
            {/* Header dos resultados */}
            <div className="bg-branco p-4 rounded-lg shadow-md">
              <div className="flex justify-between items-center">
                <div>
                  <h3 className="text-lg font-semibold text-TextosEscurosEBg">
                    Resultados da Busca
              </h3>
                  <p className="text-sm text-cinzaTexto">
                    Mostrando{" "}
                    {(resultados.currentPage - 1) * resultados.pageSize + 1}-
                    {Math.min(
                      (resultados.currentPage - 1) * resultados.pageSize +
                        resultados.pageSize,
                      resultados.totalProcessos
                    )}{" "}
                    de {resultados.totalProcessos} processos
                  </p>
                </div>

                <div className="text-sm text-cinzaTexto bg-cinzaFaixa px-3 py-2 rounded-md">
                  Página {resultados.currentPage} de {resultados.totalPages}
                </div>
              </div>

              {/* Mostrar filtros aplicados */}
              {Object.entries(resultados.filtrosAplicados || {}).some(
                ([key, value]) => key !== "page" && key !== "pageSize" && value
              ) && (
                <div className="mt-3 pt-3 border-t border-cinzaClaro">
                  <span className="text-sm font-medium text-TextosEscurosEBg">
                    Filtros aplicados:{" "}
                  </span>
                  <div className="flex flex-wrap gap-2 mt-1">
                    {Object.entries(resultados.filtrosAplicados).map(
                      ([key, value]) => {
                        if (key === "page" || key === "pageSize" || !value)
                          return null;

                        const labels: { [key: string]: string } = {
                          nomeCliente: "Cliente",
                          nomeMarca: "Marca",
                          telefone: "Telefone",
                          numeroProcesso: "Processo",
                        };

                        return (
                          <span
                            key={key}
                            className="inline-block text-xs px-2 py-1 rounded-full bg-fundoVigor text-azulDark"
                          >
                            {labels[key] || key}: {String(value)}
                          </span>
                        );
                      }
                    )}
                  </div>
                </div>
              )}
            </div>

            {/* Lista de resultados */}
            <div className="space-y-6">
              {buscaLoading ? (
                // Mostrar skeletons durante o carregamento
                Array.from({ length: pageSize }, (_, index) => (
                  <ProcessoCardSkeleton key={`skeleton-${index}`} />
                ))
              ) : (
                // Mostrar processos com informações do cliente
                resultados.processos.map((processo) => (
                  <ClienteInfoBlock 
                    key={processo.id}
                    cliente={processo.Cliente}
                    autoLoginUrl={processo.Cliente?.autoLoginUrl}
                    identificador={processo.Cliente?.identificador}
                    processo={processo}
                    estimativasMerito={estimativasMerito}
                  />
                ))
              )}
            </div>

            {/* Paginação */}
            {resultados.totalPages > 1 && (
              <div className="bg-branco p-4 rounded-lg shadow-md">
                <div className="flex justify-between items-center">
                  <div className="text-sm text-cinzaTexto">
                    <span>Página </span>
                    <span className="font-medium">
                      {resultados.currentPage}
                    </span>
                    <span> de </span>
                    <span className="font-medium">{resultados.totalPages}</span>
                  </div>

                  <div className="flex gap-2">
                    <button
                      onClick={() => handleChangePage(1)}
                      disabled={!resultados.hasPreviousPage}
                      className="px-3 py-2 text-sm border border-cinzaClaro rounded-md disabled:bg-cinzaFaixa disabled:text-cinza disabled:cursor-not-allowed hover:bg-cinzaFaixa text-TextosEscurosEBg"
                    >
                      Primeira
                    </button>

                    <button
                      onClick={() =>
                        handleChangePage(resultados.currentPage - 1)
                      }
                      disabled={!resultados.hasPreviousPage}
                      className="px-3 py-2 text-sm border border-cinzaClaro rounded-md disabled:bg-cinzaFaixa disabled:text-cinza disabled:cursor-not-allowed hover:bg-cinzaFaixa text-TextosEscurosEBg"
                    >
                      ← Anterior
                    </button>

                    {/* Páginas próximas */}
                    {Array.from(
                      { length: Math.min(5, resultados.totalPages) },
                      (_, i) => {
                        const pageStart = Math.max(
                          1,
                          resultados.currentPage - 2
                        );
                        const pageNumber = pageStart + i;

                        if (pageNumber > resultados.totalPages) return null;

                        return (
                          <button
                            key={pageNumber}
                            onClick={() => handleChangePage(pageNumber)}
                            className={`px-3 py-2 text-sm border rounded-md ${
                              pageNumber === resultados.currentPage
                                ? "bg-azul text-branco border-azul"
                                : "border-cinzaClaro hover:bg-cinzaFaixa text-TextosEscurosEBg"
                            }`}
                          >
                            {pageNumber}
                          </button>
                        );
                      }
                    )}

                    <button
                      onClick={() =>
                        handleChangePage(resultados.currentPage + 1)
                      }
                      disabled={!resultados.hasNextPage}
                      className="px-3 py-2 text-sm border border-cinzaClaro rounded-md disabled:bg-cinzaFaixa disabled:text-cinza disabled:cursor-not-allowed hover:bg-cinzaFaixa text-TextosEscurosEBg"
                    >
                      Próxima →
                    </button>

                    <button
                      onClick={() => handleChangePage(resultados.totalPages)}
                      disabled={!resultados.hasNextPage}
                      className="px-3 py-2 text-sm border border-cinzaClaro rounded-md disabled:bg-cinzaFaixa disabled:text-cinza disabled:cursor-not-allowed hover:bg-cinzaFaixa text-TextosEscurosEBg"
                    >
                      Última
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Footer com informações */}
        {resultados && (
          <div className="mt-8 text-center text-sm text-cinzaTexto">
            <p>
              Última busca realizada em {new Date().toLocaleString("pt-BR")}
            </p>
            <p className="mt-1">
              ⚡ Sistema de busca integrado - Comercial & Processos
            </p>
        </div>
        )}
      </div>
    </div>
  );
} 