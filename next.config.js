
/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    // Warning: This allows production builds to successfully complete even if
    // your project has ESLint errors.
    ignoreDuringBuilds: true,
  },
  typescript: {
    // !! WARN !!
    // Dangerously allow production builds to successfully complete even if
    // your project has type errors.
    // !! WARN !!
    ignoreBuildErrors: true,
  },
  serverExternalPackages: [""],
  // ✅ Remover console.log em produção
  compiler: {
    removeConsole: process.env.NODE_ENV === "production",
    // Se quiser manter console.error e console.warn, use assim:
    // removeConsole: { exclude: ['error', 'warn'] },
  },
  // ✅ REMOVIDO: Rewrite que estava interferindo com shortcodes
  // async rewrites() {
  //   return [
  //     {
  //       source: '/:identificador',
  //       destination: '/',
  //     },
  //   ];
  // },
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "registre-sys.registrese.app.br",
        port: "",
        pathname: "/images/logos/processos/**",
      },
      {
        protocol: "https",
        hostname: "api-v3-rgsys.registrese.app.br",
        port: "",
        pathname: "/api/protocolo/logo/**",
      },
    ],
  },
};

module.exports = nextConfig; 