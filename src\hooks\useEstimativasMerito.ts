import { useState, useEffect } from 'react';

interface EstimativasMerito {
  mediaSemIntervencoes?: number | null;
  mediaComOposicao?: number | null;
  mediaComSobrestamento?: number | null;
  mediaComExigencia?: number | null;
}

export const useEstimativasMerito = () => {
  const [estimativas, setEstimativas] = useState<EstimativasMerito | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchEstimativas = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const response = await fetch('/api/estimativas/merito');
        
        if (!response.ok) {
          throw new Error(`Erro ao buscar estimativas: ${response.status}`);
        }
        
        const data = await response.json();
        setEstimativas(data);
      } catch (err) {
        console.warn('Erro ao buscar estimativas de mérito:', err);
        setError(err instanceof Error ? err.message : 'Erro desconhecido');
        setEstimativas(null); // Continua sem estimativas em caso de erro
      } finally {
        setLoading(false);
      }
    };

    fetchEstimativas();
  }, []);

  return { estimativas, loading, error };
}; 