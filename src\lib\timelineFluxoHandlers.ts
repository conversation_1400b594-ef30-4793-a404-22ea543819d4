import { Despacho_Interface } from "./appTypes";
import { EtapaDefinicao, definicoesEtapas } from "./etapasConfig";
import { ProcessoParaTimeline, EtapaTimelineGerada, ExigenciaInfo } from "./timelineNovasTypes";
import {
  encontrarDefinicaoEtapaPorId,
  encontrarDataPublicacaoMaisRecente,
  verificarOposicaoAposData,
  isDespachoDeConcessao,
  isDespachoDeArquivamentoFaltaPagamentoTaxa,
  isDespachoDeDeferimento,
  isDespachoDePagamentoTaxaOrdinaria,
  isDespachoDePagamentoTaxaExtraordinaria,
  isDespachoDeExigencia,
  isDespachoDeCumprimentoExigencia,
  isDespachoDeArquivamento,
  formatarData,
  isDespachoDeSobrestamento,
  calcularProximaTerca,
  encontrarDataNotificacaoOposicaoMaisRecente,
  encontrarDataProtocoloManifestacao,
  isDespachoDePublicacao,
  isDespachoDeIndeferimento,
  isDespachoDeApresentacaoRecursoIndeferimento,
  isDespachoDeRecursoProvido,
  isDespachoDeRecursoNaoProvido,
  isDespachoDeInstauracaoNulidade,
  isDespachoDeConcessaoMantidaPosNulidade,
  isDespachoDeRegistroAnulado,
  isDespachoDeManifestacaoNulidade,
  isDespachoDeRenovacaoPagaOrdinaria,
  isDespachoDeRenovacaoPagaExtraordinaria
} from "./timelineConfigUtil";

// Estado compartilhado entre os handlers e a função principal
export interface TimelineFluxoEstado {
  dataDeferimento: Date | null;
  taxaConcessaoPaga: 'ORDINARIA' | 'EXTRAORDINARIA' | null;
  dataPagamentoTaxa: Date | null;
  dataConcessao: Date | null;
  arquivadoPorFaltaPagamentoTaxa: Date | null;
  exigenciaAtiva: ExigenciaInfo | null;
  etapaAtualIdentificada: boolean; // Adicionado para controle de fluxo
  dataUltimaRenovacaoConcluida: Date | null; // NOVO CAMPO
}

// Resultado de uma função handler
export interface HandlerResultado {
  timelineAtualizada: EtapaTimelineGerada[];
  estadoAtualizado: TimelineFluxoEstado;
  finalizarProcessamento: boolean;
}

// Parâmetros comuns para a maioria dos handlers que operam em um despacho específico
export interface HandlerParams {
  despacho: Despacho_Interface;
  dataDespacho: Date;
  processo: ProcessoParaTimeline;
  despachosOrdenados: Despacho_Interface[]; // Todos os despachos, para lookups
  timelineAnterior: EtapaTimelineGerada[];
  estadoAnterior: TimelineFluxoEstado;
  temDataDeposito: boolean;
}

// Nova interface de parâmetros para handlers pós-loop
export interface PosLoopHandlerParams {
  processo: ProcessoParaTimeline;
  despachosOrdenados: Despacho_Interface[];
  timelineAnterior: EtapaTimelineGerada[];
  estadoAtual: TimelineFluxoEstado; // Usa o estado atual consolidado
  temDataDeposito: boolean;
  hoje: Date;
}

// Interface simplificada para as estimativas de mérito (pode ser movida se for mais global)
interface EstimativaMeritoSalva {
  mediaSemIntervencoes?: number | null;
  mediaComOposicao?: number | null;
  mediaComSobrestamento?: number | null;
  mediaComExigencia?: number | null;
}

// Parâmetros para o handler de fluxo padrão
export interface FluxoPadraoParams {
  processo: ProcessoParaTimeline;
  despachosOrdenados: Despacho_Interface[];
  timelineAnterior: EtapaTimelineGerada[];
  estadoAtual: TimelineFluxoEstado;
  temDataDeposito: boolean;
  hoje: Date;
  estimativasDb?: EstimativaMeritoSalva | null;
}

/**
 * Placeholder para funções handler.
 * Cada função será responsável por uma lógica específica como "tentarProcessarConcessao",
 * "tentarProcessarArquivamentoPorFaltaPagamentoTaxa", etc.
 */

// Exemplo de como uma função handler poderia ser (ainda não implementada):
/*
export function tentarProcessarConcessao(params: HandlerParams): HandlerResultado {
  // Lógica para verificar se é um despacho de concessão e se as condições são atendidas
  // ...
  if (condicoesAtendidas) {
    // construir timeline de concessão
    return {
      timelineAtualizada: novaTimeline,
      estadoAtualizado: { ...params.estadoAnterior, dataConcessao: params.dataDespacho, etapaAtualIdentificada: true },
      finalizarProcessamento: true,
    };
  }
  return { 
    timelineAtualizada: params.timelineAnterior, 
    estadoAtualizado: params.estadoAnterior, 
    finalizarProcessamento: false 
  };
}
*/
    
export function processarSobrestamentoHandler(params: HandlerParams): HandlerResultado {
  const { despacho, dataDespacho, processo, despachosOrdenados, timelineAnterior, estadoAnterior, temDataDeposito } = params;
  // Retorna o estadoAnterior por padrão se nenhuma modificação específica do handler for feita.
  // Isso é importante para que o estado.etapaAtualIdentificada não seja revertido para o estado inicial do fluxo (false)
  // se este handler não identificar uma nova etapa atual por si só.
  let novoEstadoParaRetornar = estadoAnterior; 

  if (isDespachoDeSobrestamento(despacho)) {
    const ultimoDespachoRelevante = despachosOrdenados[despachosOrdenados.length - 1];
    const dataUltimoDespachoRelevante = ultimoDespachoRelevante.RPI?.dataPublicacao ? new Date(ultimoDespachoRelevante.RPI.dataPublicacao) :
                                       (ultimoDespachoRelevante.ProtocoloDespacho?.[0]?.data ? new Date(ultimoDespachoRelevante.ProtocoloDespacho[0].data) : new Date(0));

    const definicaoSobrestamento = encontrarDefinicaoEtapaPorId('SOBRESTAMENTO_01');
    if (!definicaoSobrestamento) {
      // Definição não encontrada, retorna sem alterações no estado ou timeline.
      return { timelineAtualizada: timelineAnterior, estadoAtualizado: estadoAnterior, finalizarProcessamento: false };
    }

    if (dataDespacho.getTime() >= dataUltimoDespachoRelevante.getTime()) {
      // Sobrestamento é o último evento: limpar timeline e torná-lo ATUAL
      const timelineAtualizadaParaSobrestamentoFinal = timelineAnterior.filter(
        e => e.status === 'CONCLUIDA' && e.idOriginal !== 'SOBRESTAMENTO_01'
      );
      
      // Adicionar histórico básico (Protocolo, Publicação, etc.) - Lógica de reconstrução
      // Mantendo a lógica de reconstrução original do handler para este caso.
      if (temDataDeposito && processo.dataDeposito && !timelineAtualizadaParaSobrestamentoFinal.some(e => e.idOriginal === 'PROTOCOLO_01')) {
      const definicaoProtocolo = encontrarDefinicaoEtapaPorId('PROTOCOLO_01');
      if (definicaoProtocolo) {
          timelineAtualizadaParaSobrestamentoFinal.unshift({ 
            idOriginal: definicaoProtocolo.id, etapaDefinicao: definicaoProtocolo, 
            status: 'CONCLUIDA', dataInicio: new Date(processo.dataDeposito) 
        });
      }
    }
    const despachosAteEsteSobrestamento = despachosOrdenados.filter(d => {
        const dtDesp = d.RPI?.dataPublicacao ? new Date(d.RPI.dataPublicacao) : (d.ProtocoloDespacho?.[0]?.data ? new Date(d.ProtocoloDespacho[0].data) : null);
        return dtDesp ? dtDesp <= dataDespacho : false;
    });
      const dataPublicacaoAntesSobrestamento = encontrarDataPublicacaoMaisRecente(despachosAteEsteSobrestamento.filter(d => d !== despacho));
      if (dataPublicacaoAntesSobrestamento && !timelineAtualizadaParaSobrestamentoFinal.some(e => e.etapaDefinicao.nomeOriginal === 'Publicação')) {
      const houveOposicaoPublicacao = verificarOposicaoAposData(despachosAteEsteSobrestamento, dataPublicacaoAntesSobrestamento);
      const idDefPublicacao = houveOposicaoPublicacao ? 'PUBLICACAO_COM_OPOSICAO_01' : 'PUBLICACAO_01';
      const defPublicacao = encontrarDefinicaoEtapaPorId(idDefPublicacao);
      if (defPublicacao) {
          const protocolIndex = timelineAtualizadaParaSobrestamentoFinal.findIndex(e => e.idOriginal === 'PROTOCOLO_01');
          timelineAtualizadaParaSobrestamentoFinal.splice(protocolIndex !== -1 ? protocolIndex + 1 : timelineAtualizadaParaSobrestamentoFinal.length, 0, { 
            idOriginal: defPublicacao.id, etapaDefinicao: defPublicacao, 
            status: 'CONCLUIDA', dataInicio: dataPublicacaoAntesSobrestamento 
          });
        const dataFimPrazoOposicao = new Date(dataPublicacaoAntesSobrestamento);
        dataFimPrazoOposicao.setDate(dataFimPrazoOposicao.getDate() + 60);
        const idDefPrazoOposicao = houveOposicaoPublicacao ? 'PRAZO_OPOSICAO_COM_OPOSICAO_01' : 'PRAZO_OPOSICAO_SEM_OPOSICAO_01';
        const defPrazoOposicao = encontrarDefinicaoEtapaPorId(idDefPrazoOposicao);
        if (defPrazoOposicao) {
             const publicacaoIdx = timelineAtualizadaParaSobrestamentoFinal.findIndex(e => e.idOriginal === idDefPublicacao); 
             timelineAtualizadaParaSobrestamentoFinal.splice(publicacaoIdx !== -1 ? publicacaoIdx + 1 : timelineAtualizadaParaSobrestamentoFinal.length, 0, {
            idOriginal: defPrazoOposicao.id, etapaDefinicao: defPrazoOposicao, status: 'CONCLUIDA',
            dataInicio: dataPublicacaoAntesSobrestamento, dataFim: dataFimPrazoOposicao
          });
        }
      }
    }
      // Fim da lógica de reconstrução de histórico.

      timelineAtualizadaParaSobrestamentoFinal.push({
          idOriginal: definicaoSobrestamento.id, etapaDefinicao: definicaoSobrestamento,
          status: 'ATUAL', dataInicio: dataDespacho,
        exigenciaInfo: estadoAnterior.exigenciaAtiva, // Usa o estado da exigência do estadoAnterior
      });
      
      timelineAtualizadaParaSobrestamentoFinal.sort((a,b)=>(a.dataInicio?.getTime() || 0) - (b.dataInicio?.getTime() || 0));
      
      // Modifica o novoEstado local para refletir que uma etapa ATUAL foi identificada.
      novoEstadoParaRetornar = { ...estadoAnterior, etapaAtualIdentificada: true, exigenciaAtiva: estadoAnterior.exigenciaAtiva };
      return { timelineAtualizada: timelineAtualizadaParaSobrestamentoFinal, estadoAtualizado: novoEstadoParaRetornar, finalizarProcessamento: true };

      } else {
      // Sobrestamento NÃO é o último evento:
      // Adiciona este sobrestamento como um evento CONCLUIDO.
      // Não limpa a etapa ATUAL da timelineAnterior nem modifica estadoAnterior.etapaAtualIdentificada.
      let timelineModificada = [...timelineAnterior];
      
      // Remove sobrestamentos CONCLUIDOS anteriores para não duplicar, mas mantém outras etapas.
      timelineModificada = timelineModificada.filter(e => e.idOriginal !== 'SOBRESTAMENTO_01' || e.status !== 'CONCLUIDA');

      timelineModificada.push({
          idOriginal: definicaoSobrestamento.id, etapaDefinicao: definicaoSobrestamento,
          status: 'CONCLUIDA', dataInicio: dataDespacho, dataFim: dataDespacho, 
        // Preserva o estado da exigência como estava no estadoAnterior, pois o sobrestamento não a resolve.
        exigenciaInfo: estadoAnterior.exigenciaAtiva, 
      });
      
      timelineModificada.sort((a,b) => (a.dataInicio?.getTime() || 0) - (b.dataInicio?.getTime() || 0));
      
      // Retorna o estadoAnterior INALTERADO (incluindo etapaAtualIdentificada)
      // porque este sobrestamento não define a etapa ATUAL final do processo.
        return {
        timelineAtualizada: timelineModificada,
        estadoAtualizado: estadoAnterior, 
          finalizarProcessamento: false,
        };
    }
  }

  // Não é um despacho de sobrestamento, retorna o estado e timeline originais.
  return { timelineAtualizada: timelineAnterior, estadoAtualizado: estadoAnterior, finalizarProcessamento: false };
}

export function processarConcessaoRegistroHandler(params: HandlerParams): HandlerResultado {
  const { despacho, dataDespacho, timelineAnterior, estadoAnterior } = params;
  const novoEstado = { ...estadoAnterior };
  let novaTimeline = [...timelineAnterior];

  if (
    novoEstado.dataDeferimento &&
    novoEstado.taxaConcessaoPaga &&
    !novoEstado.dataConcessao && 
    !novoEstado.arquivadoPorFaltaPagamentoTaxa && 
    isDespachoDeConcessao(despacho) 
  ) {
    // Concluir a etapa de Prazo de Pagamento que estava ATUAL
    const prazoPagamentoOrdIndex = novaTimeline.findIndex(
      e => e.idOriginal === 'PRAZO_PAGAMENTO_TAXA_CONCESSAO_ORDINARIO_01' && e.status === 'ATUAL'
    );
    if (prazoPagamentoOrdIndex !== -1) {
      novaTimeline[prazoPagamentoOrdIndex] = {
        ...novaTimeline[prazoPagamentoOrdIndex],
        status: 'CONCLUIDA',
        dataFim: novoEstado.dataPagamentoTaxa || dataDespacho, // Usa data do pagamento se disponível, senão data da concessão
        observacaoGerada: (novaTimeline[prazoPagamentoOrdIndex].observacaoGerada || "") + " Concluído pela concessão do registro."
      };
    }
    const prazoPagamentoExtraIndex = novaTimeline.findIndex(
      e => e.idOriginal === 'PRAZO_PAGAMENTO_TAXA_CONCESSAO_EXTRAORDINARIO_01' && e.status === 'ATUAL'
    );
    if (prazoPagamentoExtraIndex !== -1) {
      novaTimeline[prazoPagamentoExtraIndex] = {
        ...novaTimeline[prazoPagamentoExtraIndex],
        status: 'CONCLUIDA',
        dataFim: novoEstado.dataPagamentoTaxa || dataDespacho,
        observacaoGerada: (novaTimeline[prazoPagamentoExtraIndex].observacaoGerada || "") + " Concluído pela concessão do registro."
      };
    }

    // Adicionar TAXA_CONCESSAO_PAGA_01 como CONCLUIDA
    const defTaxaPaga = encontrarDefinicaoEtapaPorId('TAXA_CONCESSAO_PAGA_01');
    if (defTaxaPaga) {
      // Remover qualquer ocorrência anterior para evitar duplicatas
      novaTimeline = novaTimeline.filter(e => e.idOriginal !== defTaxaPaga.id);
      novaTimeline.push({
        idOriginal: defTaxaPaga.id,
        etapaDefinicao: defTaxaPaga,
        status: 'CONCLUIDA',
        dataInicio: novoEstado.dataPagamentoTaxa || novoEstado.dataDeferimento, // Data do pagamento ou deferimento como fallback para início
        dataFim: novoEstado.dataPagamentoTaxa || dataDespacho      // Data do pagamento ou concessão como fallback para fim
      });
    }

    // Marcar a CONCESSAO_REGISTRO_01 como CONCLUIDA
    const defConcessao = encontrarDefinicaoEtapaPorId('CONCESSAO_REGISTRO_01');
    if (defConcessao) {
      // Remove qualquer ocorrência anterior para garantir que não haja duplicatas ou status incorretos
      novaTimeline = novaTimeline.filter(e => e.idOriginal !== defConcessao.id);
        novaTimeline.push({
        idOriginal: defConcessao.id, 
        etapaDefinicao: defConcessao, 
        status: 'CONCLUIDA', 
        dataInicio: dataDespacho, 
        dataFim: dataDespacho 
      });
    }
    novoEstado.dataConcessao = dataDespacho;

    // Adicionar FIM_PRAZO_NULIDADE_01 como PROXIMA_EXATA
    const defFimPrazoNulidade = encontrarDefinicaoEtapaPorId('FIM_PRAZO_NULIDADE_01');
    if (defFimPrazoNulidade) {
      const dataFimPrazoNulidade = new Date(dataDespacho);
      dataFimPrazoNulidade.setDate(dataFimPrazoNulidade.getDate() + 180);
      // Remove qualquer ocorrência anterior para evitar duplicatas
      novaTimeline = novaTimeline.filter(e => e.idOriginal !== defFimPrazoNulidade.id);
                    novaTimeline.push({
        idOriginal: defFimPrazoNulidade.id,
        etapaDefinicao: defFimPrazoNulidade,
        status: 'PROXIMA_EXATA',
        dataInicio: dataFimPrazoNulidade,
        dataFim: dataFimPrazoNulidade,
        observacaoGerada: `Prazo para nulidade até ${formatarData(dataFimPrazoNulidade)}.`
      });
    }

    // Adicionar RENOVACAO_01 como próxima estimada
    const defRenovacao = encontrarDefinicaoEtapaPorId('RENOVACAO_01');
    if (defRenovacao && novoEstado.dataConcessao) {
      const dataInicioProximaJanela = new Date(novoEstado.dataConcessao);
      // Para a primeira concessão, a próxima renovação é para o segundo decênio
      dataInicioProximaJanela.setFullYear(dataInicioProximaJanela.getFullYear() + 10);

      const observacaoRenovacao = `Próxima janela de renovação. Registro válido até ${formatarData(dataInicioProximaJanela)}, calculado a partir da data de concessão (${formatarData(novoEstado.dataConcessao)}).`;

      // Remover qualquer RENOVACAO_01 PROXIMA_ESTIMADA existente (embora não deva haver neste ponto)
      novaTimeline = novaTimeline.filter(e => !(e.idOriginal === 'RENOVACAO_01' && e.status === 'PROXIMA_ESTIMADA'));

      novaTimeline.push({
        idOriginal: defRenovacao.id,
        etapaDefinicao: defRenovacao,
        status: 'PROXIMA_ESTIMADA',
        dataInicio: dataInicioProximaJanela,
        observacaoGerada: observacaoRenovacao
      });
    }
    
    novoEstado.exigenciaAtiva = null;
    novoEstado.etapaAtualIdentificada = false; // FIM_PRAZO_NULIDADE_01 é PROXIMA_EXATA, não ATUAL
    
    // Assegurar que a timeline esteja ordenada por data de início após as modificações
    novaTimeline.sort((a,b)=>(a.dataInicio?.getTime() || 0) - (b.dataInicio?.getTime() || 0));
    
    return {
      timelineAtualizada: novaTimeline,
      estadoAtualizado: novoEstado,
      finalizarProcessamento: false, // Alterado para false para permitir processamento de despachos subsequentes
    };
  }

  return { 
    timelineAtualizada: timelineAnterior, // Importante retornar a timelineAnterior se o if não for satisfeito
    estadoAtualizado: novoEstado, // Retorna novoEstado (que pode ser igual ao estadoAnterior se não entrou no if)
    finalizarProcessamento: false 
  };
}

export function processarArquivamentoFaltaPagamentoHandler(params: HandlerParams): HandlerResultado {
  const { despacho, dataDespacho, processo, despachosOrdenados, timelineAnterior, estadoAnterior, temDataDeposito } = params;

  if (
    estadoAnterior.dataDeferimento &&
    !estadoAnterior.taxaConcessaoPaga && 
    !estadoAnterior.dataConcessao && 
    !estadoAnterior.arquivadoPorFaltaPagamentoTaxa && 
    isDespachoDeArquivamentoFaltaPagamentoTaxa(despacho) 
  ) {
    const novaTimeline: EtapaTimelineGerada[] = [];

    // Preservar etapa de Sobrestamento Concluído da timelineAnterior
    const sobrestamentoConcluidoAnterior = timelineAnterior.find(
        e => e.idOriginal === 'SOBRESTAMENTO_01' && e.status === 'CONCLUIDA'
    );
    if (sobrestamentoConcluidoAnterior) {
        novaTimeline.push(sobrestamentoConcluidoAnterior);
    }

    if (temDataDeposito && processo.dataDeposito) {
      const defProto = encontrarDefinicaoEtapaPorId('PROTOCOLO_01');
      if (defProto) novaTimeline.push({ idOriginal: defProto.id, etapaDefinicao: defProto, status: 'CONCLUIDA', dataInicio: new Date(processo.dataDeposito) });
    }
    const dataPubArq = encontrarDataPublicacaoMaisRecente(despachosOrdenados.filter(d => d.RPI?.dataPublicacao && new Date(d.RPI.dataPublicacao) < dataDespacho));
    if (dataPubArq) {
      const houveOposicao = verificarOposicaoAposData(despachosOrdenados, dataPubArq);
      const idDefPub = houveOposicao ? 'PUBLICACAO_COM_OPOSICAO_01' : 'PUBLICACAO_01';
      const defPub = encontrarDefinicaoEtapaPorId(idDefPub);
      if (defPub) novaTimeline.push({idOriginal: defPub.id, etapaDefinicao: defPub, status: 'CONCLUIDA', dataInicio: dataPubArq});
    }
    const defDefer = encontrarDefinicaoEtapaPorId('DEFERIMENTO_PEDIDO_01');
    if (defDefer && estadoAnterior.dataDeferimento) {
      novaTimeline.push({idOriginal: defDefer.id, etapaDefinicao: defDefer, status: 'CONCLUIDA', dataInicio: estadoAnterior.dataDeferimento});
    }
    
    const dataFimPrazoOrdArq = new Date(estadoAnterior.dataDeferimento!);
    dataFimPrazoOrdArq.setDate(dataFimPrazoOrdArq.getDate() + 60);
    const defPrazoOrdArq = encontrarDefinicaoEtapaPorId('PRAZO_PAGAMENTO_TAXA_CONCESSAO_ORDINARIO_01');
    if (defPrazoOrdArq && estadoAnterior.dataDeferimento) {
         novaTimeline.push({idOriginal: defPrazoOrdArq.id, etapaDefinicao: defPrazoOrdArq, status: 'CONCLUIDA', dataInicio: estadoAnterior.dataDeferimento, dataFim: dataFimPrazoOrdArq});
    }
    
    const dataInicioPrazoExtraArq = dataFimPrazoOrdArq;
    if (dataDespacho >= dataInicioPrazoExtraArq) { 
      const defPrazoExtraArq = encontrarDefinicaoEtapaPorId('PRAZO_PAGAMENTO_TAXA_CONCESSAO_EXTRAORDINARIO_01');
      const dataFimPrazoExtraArq = new Date(dataInicioPrazoExtraArq);
      dataFimPrazoExtraArq.setDate(dataFimPrazoExtraArq.getDate() + 30);
      if (defPrazoExtraArq) novaTimeline.push({idOriginal: defPrazoExtraArq.id, etapaDefinicao: defPrazoExtraArq, status: 'CONCLUIDA', dataInicio: dataInicioPrazoExtraArq, dataFim: dataFimPrazoExtraArq});
    }

    const defArqTaxa = encontrarDefinicaoEtapaPorId('ARQUIVAMENTO_FALTA_PAGAMENTO_TAXA_01');
    if (defArqTaxa) novaTimeline.push({idOriginal: defArqTaxa.id, etapaDefinicao: defArqTaxa, status: 'ATUAL', dataInicio: dataDespacho, exigenciaInfo: null});
    
    return {
      timelineAtualizada: novaTimeline,
      estadoAtualizado: { ...estadoAnterior, arquivadoPorFaltaPagamentoTaxa: dataDespacho, exigenciaAtiva: null, etapaAtualIdentificada: true },
      finalizarProcessamento: true,
    };
  }

  return { 
    timelineAtualizada: timelineAnterior, 
    estadoAtualizado: estadoAnterior, 
    finalizarProcessamento: false 
  };
}

export function atualizarEstadoDeferimentoPagamentoHandler(params: HandlerParams): HandlerResultado {
  const { despacho, dataDespacho, estadoAnterior, despachosOrdenados, processo } = params;
  const novoEstado = { ...estadoAnterior };
  const novaTimeline = [...params.timelineAnterior]; // Criar cópia da timeline no início

  if (!novoEstado.dataConcessao && !novoEstado.arquivadoPorFaltaPagamentoTaxa) {
    // 1. Identifica o deferimento
    if (!novoEstado.dataDeferimento && isDespachoDeDeferimento(despacho)) {
      novoEstado.dataDeferimento = dataDespacho;
      novoEstado.exigenciaAtiva = null; // Deferimento do pedido principal resolve/supera exigências pendentes no estado.

      // Marcar a etapa de Exigência Ativa na timeline como CONCLUIDA
      const exigenciaAtivaIndex = novaTimeline.findIndex(
        e => e.idOriginal === 'INDICADOR_EXIGENCIA_ATIVA_01' && e.status === 'ATUAL'
      );
      if (exigenciaAtivaIndex !== -1) {
        novaTimeline[exigenciaAtivaIndex] = {
          ...novaTimeline[exigenciaAtivaIndex],
          status: 'CONCLUIDA',
          dataFim: dataDespacho, // Concluída na data do deferimento
          observacaoGerada: (novaTimeline[exigenciaAtivaIndex].observacaoGerada || '') + 
                            " Exigência superada pelo deferimento do pedido."
        };
        // Não precisa de return aqui, a timeline modificada será retornada no final do handler
      }
    }

    // 2. Identifica pagamento explícito da taxa de concessão (APÓS o deferimento)
    // Só processa pagamento se já houve deferimento e a taxa ainda não foi marcada como paga.
    if (novoEstado.dataDeferimento && !novoEstado.taxaConcessaoPaga && dataDespacho >= novoEstado.dataDeferimento) {
      if (isDespachoDePagamentoTaxaOrdinaria(despacho)) {
        novoEstado.taxaConcessaoPaga = 'ORDINARIA';
        novoEstado.dataPagamentoTaxa = dataDespacho;
      } else if (isDespachoDePagamentoTaxaExtraordinaria(despacho)) {
        novoEstado.taxaConcessaoPaga = 'EXTRAORDINARIA';
        novoEstado.dataPagamentoTaxa = dataDespacho;
      }
    }

    // 3. Inferência de pagamento pela Concessão
    // Se o despacho atual é de Concessão e já houve Deferimento, mas a taxa não foi marcada como paga explicitamente ANTES desta concessão.
    if (
      isDespachoDeConcessao(despacho) &&
      novoEstado.dataDeferimento && // Garante que o deferimento já ocorreu
      dataDespacho >= novoEstado.dataDeferimento && // Garante que a concessão não é antes do deferimento
      !novoEstado.taxaConcessaoPaga // Apenas se não identificamos um pagamento explícito *antes*
    ) {
      // Verifica se houve um despacho de pagamento explícito *antes* da data desta concessão
      const pagamentoExplicitoAntesDaConcessao = despachosOrdenados.find(d => {
        const dtPagamento = d.RPI?.dataPublicacao ? new Date(d.RPI.dataPublicacao) : 
                             (d.ProtocoloDespacho && d.ProtocoloDespacho.length > 0 && d.ProtocoloDespacho[0].data ? new Date(d.ProtocoloDespacho[0].data) : null);
        if (!dtPagamento || !novoEstado.dataDeferimento || dtPagamento < novoEstado.dataDeferimento || dtPagamento >= dataDespacho) {
          return false; // Data inválida, antes do deferimento ou na/após a concessão atual
        }
        return isDespachoDePagamentoTaxaOrdinaria(d) || isDespachoDePagamentoTaxaExtraordinaria(d);
      });

      if (!pagamentoExplicitoAntesDaConcessao) {
        novoEstado.taxaConcessaoPaga = 'ORDINARIA'; // Inferencia padrão
      }
    }
  }
  // Retorna sempre a novaTimeline e novoEstado, mesmo que não tenham sido alterados dentro de todos os ifs.
  return {
    timelineAtualizada: novaTimeline, 
    estadoAtualizado: novoEstado,
    finalizarProcessamento: false, 
  };
}

export function gerenciarExigenciasHandler(params: HandlerParams): HandlerResultado {
  const { despacho, dataDespacho, timelineAnterior, estadoAnterior, processo } = params;
  const novoEstado = { ...estadoAnterior };
  let novaTimeline = [...timelineAnterior];

  const sobrestamentoAtualNaTimeline = novaTimeline.find(e => e.idOriginal === 'SOBRESTAMENTO_01' && e.status === 'ATUAL');

  // Nova lógica: Se o despacho atual é um Deferimento do Pedido e uma exigência estava ativa,
  // considerar a exigência superada/concluída por este deferimento.
  if (isDespachoDeDeferimento(despacho) && novoEstado.exigenciaAtiva && dataDespacho >= novoEstado.exigenciaAtiva.dataDespachoExigencia) {
    const exigenciaAtivaIndex = novaTimeline.findIndex(
      e => e.idOriginal === 'INDICADOR_EXIGENCIA_ATIVA_01' && (e.status === 'ATUAL' || e.status === 'ALERTA_ACAO')
    );
    if (exigenciaAtivaIndex !== -1) {
      novaTimeline[exigenciaAtivaIndex] = {
        ...novaTimeline[exigenciaAtivaIndex],
        status: 'CONCLUIDA',
        dataFim: dataDespacho, // Concluída na data do deferimento
        observacaoGerada: (novaTimeline[exigenciaAtivaIndex].observacaoGerada || '') +
                          " Exigência superada pelo deferimento do pedido."
      };
    }
    novoEstado.exigenciaAtiva = null;
    // Não setar etapaAtualIdentificada = true aqui, pois o deferimento em si será tratado por outro handler
    // e definirá a próxima etapa (prazo de pagamento). Se setarmos como true, pode impedir
    // que os handlers pós-loop de pagamento funcionem corretamente.
    // A ideia é que este handler APENAS gerencia o estado da exigência.
    // O `atualizarEstadoDeferimentoPagamentoHandler` irá setar o `dataDeferimento` no estado.
  } else if (isDespachoDeCumprimentoExigencia(despacho, novoEstado.exigenciaAtiva)) {
    // Lógica existente para cumprimento explícito de exigência
    if (novoEstado.exigenciaAtiva && dataDespacho >= novoEstado.exigenciaAtiva.dataDespachoExigencia) {
      // Verifica se é uma exigência pós-concessão e se os códigos de serviço do protocolo batem
      if (novoEstado.dataConcessao && 
          novoEstado.exigenciaAtiva.codigoServicoProtocoloOriginador &&
          despacho.ProtocoloDespacho?.some(p => p.codigoServico === novoEstado.exigenciaAtiva!.codigoServicoProtocoloOriginador)) {
        
        // Concluir o ALERTA_ACAO da exigência ativa
        const alertaExigenciaIndex = novaTimeline.findIndex(e => 
          e.idOriginal === 'INDICADOR_EXIGENCIA_ATIVA_01' && e.status === 'ALERTA_ACAO'
        );
        if (alertaExigenciaIndex !== -1) {
          novaTimeline[alertaExigenciaIndex] = {
            ...novaTimeline[alertaExigenciaIndex],
            status: 'CONCLUIDA',
            dataFim: dataDespacho,
            observacaoGerada: (novaTimeline[alertaExigenciaIndex].observacaoGerada || '') + ` Cumprida em ${formatarData(dataDespacho)}.`,
          };
        }

        // Adicionar a nova etapa EXIGENCIA_PETICAO_CUMPRIDA_01
        const defExigCumpridaPosConcessao = encontrarDefinicaoEtapaPorId('EXIGENCIA_PETICAO_CUMPRIDA_01');
        if (defExigCumpridaPosConcessao) {
          novaTimeline = novaTimeline.filter(e => e.idOriginal !== defExigCumpridaPosConcessao.id); // Evitar duplicatas
          novaTimeline.push({
            idOriginal: defExigCumpridaPosConcessao.id,
            etapaDefinicao: defExigCumpridaPosConcessao,
            status: 'CONCLUIDA',
            dataInicio: dataDespacho,
            dataFim: dataDespacho,
          });
        }
        novoEstado.exigenciaAtiva = null; // Limpa a exigência do estado
        novoEstado.etapaAtualIdentificada = false; // Não muda a etapa ATUAL principal

      } else if (!novoEstado.dataConcessao) { // Exigência ANTES da concessão (lógica original)
        const exigenciaAtivaIndex = novaTimeline.findIndex(e => 
          (e.idOriginal === 'INDICADOR_EXIGENCIA_ATIVA_01' && e.status === 'ATUAL') ||
          (e.idOriginal === 'INDICADOR_EXIGENCIA_ATIVA_01' && e.status === 'ALERTA_ACAO') // Caso raro, mas tratar
        );
        if (exigenciaAtivaIndex !== -1) {
          novaTimeline[exigenciaAtivaIndex] = {
            ...novaTimeline[exigenciaAtivaIndex],
            status: 'CONCLUIDA',
            dataFim: dataDespacho,
            observacaoGerada: `Exigência cumprida em ${formatarData(dataDespacho)}.`,
          };
        }
      novoEstado.exigenciaAtiva = null;
        novoEstado.etapaAtualIdentificada = false; 
      }
      // Se estava em sobrestamento e a exigência foi resolvida, o sobrestamento pode voltar a ser ATUAL
      if (sobrestamentoAtualNaTimeline && sobrestamentoAtualNaTimeline.exigenciaInfo && !novoEstado.exigenciaAtiva) {
        const sobrestamentoIndex = novaTimeline.findIndex(e => e.idOriginal === 'SOBRESTAMENTO_01' && e.status === 'ATUAL');
        if (sobrestamentoIndex !== -1) {
            novaTimeline[sobrestamentoIndex] = { ...novaTimeline[sobrestamentoIndex], exigenciaInfo: null };
            novoEstado.etapaAtualIdentificada = true; 
        }
      }
    }
  } else if (despacho.codigo?.trim() === "120.3" && novoEstado.exigenciaAtiva && dataDespacho >= novoEstado.exigenciaAtiva.dataDespachoExigencia) {
    // Este é o despacho "Indeferimento da petição (exigência não cumprida)"
    
    // Verifica se é uma exigência pós-concessão e se os códigos de serviço do protocolo batem
    if (novoEstado.dataConcessao &&
        novoEstado.exigenciaAtiva.codigoServicoProtocoloOriginador &&
        despacho.ProtocoloDespacho?.some(p => p.codigoServico === novoEstado.exigenciaAtiva!.codigoServicoProtocoloOriginador)) {

      // Concluir o ALERTA_ACAO da exigência ativa
      const alertaExigenciaIndex = novaTimeline.findIndex(
        e => e.idOriginal === 'INDICADOR_EXIGENCIA_ATIVA_01' && e.status === 'ALERTA_ACAO'
      );
      if (alertaExigenciaIndex !== -1) {
        novaTimeline[alertaExigenciaIndex] = {
          ...novaTimeline[alertaExigenciaIndex],
          status: 'CONCLUIDA', 
          dataFim: dataDespacho, 
          observacaoGerada: (novaTimeline[alertaExigenciaIndex].observacaoGerada || '') +
                            ` Petição referente à exigência foi indeferida em ${formatarData(dataDespacho)}.`,
        };
      }

      // Adicionar a nova etapa EXIGENCIA_PETICAO_NAO_CUMPRIDA_01
      const defExigNaoCumpridaPosConcessao = encontrarDefinicaoEtapaPorId('EXIGENCIA_PETICAO_NAO_CUMPRIDA_01');
      if (defExigNaoCumpridaPosConcessao) {
        novaTimeline = novaTimeline.filter(e => e.idOriginal !== defExigNaoCumpridaPosConcessao.id); // Evitar duplicatas
        novaTimeline.push({
          idOriginal: defExigNaoCumpridaPosConcessao.id,
          etapaDefinicao: defExigNaoCumpridaPosConcessao,
          status: 'CONCLUIDA',
          dataInicio: dataDespacho,
          dataFim: dataDespacho,
        });
      }
      novoEstado.exigenciaAtiva = {
        ...(novoEstado.exigenciaAtiva as ExigenciaInfo), 
        cumprida: false,
        dataNaoCumprimento: dataDespacho,
      };
      // Não limpamos exigenciaAtiva totalmente aqui para o caso de um deferimento geral do processo ocorrer e precisar saber desse estado.
      // No entanto, para o fluxo principal da timeline pós-concessão, isso não muda a etapa ATUAL.
      novoEstado.etapaAtualIdentificada = false; 

    } else if (!novoEstado.dataConcessao) { // Exigência ANTES da concessão (lógica original para 120.3)
      const exigenciaAtivaIndex = novaTimeline.findIndex(
        e => e.idOriginal === 'INDICADOR_EXIGENCIA_ATIVA_01' && (e.status === 'ATUAL' || e.status === 'ALERTA_ACAO')
      );
      if (exigenciaAtivaIndex !== -1) {
        novaTimeline[exigenciaAtivaIndex] = {
          ...novaTimeline[exigenciaAtivaIndex],
          status: 'CONCLUIDA', 
          dataFim: dataDespacho, 
          observacaoGerada: (novaTimeline[exigenciaAtivaIndex].observacaoGerada || '') +
                            ` Petição referente à exigência foi indeferida em ${formatarData(dataDespacho)}. Exigência considerada não cumprida.`,
        };
      }
      novoEstado.exigenciaAtiva = {
        ...(novoEstado.exigenciaAtiva as ExigenciaInfo), 
        cumprida: false,
        dataNaoCumprimento: dataDespacho,
      };
      novoEstado.etapaAtualIdentificada = true; // No fluxo pré-concessão, isso pode definir uma etapa.
    }
  } else if (isDespachoDeExigencia(despacho)) {
    // Lógica existente para uma nova exigência
    const dataLimite = new Date(dataDespacho);
    dataLimite.setDate(dataLimite.getDate() + 60);
    
    let codigoProtocoloExigencia: string | undefined = undefined;
    if (despacho.ProtocoloDespacho && despacho.ProtocoloDespacho.length > 0) {
        codigoProtocoloExigencia = despacho.ProtocoloDespacho[0].codigoServico;
    }

    novoEstado.exigenciaAtiva = { 
        dataDespachoExigencia: dataDespacho, 
        dataLimiteCumprimento: dataLimite,
        codigoServicoProtocoloOriginador: codigoProtocoloExigencia 
    };

    if (sobrestamentoAtualNaTimeline) {
      const sobrestamentoIndex = novaTimeline.findIndex(e => e.idOriginal === 'SOBRESTAMENTO_01' && e.status === 'ATUAL');
      if (sobrestamentoIndex !== -1) {
        novaTimeline[sobrestamentoIndex] = { ...novaTimeline[sobrestamentoIndex], exigenciaInfo: novoEstado.exigenciaAtiva };
        novoEstado.etapaAtualIdentificada = true;
      }
    } else if (novoEstado.dataConcessao) {
        const defExigencia = encontrarDefinicaoEtapaPorId('INDICADOR_EXIGENCIA_ATIVA_01');
        if (defExigencia) {
            novaTimeline = novaTimeline.filter(e => e.idOriginal !== 'INDICADOR_EXIGENCIA_ATIVA_01');
            novaTimeline.push({
                idOriginal: defExigencia.id,
                etapaDefinicao: defExigencia,
                status: 'ALERTA_ACAO',
                dataInicio: dataDespacho,
                dataFim: dataLimite,
                exigenciaInfo: novoEstado.exigenciaAtiva,
                observacaoGerada: `Exigência (em petição) publicada em ${formatarData(dataDespacho)}. Prazo até ${formatarData(dataLimite)}.`
            });
        }
    } else {
      const etapaAtualAnteriorIndex = novaTimeline.findIndex(e => e.status === 'ATUAL' && e.idOriginal !== 'INDICADOR_EXIGENCIA_ATIVA_01');
      if (etapaAtualAnteriorIndex !== -1) {
        novaTimeline[etapaAtualAnteriorIndex] = {
          ...novaTimeline[etapaAtualAnteriorIndex],
          status: 'CONCLUIDA',
          dataFim: dataDespacho,
          observacaoGerada: (novaTimeline[etapaAtualAnteriorIndex].observacaoGerada || "") + " Interrompida por exigência."
        };
      }

      const defExigencia = encontrarDefinicaoEtapaPorId('INDICADOR_EXIGENCIA_ATIVA_01');
      if (defExigencia) {
        novaTimeline = novaTimeline.filter(e => e.idOriginal !== 'INDICADOR_EXIGENCIA_ATIVA_01');
        novaTimeline.push({
          idOriginal: defExigencia.id,
          etapaDefinicao: defExigencia,
          status: 'ATUAL',
          dataInicio: dataDespacho,
          dataFim: dataLimite,
          exigenciaInfo: novoEstado.exigenciaAtiva,
          observacaoGerada: `Exigência publicada em ${formatarData(dataDespacho)}. Prazo para cumprimento até ${formatarData(dataLimite)}.`
        });
        // NÃO definir etapaAtualIdentificada = true aqui para permitir que o fluxo padrão
        // adicione publicação e análise de mérito com exigência
        // novoEstado.etapaAtualIdentificada = true;
      }
    }
  }
  // Se um sobrestamento estava ATUAL e tinha uma exigência, e essa exigência foi resolvida (novoEstado.exigenciaAtiva é null agora),
  // mas o sobrestamento em si continua, então etapaAtualIdentificada deve ser true para manter o sobrestamento como ATUAL.
  // A lógica de `finalizarProcessamento` considera isso.
  const finalizar = novoEstado.etapaAtualIdentificada && !!sobrestamentoAtualNaTimeline && !novoEstado.exigenciaAtiva;


  return { 
    timelineAtualizada: novaTimeline, 
    estadoAtualizado: novoEstado, 
    // Finaliza o processamento do loop de despachos apenas se a exigência ocorreu DURANTE um sobrestamento e FOI RESOLVIDA.
    // Se uma nova exigência define a etapa atual (e não está em sobrestamento), ou se um deferimento resolveu uma exigência
    // (e não estava em sobrestamento), o loop deve continuar para processar o deferimento/concessão etc.
    finalizarProcessamento: finalizar 
  };
}

export function processarArquivamentoGeralHandler(params: HandlerParams): HandlerResultado {
  const { despacho, dataDespacho, processo, despachosOrdenados, timelineAnterior, estadoAnterior, temDataDeposito } = params;
  const novoEstado = { ...estadoAnterior };
  let novaTimeline = [...timelineAnterior]; 
  const arquivamentoInfo = isDespachoDeArquivamento(despacho); // Agora retorna ResultadoIsArquivamento

  if (arquivamentoInfo.eArquivamento) {
    const sobrestamentoConcluidoAnterior = timelineAnterior.find(
        e => e.idOriginal === 'SOBRESTAMENTO_01' && e.status === 'CONCLUIDA'
    );

    novaTimeline = novaTimeline.filter(e => e.status === 'CONCLUIDA');
    
    if (sobrestamentoConcluidoAnterior && !novaTimeline.some(e => e.idOriginal === 'SOBRESTAMENTO_01')) {
        novaTimeline.push(sobrestamentoConcluidoAnterior); 
    }

    if (temDataDeposito && !novaTimeline.find(e => e.idOriginal === 'PROTOCOLO_01') && processo.dataDeposito) {
      const definicaoProtocolo = encontrarDefinicaoEtapaPorId('PROTOCOLO_01');
      if (definicaoProtocolo) {
        novaTimeline.unshift({ idOriginal: definicaoProtocolo.id, etapaDefinicao: definicaoProtocolo, status: 'CONCLUIDA', dataInicio: new Date(processo.dataDeposito) });
      }
    }
    let idDefPublicacaoUsada: string | null = null;
    const despachosAteArquivamento = despachosOrdenados.filter(d => {
        const dtDesp = d.RPI?.dataPublicacao ? new Date(d.RPI.dataPublicacao) : (d.ProtocoloDespacho?.[0]?.data ? new Date(d.ProtocoloDespacho[0].data) : null);
        return dtDesp ? dtDesp <= dataDespacho : false;
    });
    const dataPublicacaoAntesArquivamento = encontrarDataPublicacaoMaisRecente(despachosAteArquivamento.filter(d => d !== despacho));
    if (dataPublicacaoAntesArquivamento && !novaTimeline.find(e => e.etapaDefinicao.nomeOriginal === 'Publicação')) {
      const houveOposicaoPublicacao = verificarOposicaoAposData(despachosAteArquivamento, dataPublicacaoAntesArquivamento);
      idDefPublicacaoUsada = houveOposicaoPublicacao ? 'PUBLICACAO_COM_OPOSICAO_01' : 'PUBLICACAO_01';
      const defPublicacao = encontrarDefinicaoEtapaPorId(idDefPublicacaoUsada);
      if (defPublicacao) {
        const protocolIndex = novaTimeline.findIndex(e => e.idOriginal === 'PROTOCOLO_01');
        novaTimeline.splice(protocolIndex !== -1 ? protocolIndex + 1 : 0, 0, { idOriginal: defPublicacao.id, etapaDefinicao: defPublicacao, status: 'CONCLUIDA', dataInicio: dataPublicacaoAntesArquivamento });
        const dataFimPrazoOposicao = new Date(dataPublicacaoAntesArquivamento);
        dataFimPrazoOposicao.setDate(dataFimPrazoOposicao.getDate() + 60);
        const idDefPrazoOposicao = houveOposicaoPublicacao ? 'PRAZO_OPOSICAO_COM_OPOSICAO_01' : 'PRAZO_OPOSICAO_SEM_OPOSICAO_01';
        const defPrazoOposicao = encontrarDefinicaoEtapaPorId(idDefPrazoOposicao);
        if (defPrazoOposicao) {
          const publicacaoIdx = novaTimeline.findIndex(e => e.idOriginal === idDefPublicacaoUsada);
          novaTimeline.splice(publicacaoIdx !== -1 ? publicacaoIdx + 1 : novaTimeline.length, 0, {
            idOriginal: defPrazoOposicao.id, etapaDefinicao: defPrazoOposicao, status: 'CONCLUIDA',
            dataInicio: dataPublicacaoAntesArquivamento, dataFim: dataFimPrazoOposicao
          });
        }
      }
    }

    // Lógica específica para arquivamento por desistência
    if (arquivamentoInfo.tipoArquivamento === 'POR_DESISTENCIA') {
      const definicaoDesistencia = encontrarDefinicaoEtapaPorId('DESISTENCIA_HOMOLOGADA_01');
      if (definicaoDesistencia) {
        // Remove qualquer ocorrência anterior para evitar duplicatas
        novaTimeline = novaTimeline.filter(e => e.idOriginal !== definicaoDesistencia.id);
        novaTimeline.push({
          idOriginal: definicaoDesistencia.id, 
          etapaDefinicao: definicaoDesistencia, 
          status: 'CONCLUIDA', 
          dataInicio: dataDespacho, // Data do despacho que homologou a desistência
          dataFim: dataDespacho 
        });
      }
    } else if (arquivamentoInfo.tipoArquivamento === 'POR_NAO_CUMPRIMENTO_EXIGENCIA' && novoEstado.exigenciaAtiva && dataDespacho >= novoEstado.exigenciaAtiva.dataDespachoExigencia) {
      const definicaoExigNaoCumprida = encontrarDefinicaoEtapaPorId('EXIGENCIA_NAO_CUMPRIDA_01');
      if (definicaoExigNaoCumprida && novoEstado.exigenciaAtiva) {
        if (!novaTimeline.find(e => e.idOriginal === 'EXIGENCIA_NAO_CUMPRIDA_01')) {
            novaTimeline.push({
                idOriginal: definicaoExigNaoCumprida.id, etapaDefinicao: definicaoExigNaoCumprida, status: 'CONCLUIDA',
                dataInicio: novoEstado.exigenciaAtiva.dataDespachoExigencia, dataFim: dataDespacho,
                observacaoGerada: `Arquivado por não cumprimento de exigência de ${formatarData(novoEstado.exigenciaAtiva.dataDespachoExigencia)}.`
            });
        }
      }
    }
    // A etapa de Arquivamento genérica é adicionada como ATUAL para todos os tipos de arquivamento que chegam aqui
    const definicaoArquivamento = encontrarDefinicaoEtapaPorId('ARQUIVAMENTO_01');
    if (definicaoArquivamento) {
      novaTimeline = novaTimeline.filter(e => e.idOriginal !== 'ARQUIVAMENTO_01'); // Evita duplicatas da etapa de arquivamento
      let observacaoArquivamento = "Pedido arquivado."; // Default
      if (arquivamentoInfo.tipoArquivamento === 'POR_DESISTENCIA') {
        observacaoArquivamento = "Pedido arquivado devido à homologação da desistência.";
      } else if (arquivamentoInfo.tipoArquivamento === 'POR_NAO_CUMPRIMENTO_EXIGENCIA') {
        observacaoArquivamento = "Pedido arquivado por não cumprimento de exigência.";
      } // Para 'OUTRO', mantém o default ou pode adicionar mais lógicas

      novaTimeline.push({ 
        idOriginal: definicaoArquivamento.id, 
        etapaDefinicao: definicaoArquivamento, 
        status: 'ATUAL', 
        dataInicio: dataDespacho, 
        exigenciaInfo: null, 
        observacaoGerada: observacaoArquivamento 
      });
    }
    novoEstado.exigenciaAtiva = null;
    novoEstado.etapaAtualIdentificada = true;
    return { timelineAtualizada: novaTimeline, estadoAtualizado: novoEstado, finalizarProcessamento: true, };
  }
  return { timelineAtualizada: params.timelineAnterior, estadoAtualizado: novoEstado, finalizarProcessamento: false };
}

export function processarPrazosPagamentoPosLoopHandler(params: PosLoopHandlerParams): HandlerResultado {
  const { processo, despachosOrdenados, timelineAnterior, estadoAtual, temDataDeposito, hoje } = params;
  const novoEstado = { ...estadoAtual };
  let novaTimeline = [...timelineAnterior]; 

  if (novoEstado.dataDeferimento && !novoEstado.taxaConcessaoPaga && !novoEstado.dataConcessao && !novoEstado.arquivadoPorFaltaPagamentoTaxa) {
    const dataFimPrazoOrdinario = new Date(novoEstado.dataDeferimento);
    dataFimPrazoOrdinario.setDate(dataFimPrazoOrdinario.getDate() + 60);

    const dataInicioPrazoExtraordinario = new Date(dataFimPrazoOrdinario);

    const dataFimPrazoExtraordinario = new Date(dataInicioPrazoExtraordinario);
    dataFimPrazoExtraordinario.setDate(dataFimPrazoExtraordinario.getDate() + 30);
    
    const dataLimiteAntecipacaoArquivamento = new Date(dataFimPrazoExtraordinario);
    dataLimiteAntecipacaoArquivamento.setDate(dataLimiteAntecipacaoArquivamento.getDate() + 2);

    let construirTimelineParaPrazos = false;
    novaTimeline = []; 

    if (temDataDeposito) {
        const defProto = encontrarDefinicaoEtapaPorId('PROTOCOLO_01');
        if (defProto) novaTimeline.push({ idOriginal: defProto.id, etapaDefinicao: defProto, status: 'CONCLUIDA', dataInicio: new Date(processo.dataDeposito!) });
    }
    const dataPubAntesDeferimento = encontrarDataPublicacaoMaisRecente(despachosOrdenados.filter(d => d.RPI?.dataPublicacao && novoEstado.dataDeferimento && new Date(d.RPI.dataPublicacao) < novoEstado.dataDeferimento));
    if (dataPubAntesDeferimento) {
        const houveOposicao = verificarOposicaoAposData(despachosOrdenados, dataPubAntesDeferimento);
        const idDefPub = houveOposicao ? 'PUBLICACAO_COM_OPOSICAO_01' : 'PUBLICACAO_01';
        const defPub = encontrarDefinicaoEtapaPorId(idDefPub);
        if (defPub) novaTimeline.push({idOriginal: defPub.id, etapaDefinicao: defPub, status: 'CONCLUIDA', dataInicio: dataPubAntesDeferimento});
    }
    const defDefer = encontrarDefinicaoEtapaPorId('DEFERIMENTO_PEDIDO_01');
    if (defDefer && novoEstado.dataDeferimento) novaTimeline.push({idOriginal: defDefer.id, etapaDefinicao: defDefer, status: 'CONCLUIDA', dataInicio: novoEstado.dataDeferimento});

    if (hoje > dataLimiteAntecipacaoArquivamento) {
      novoEstado.arquivadoPorFaltaPagamentoTaxa = dataLimiteAntecipacaoArquivamento; 
      
      const defPrazoOrd = encontrarDefinicaoEtapaPorId('PRAZO_PAGAMENTO_TAXA_CONCESSAO_ORDINARIO_01');
      if (defPrazoOrd && novoEstado.dataDeferimento) novaTimeline.push({idOriginal: defPrazoOrd.id, etapaDefinicao: defPrazoOrd, status: 'CONCLUIDA', dataInicio: novoEstado.dataDeferimento, dataFim: dataFimPrazoOrdinario});
      
      const defPrazoExtra = encontrarDefinicaoEtapaPorId('PRAZO_PAGAMENTO_TAXA_CONCESSAO_EXTRAORDINARIO_01');
      if (defPrazoExtra && novoEstado.dataDeferimento) novaTimeline.push({idOriginal: defPrazoExtra.id, etapaDefinicao: defPrazoExtra, status: 'CONCLUIDA', dataInicio: dataInicioPrazoExtraordinario, dataFim: dataFimPrazoExtraordinario});
      
      const defArqTaxa = encontrarDefinicaoEtapaPorId('ARQUIVAMENTO_FALTA_PAGAMENTO_TAXA_01');
      if (defArqTaxa) novaTimeline.push({idOriginal: defArqTaxa.id, etapaDefinicao: defArqTaxa, status: 'ATUAL', dataInicio: novoEstado.arquivadoPorFaltaPagamentoTaxa, exigenciaInfo: null, observacaoGerada: "Arquivamento antecipado por expiração do prazo de pagamento."});
      
      construirTimelineParaPrazos = true;
    }
    else if (hoje > dataFimPrazoOrdinario && hoje <= dataLimiteAntecipacaoArquivamento) { 
      const defPrazoOrd = encontrarDefinicaoEtapaPorId('PRAZO_PAGAMENTO_TAXA_CONCESSAO_ORDINARIO_01');
      if (defPrazoOrd && novoEstado.dataDeferimento) novaTimeline.push({idOriginal: defPrazoOrd.id, etapaDefinicao: defPrazoOrd, status: 'CONCLUIDA', dataInicio: novoEstado.dataDeferimento, dataFim: dataFimPrazoOrdinario});
      
      const defPrazoExtra = encontrarDefinicaoEtapaPorId('PRAZO_PAGAMENTO_TAXA_CONCESSAO_EXTRAORDINARIO_01');
      if (defPrazoExtra && novoEstado.dataDeferimento) {
          novaTimeline.push({
            idOriginal: defPrazoExtra.id, etapaDefinicao: defPrazoExtra, status: 'ATUAL', 
            dataInicio: dataInicioPrazoExtraordinario, dataFim: dataFimPrazoExtraordinario, 
            exigenciaInfo: novoEstado.exigenciaAtiva 
          });
      }
      construirTimelineParaPrazos = true;
    }
    else if (novoEstado.dataDeferimento && hoje <= dataFimPrazoOrdinario) {
      const defPrazoOrd = encontrarDefinicaoEtapaPorId('PRAZO_PAGAMENTO_TAXA_CONCESSAO_ORDINARIO_01');
      if (defPrazoOrd) {
          novaTimeline.push({
            idOriginal: defPrazoOrd.id, etapaDefinicao: defPrazoOrd, status: 'ATUAL', 
            dataInicio: novoEstado.dataDeferimento, dataFim: dataFimPrazoOrdinario, 
            exigenciaInfo: novoEstado.exigenciaAtiva
          });
      }
      construirTimelineParaPrazos = true;
    }

    if (construirTimelineParaPrazos) {
      novoEstado.exigenciaAtiva = null; 
      novoEstado.etapaAtualIdentificada = true;
      novaTimeline = novaTimeline.filter(etapa => encontrarDefinicaoEtapaPorId(etapa.idOriginal));
      return {
        timelineAtualizada: novaTimeline,
        estadoAtualizado: novoEstado,
        finalizarProcessamento: true, 
      };
    }
  }

  return {
    timelineAtualizada: params.timelineAnterior, 
    estadoAtualizado: novoEstado, 
    finalizarProcessamento: false, 
  };
}

// Funções auxiliares para o fluxo padrão
function handleFluxoProtocoloInicial(
  params: FluxoPadraoParams,
  timelineBase: EtapaTimelineGerada[]
): { timeline: EtapaTimelineGerada[], estado: TimelineFluxoEstado } {
  const { processo, estadoAtual, temDataDeposito, despachosOrdenados, hoje, estimativasDb } = params;
  let novaTimeline = [...timelineBase];
  const novoEstado = { ...estadoAtual };

  if (temDataDeposito && processo.dataDeposito && !novaTimeline.some(e => e.idOriginal === 'PROTOCOLO_01')) {
    const definicaoProtocolo = encontrarDefinicaoEtapaPorId('PROTOCOLO_01');
    if (definicaoProtocolo) {
      novaTimeline.unshift({
        idOriginal: definicaoProtocolo.id, etapaDefinicao: definicaoProtocolo, status: 'CONCLUIDA',
        dataInicio: new Date(processo.dataDeposito)
      });
    }
  }

  if (temDataDeposito && processo.dataDeposito && despachosOrdenados.length === 0 && !novoEstado.etapaAtualIdentificada) {
    const etapaProtocoloExistenteIndex = novaTimeline.findIndex(e => e.idOriginal === 'PROTOCOLO_01');
    const definicaoProtocolo = encontrarDefinicaoEtapaPorId('PROTOCOLO_01');

    if (definicaoProtocolo) {
        if (etapaProtocoloExistenteIndex > -1) {
            novaTimeline[etapaProtocoloExistenteIndex].status = 'ATUAL';
            novaTimeline[etapaProtocoloExistenteIndex].dataInicio = new Date(processo.dataDeposito!);
            if (novoEstado.exigenciaAtiva) novaTimeline[etapaProtocoloExistenteIndex].exigenciaInfo = novoEstado.exigenciaAtiva;
        } else {
            novaTimeline.unshift({
              idOriginal: definicaoProtocolo.id, etapaDefinicao: definicaoProtocolo, status: 'ATUAL',
              dataInicio: new Date(processo.dataDeposito!),
              exigenciaInfo: novoEstado.exigenciaAtiva,
            });
        }
        novaTimeline = novaTimeline.filter(e => e.status === 'CONCLUIDA' || e.idOriginal === 'PROTOCOLO_01');
    }
    
    const definicaoPublicacaoFutura = encontrarDefinicaoEtapaPorId('PUBLICACAO_01');
    if (definicaoPublicacaoFutura && processo.dataDeposito) {
      const dataReferenciaParaProximaTerca = new Date(processo.dataDeposito!);
      dataReferenciaParaProximaTerca.setDate(dataReferenciaParaProximaTerca.getDate() + 21);
      const dataEstimadaPublicacao = calcularProximaTerca(dataReferenciaParaProximaTerca, 0);
      novaTimeline.push({
        idOriginal: definicaoPublicacaoFutura.id, etapaDefinicao: definicaoPublicacaoFutura,
        status: 'PROXIMA_ESTIMADA', dataInicio: dataEstimadaPublicacao,
        observacaoGerada: `Estimativa de publicação para ${formatarData(dataEstimadaPublicacao)}.`
      });
    }
    novoEstado.etapaAtualIdentificada = true;
  }
  return { timeline: novaTimeline, estado: novoEstado };
}

function handleFluxoPublicacaoOposicao(
  params: FluxoPadraoParams,
  timelineBase: EtapaTimelineGerada[]
): { timeline: EtapaTimelineGerada[], estado: TimelineFluxoEstado } {
  const { processo, despachosOrdenados, estadoAtual, hoje, estimativasDb, temDataDeposito } = params;
  let novaTimeline = [...timelineBase];
  const novoEstado = { ...estadoAtual };

  // Se uma etapa atual já foi identificada por um handler anterior OU se não há data de depósito (essencial para o fluxo padrão)
  if (novoEstado.etapaAtualIdentificada || !temDataDeposito || !processo.dataDeposito) {
    return { timeline: novaTimeline, estado: novoEstado };
  }

  // A data de publicação é crucial para determinar o prazo de oposição.
  const dataPublicacao = encontrarDataPublicacaoMaisRecente(despachosOrdenados); // Corrigido: chamada original

  if (dataPublicacao) {
    const houveOposicao = verificarOposicaoAposData(despachosOrdenados, dataPublicacao);
    const idDefinicaoPublicacao = houveOposicao ? 'PUBLICACAO_COM_OPOSICAO_01' : 'PUBLICACAO_01';
    const definicaoPublicacaoConcluida = encontrarDefinicaoEtapaPorId(idDefinicaoPublicacao);

    // Adicionar etapa de Publicação Concluída se ainda não existir
    if (definicaoPublicacaoConcluida && !novaTimeline.some(e => e.idOriginal === idDefinicaoPublicacao && e.status === 'CONCLUIDA')) {
        const protoIndex = novaTimeline.findIndex(e => e.idOriginal === 'PROTOCOLO_01');
        novaTimeline.splice(protoIndex > -1 ? protoIndex + 1 : 0, 0, {
          idOriginal: definicaoPublicacaoConcluida.id, etapaDefinicao: definicaoPublicacaoConcluida,
          status: 'CONCLUIDA', dataInicio: dataPublicacao,
        });
        // Remover duplicatas ou entradas anteriores de publicação que possam ter sido PROXIMA
        novaTimeline = novaTimeline.filter((e, index, self) => 
            index === self.findIndex(t => t.idOriginal === e.idOriginal && t.status === e.status) || 
            e.idOriginal !== idDefinicaoPublicacao || e.status !== 'CONCLUIDA'
        );
    }

    const idDefinicaoPrazoOposicao = houveOposicao ? 'PRAZO_OPOSICAO_COM_OPOSICAO_01' : 'PRAZO_OPOSICAO_SEM_OPOSICAO_01';
    const definicaoPrazoOposicao = encontrarDefinicaoEtapaPorId(idDefinicaoPrazoOposicao);

    if (definicaoPrazoOposicao) {
      const dataFimPrazoOposicao = new Date(dataPublicacao);
      dataFimPrazoOposicao.setDate(dataFimPrazoOposicao.getDate() + 60);

      novaTimeline = novaTimeline.filter(e => e.idOriginal !== idDefinicaoPrazoOposicao); // Limpa prazo de oposição anterior

      if (hoje <= dataFimPrazoOposicao) { // Se o prazo de oposição ainda está vigente
        novaTimeline.push({
          idOriginal: definicaoPrazoOposicao.id, etapaDefinicao: definicaoPrazoOposicao,
          status: 'ATUAL', dataInicio: dataPublicacao, dataFim: dataFimPrazoOposicao,
          exigenciaInfo: novoEstado.exigenciaAtiva, // Carrega exigência ativa, se houver
        });
        novoEstado.etapaAtualIdentificada = true;

        if (houveOposicao) {
          const defManifestacao = encontrarDefinicaoEtapaPorId('PRAZO_MANIFESTACAO_OPOSICAO_01');
          const dataNotificacaoOpo = encontrarDataNotificacaoOposicaoMaisRecente(despachosOrdenados, dataPublicacao);
          if (defManifestacao && dataNotificacaoOpo) {
            const dataFimPrazoManifestacao = new Date(dataNotificacaoOpo);
            dataFimPrazoManifestacao.setDate(dataFimPrazoManifestacao.getDate() + 60);
            novaTimeline.push({
              idOriginal: defManifestacao.id, etapaDefinicao: defManifestacao, status: 'PROXIMA_EXATA',
              dataInicio: dataNotificacaoOpo, dataFim: dataFimPrazoManifestacao,
            });
          }
        } else { // Sem oposição, e prazo de oposição ATUAL
          // Verificar se o mérito já foi processado - se sim, não adicionar análise de mérito
          if (!jaMeritoFoiProcessado(novaTimeline)) {
            // Verificar se há exigência ativa para usar análise de mérito com exigência
            if (novoEstado.exigenciaAtiva) {
              const defAnaliseComExigencia = encontrarDefinicaoEtapaPorId('ANALISE_MERITO_COM_EXIGENCIA_01');
              if (defAnaliseComExigencia && processo.dataDeposito) {
                let dataInicioAnaliseEstimada = new Date(processo.dataDeposito);
                let obs = defAnaliseComExigencia.prazoProximaEtapaDescricao || "Estimativa baseada na data de depósito.";
                if (estimativasDb?.mediaComExigencia) {
                  dataInicioAnaliseEstimada = new Date(processo.dataDeposito);
                  dataInicioAnaliseEstimada.setDate(dataInicioAnaliseEstimada.getDate() + estimativasDb.mediaComExigencia);
                  obs = `Estimativa para ${formatarData(dataInicioAnaliseEstimada)} (média de ${estimativasDb.mediaComExigencia} dias a partir do depósito para processos com exigência).`;
                }
                novaTimeline.push({
                  idOriginal: defAnaliseComExigencia.id, etapaDefinicao: defAnaliseComExigencia, status: 'PROXIMA_ESTIMADA',
                  dataInicio: dataInicioAnaliseEstimada, observacaoGerada: obs,
                });
              }
            } else {
              // Análise de mérito sem exigência (lógica original)
          const defAnaliseSO = encontrarDefinicaoEtapaPorId('ANALISE_MERITO_SEM_OPOSICAO_01');
          if (defAnaliseSO && processo.dataDeposito) { // dataDeposito é string, new Date() é usado abaixo
            let dataInicioAnaliseEstimada = new Date(processo.dataDeposito);
            let obs = defAnaliseSO.prazoProximaEtapaDescricao || "Estimativa baseada na data de depósito.";
            if (estimativasDb?.mediaSemIntervencoes) {
              dataInicioAnaliseEstimada = new Date(processo.dataDeposito); // Base de cálculo alterada
              dataInicioAnaliseEstimada.setDate(dataInicioAnaliseEstimada.getDate() + estimativasDb.mediaSemIntervencoes);
              obs = `Estimativa para ${formatarData(dataInicioAnaliseEstimada)} (média de ${estimativasDb.mediaSemIntervencoes} dias a partir do depósito).`;
            }
            novaTimeline.push({
              idOriginal: defAnaliseSO.id, etapaDefinicao: defAnaliseSO, status: 'PROXIMA_ESTIMADA',
              dataInicio: dataInicioAnaliseEstimada, observacaoGerada: obs,
            });
              }
            }
          }
        }
      } else { // Prazo de oposição CONCLUÍDO
        novaTimeline.push({
          idOriginal: definicaoPrazoOposicao.id, etapaDefinicao: definicaoPrazoOposicao,
          status: 'CONCLUIDA', dataInicio: dataPublicacao, dataFim: dataFimPrazoOposicao,
        });
        // Se não houve oposição e nenhuma etapa ATUAL foi identificada ainda (ex: por exigência pendente)
        if (!houveOposicao && !novoEstado.etapaAtualIdentificada && processo.dataDeposito) {
          // Verificar se o mérito já foi processado - se sim, não adicionar análise de mérito
          if (!jaMeritoFoiProcessado(novaTimeline)) {
            // Verificar se há exigência ativa para usar análise de mérito com exigência
            if (novoEstado.exigenciaAtiva) {
              const defAnaliseComExigencia = encontrarDefinicaoEtapaPorId('ANALISE_MERITO_COM_EXIGENCIA_01');
              if (defAnaliseComExigencia) {
                let dataInicioAnaliseEstimada = new Date(processo.dataDeposito);
                let obs = defAnaliseComExigencia.prazoProximaEtapaDescricao || "Estimativa baseada na data de depósito.";
                if (estimativasDb?.mediaComExigencia) {
                  dataInicioAnaliseEstimada = new Date(processo.dataDeposito);
                  dataInicioAnaliseEstimada.setDate(dataInicioAnaliseEstimada.getDate() + estimativasDb.mediaComExigencia);
                  obs = `Estimativa para ${formatarData(dataInicioAnaliseEstimada)} (média de ${estimativasDb.mediaComExigencia} dias a partir do depósito para processos com exigência).`;
                }
                // Evitar duplicar a etapa de análise se já existir
                if (!novaTimeline.some(e => e.idOriginal === defAnaliseComExigencia.id && (e.status === 'PROXIMA_ESTIMADA' || e.status === 'ATUAL'))) {
                  novaTimeline.push({
                    idOriginal: defAnaliseComExigencia.id, etapaDefinicao: defAnaliseComExigencia, status: 'PROXIMA_ESTIMADA',
                    dataInicio: dataInicioAnaliseEstimada, observacaoGerada: obs,
                  });
                }
              }
            } else {
              // Análise de mérito sem exigência (lógica original)
          const defAnaliseSO = encontrarDefinicaoEtapaPorId('ANALISE_MERITO_SEM_OPOSICAO_01');
          if (defAnaliseSO) {
            let dataInicioAnaliseEstimada = new Date(processo.dataDeposito);
            let obs = defAnaliseSO.prazoProximaEtapaDescricao || "Estimativa baseada na data de depósito.";
            if (estimativasDb?.mediaSemIntervencoes) {
              dataInicioAnaliseEstimada = new Date(processo.dataDeposito); // Base de cálculo alterada
              dataInicioAnaliseEstimada.setDate(dataInicioAnaliseEstimada.getDate() + estimativasDb.mediaSemIntervencoes);
              obs = `Estimativa para ${formatarData(dataInicioAnaliseEstimada)} (média de ${estimativasDb.mediaSemIntervencoes} dias a partir do depósito).`;
            }
            // Evitar duplicar a etapa de análise se já existir como PROXIMA_ESTIMADA ou ATUAL
            if (!novaTimeline.some(e => e.idOriginal === defAnaliseSO.id && (e.status === 'PROXIMA_ESTIMADA' || e.status === 'ATUAL'))) {
              novaTimeline.push({
                idOriginal: defAnaliseSO.id, etapaDefinicao: defAnaliseSO, status: 'PROXIMA_ESTIMADA',
                dataInicio: dataInicioAnaliseEstimada, observacaoGerada: obs,
              });
                }
              }
            }
          }
        }
      }
    }
  }
  return { timeline: novaTimeline, estado: novoEstado };
}

function handleFluxoManifestacaoOposicao(
  params: FluxoPadraoParams,
  timelineBase: EtapaTimelineGerada[]
): { timeline: EtapaTimelineGerada[], estado: TimelineFluxoEstado } {
  const { despachosOrdenados, estadoAtual, hoje, estimativasDb, processo } = params;
  let novaTimeline = [...timelineBase];
  const novoEstado = { ...estadoAtual };

  if (novoEstado.etapaAtualIdentificada) return { timeline: novaTimeline, estado: novoEstado };

  const prazoOposicaoComOposicaoConcluida = novaTimeline.find(
    e => e.idOriginal === 'PRAZO_OPOSICAO_COM_OPOSICAO_01' && e.status === 'CONCLUIDA'
  );

  if (prazoOposicaoComOposicaoConcluida && prazoOposicaoComOposicaoConcluida.dataInicio) {
    const dataPublicacaoOriginalOposicao = prazoOposicaoComOposicaoConcluida.dataInicio;
    const dataNotificacaoOpo = encontrarDataNotificacaoOposicaoMaisRecente(despachosOrdenados, dataPublicacaoOriginalOposicao);

    if (dataNotificacaoOpo) {
      const dataFimPrazoManifestacao = new Date(dataNotificacaoOpo);
      dataFimPrazoManifestacao.setDate(dataFimPrazoManifestacao.getDate() + 60);
      const definicaoPrazoManifestacao = encontrarDefinicaoEtapaPorId('PRAZO_MANIFESTACAO_OPOSICAO_01');
      
      if (definicaoPrazoManifestacao) {
        novaTimeline = novaTimeline.filter(e => e.idOriginal !== definicaoPrazoManifestacao.id && e.idOriginal !== 'MANIFESTACAO_OPOSICAO_APRESENTADA_01');

        const dataManifestacaoApresentada = encontrarDataProtocoloManifestacao(despachosOrdenados);
        let manifestacaoFoiConsideradaTempestiva = false;

        if (dataManifestacaoApresentada) {
          // Adicionar MANIFESTACAO_OPOSICAO_APRESENTADA_01 como CONCLUIDA, independentemente de ser tempestiva ou não.
          const defManifestacaoApresentada = encontrarDefinicaoEtapaPorId('MANIFESTACAO_OPOSICAO_APRESENTADA_01');
          if (defManifestacaoApresentada) {
            let obsManifestacao = "Manifestação à oposição protocolada.";
            if (dataManifestacaoApresentada > dataFimPrazoManifestacao) {
              obsManifestacao += " (Publicada após o prazo legal)";
            }
            // Verificar se existe o despacho "Decisão de não conhecer da petição" associado a este protocolo
            const despachoNaoConhecer = despachosOrdenados.find(d => 
              d.ProtocoloDespacho?.some(p => p.codigoServico && p.codigoServico.replace('.', '').startsWith('339') && p.data === dataManifestacaoApresentada.toISOString().split('T')[0]) &&
              d.nome?.toLowerCase().includes("decisão de não conhecer da petição")
            );
            if (despachoNaoConhecer && despachoNaoConhecer.RPI?.dataPublicacao) {
              obsManifestacao += ` Despacho 'Decisão de não conhecer da petição' publicado em RPI de ${formatarData(new Date(despachoNaoConhecer.RPI.dataPublicacao))}.`;
            }

            const idxPrazoManifConcluido = novaTimeline.findIndex(e=> e.idOriginal === 'PRAZO_MANIFESTACAO_OPOSICAO_01' && e.status === 'CONCLUIDA');
            const insertPosition = idxPrazoManifConcluido !== -1 ? idxPrazoManifConcluido + 1 : novaTimeline.length;
            
            novaTimeline.splice(insertPosition, 0, { // Inserir após o prazo de manifestação
                idOriginal: defManifestacaoApresentada.id, etapaDefinicao: defManifestacaoApresentada,
                status: 'CONCLUIDA', dataInicio: dataManifestacaoApresentada, observacaoGerada: obsManifestacao
            });
          }
          manifestacaoFoiConsideradaTempestiva = dataManifestacaoApresentada <= dataFimPrazoManifestacao;
        }

        if (hoje <= dataFimPrazoManifestacao && !dataManifestacaoApresentada) { // Ainda no prazo e NENHUMA manifestação ainda
          novaTimeline.push({
            idOriginal: definicaoPrazoManifestacao.id, etapaDefinicao: definicaoPrazoManifestacao,
            status: 'ATUAL', dataInicio: dataNotificacaoOpo, dataFim: dataFimPrazoManifestacao,
            exigenciaInfo: novoEstado.exigenciaAtiva,
          });
          novoEstado.etapaAtualIdentificada = true;

          // Próxima etapa se manifestar (tempestivamente) ou não - só se o mérito não foi processado
          if (!jaMeritoFoiProcessado(novaTimeline)) {
          const defProximaAnalise = manifestacaoFoiConsideradaTempestiva 
            ? encontrarDefinicaoEtapaPorId('ANALISE_MERITO_COM_OPOSICAO_COM_MANIFESTACAO_01')
            : encontrarDefinicaoEtapaPorId('ANALISE_MERITO_COM_OPOSICAO_SEM_MANIFESTACAO_01');
          
          if (defProximaAnalise) {
            let dataInicioAnalise = new Date(dataFimPrazoManifestacao); dataInicioAnalise.setDate(dataInicioAnalise.getDate() + 1);
            let obs = defProximaAnalise.prazoProximaEtapaDescricao || "Estimativa após prazo.";
            const mediaDias = estimativasDb?.mediaComOposicao; // Usar média com oposição para ambos os casos aqui
            if (mediaDias) {
              dataInicioAnalise = new Date(dataFimPrazoManifestacao);
              dataInicioAnalise.setDate(dataInicioAnalise.getDate() + mediaDias);
              obs = `Estimativa para ${formatarData(dataInicioAnalise)} (média de ${mediaDias} dias).`;
              }
              novaTimeline.push({
              idOriginal: defProximaAnalise.id, etapaDefinicao: defProximaAnalise, status: 'PROXIMA_ESTIMADA',
              dataInicio: dataInicioAnalise, observacaoGerada: obs
            });
            }
          }

        } else { // Prazo já passou ou manifestação já foi feita (tempestiva ou não)
          // Se a manifestação NÃO foi apresentada, então o prazo se concluiu por expiração.
          if (!dataManifestacaoApresentada) {
            novaTimeline.push({ // Prazo de manifestação sempre concluído neste ponto
                idOriginal: definicaoPrazoManifestacao.id, etapaDefinicao: definicaoPrazoManifestacao,
                status: 'CONCLUIDA', dataInicio: dataNotificacaoOpo, dataFim: dataFimPrazoManifestacao,
            });
          }

          // Se MANIFESTACAO_OPOSICAO_APRESENTADA_01 já foi adicionada acima, não precisa adicionar de novo.
          // A decisão de qual "Análise de Mérito" virá agora depende se a manifestação (se houve) foi tempestiva.
          if (!novoEstado.etapaAtualIdentificada && !jaMeritoFoiProcessado(novaTimeline)) { // Só adiciona próxima análise se nenhuma etapa ATUAL foi definida ainda e mérito não foi processado.
            const defProximaAnalise = manifestacaoFoiConsideradaTempestiva && dataManifestacaoApresentada // Houve manifestação e foi tempestiva
              ? encontrarDefinicaoEtapaPorId('ANALISE_MERITO_COM_OPOSICAO_COM_MANIFESTACAO_01')
              : encontrarDefinicaoEtapaPorId('ANALISE_MERITO_COM_OPOSICAO_SEM_MANIFESTACAO_01'); // Não houve, ou foi intempestiva

            if (defProximaAnalise) {
              let dataInicioAnalise = new Date(dataFimPrazoManifestacao); dataInicioAnalise.setDate(dataInicioAnalise.getDate() + 1);
              let obs = defProximaAnalise.prazoProximaEtapaDescricao || "Estimativa após prazo de manifestação.";
              const mediaDias = estimativasDb?.mediaComOposicao;
              if (mediaDias) {
                dataInicioAnalise = new Date(dataFimPrazoManifestacao);
                dataInicioAnalise.setDate(dataInicioAnalise.getDate() + mediaDias);
                obs = `Estimativa para ${formatarData(dataInicioAnalise)} (média de ${mediaDias} dias).`;
              }
              if (!novaTimeline.some(e => e.idOriginal === defProximaAnalise.id && (e.status === 'PROXIMA_ESTIMADA' || e.status === 'ATUAL'))) {
                novaTimeline.push({
                  idOriginal: defProximaAnalise.id, etapaDefinicao: defProximaAnalise, status: 'PROXIMA_ESTIMADA',
                  dataInicio: dataInicioAnalise, observacaoGerada: obs
                });
              }
            }
          }
        }
      }
    }
  }
  // Assegurar que, se MANIFESTACAO_OPOSICAO_APRESENTADA_01 foi adicionada, ela esteja após PRAZO_MANIFESTACAO_OPOSICAO_01 se ambas forem concluídas.
  const idxPrazoManif = novaTimeline.findIndex(e => e.idOriginal === 'PRAZO_MANIFESTACAO_OPOSICAO_01' && e.status === 'CONCLUIDA');
  const idxManifApres = novaTimeline.findIndex(e => e.idOriginal === 'MANIFESTACAO_OPOSICAO_APRESENTADA_01' && e.status === 'CONCLUIDA');

  if (idxPrazoManif !== -1 && idxManifApres !== -1 && idxManifApres < idxPrazoManif) {
    const [manifApres] = novaTimeline.splice(idxManifApres, 1);
    novaTimeline.splice(idxPrazoManif, 0, manifApres); // Coloca a manif apresentada logo depois do prazo concluído
  }


  return { timeline: novaTimeline, estado: novoEstado };
}

function handleFluxoAnaliseMeritoAtual(
  params: FluxoPadraoParams,
  timelineBase: EtapaTimelineGerada[]
): { timeline: EtapaTimelineGerada[], estado: TimelineFluxoEstado } {
  const { hoje, estadoAtual } = params;
  let novaTimeline = [...timelineBase];
  const novoEstado = { ...estadoAtual };

  if (novoEstado.etapaAtualIdentificada) return { timeline: novaTimeline, estado: novoEstado };

  const proximaEstimadaAnaliseIndex = novaTimeline.findIndex(
    e => e.status === 'PROXIMA_ESTIMADA' && 
         e.etapaDefinicao.nomeOriginal?.startsWith("Análise de mérito") &&
         e.dataInicio && hoje >= e.dataInicio
  );

  if (proximaEstimadaAnaliseIndex !== -1) {
    const etapaAnalise = novaTimeline[proximaEstimadaAnaliseIndex];
    etapaAnalise.status = 'ATUAL';
    if (novoEstado.exigenciaAtiva) {
      etapaAnalise.exigenciaInfo = novoEstado.exigenciaAtiva;
    }
    
    novaTimeline = novaTimeline.filter(e => 
        !(e.status === 'PROXIMA_ESTIMADA' && 
          e.etapaDefinicao.nomeOriginal?.startsWith("Análise de mérito") && 
          e.idOriginal !== etapaAnalise.idOriginal)
    );
    novoEstado.etapaAtualIdentificada = true;
  }
  return { timeline: novaTimeline, estado: novoEstado };
}

function handleFluxoFallback(
  params: FluxoPadraoParams,
  timelineBase: EtapaTimelineGerada[]
): { timeline: EtapaTimelineGerada[], estado: TimelineFluxoEstado } {
  const { hoje, estadoAtual } = params;
  let novaTimeline = [...timelineBase];
  const novoEstado = { ...estadoAtual };

  const temEtapaValida = novaTimeline.some(e => e.status === 'ATUAL' || e.status === 'PROXIMA_EXATA' || e.status === 'PROXIMA_ESTIMADA');

  if (!novoEstado.etapaAtualIdentificada && !temEtapaValida) {
    const definicaoFallback = encontrarDefinicaoEtapaPorId('LOGICA_EM_DESENVOLVIMENTO_01');
    if (definicaoFallback) {
      novaTimeline = novaTimeline.filter(e => e.idOriginal !== 'LOGICA_EM_DESENVOLVIMENTO_01');
      novaTimeline.push({
        idOriginal: definicaoFallback.id, etapaDefinicao: definicaoFallback,
        status: 'ATUAL', dataInicio: hoje,
        observacaoGerada: "A lógica da timeline para este estado do processo ainda está em desenvolvimento ou o estado é inesperado."
      });
      novoEstado.etapaAtualIdentificada = true;
    }
  }
  return { timeline: novaTimeline, estado: novoEstado };
}

// Handler principal para o fluxo padrão
export function processarFluxoPadraoTimelineHandler(params: FluxoPadraoParams): HandlerResultado {
  let timelineAtual = [...params.timelineAnterior]; 
  let estadoAtual = { ...params.estadoAtual };     

  if (!estadoAtual.etapaAtualIdentificada) {
    const resProtocolo = handleFluxoProtocoloInicial(params, timelineAtual);
    timelineAtual = resProtocolo.timeline;
    estadoAtual = resProtocolo.estado;
  }

  if (!estadoAtual.etapaAtualIdentificada) {
    const resPubOpo = handleFluxoPublicacaoOposicao(params, timelineAtual);
    timelineAtual = resPubOpo.timeline;
    estadoAtual = resPubOpo.estado;
  }

  if (!estadoAtual.etapaAtualIdentificada) {
    const resManifestacao = handleFluxoManifestacaoOposicao(params, timelineAtual);
    timelineAtual = resManifestacao.timeline;
    estadoAtual = resManifestacao.estado;
  }
  
  if (!estadoAtual.etapaAtualIdentificada) {
    const resAnaliseAtual = handleFluxoAnaliseMeritoAtual(params, timelineAtual);
    timelineAtual = resAnaliseAtual.timeline;
    estadoAtual = resAnaliseAtual.estado;
  }

  if (!estadoAtual.etapaAtualIdentificada) {
    const resFallback = handleFluxoFallback(params, timelineAtual);
    timelineAtual = resFallback.timeline;
    estadoAtual = resFallback.estado;
  }
  
  return {
    timelineAtualizada: timelineAtual, 
    estadoAtualizado: estadoAtual,     
    finalizarProcessamento: estadoAtual.etapaAtualIdentificada 
  };
}

export function processarIndeferimentoHandler(params: HandlerParams): HandlerResultado {
  const { despacho, dataDespacho, processo, despachosOrdenados, timelineAnterior, estadoAnterior, temDataDeposito } = params;

  // Condição para acionar este handler: é um despacho de indeferimento
  // E o processo ainda não tem uma concessão ou um arquivamento por falta de pagamento (estados finais prioritários)
  if (isDespachoDeIndeferimento(despacho) && !estadoAnterior.dataConcessao && !estadoAnterior.arquivadoPorFaltaPagamentoTaxa) {
    const novaTimeline: EtapaTimelineGerada[] = [];
    const novoEstado = { ...estadoAnterior }; // Criar uma cópia local do estado para modificar

    // Limpar exigência ativa no estado, pois o indeferimento a supera
    novoEstado.exigenciaAtiva = null;

    // 1. Preservar etapas concluídas relevantes e Sobrestamento Concluído
    const sobrestamentoConcluidoAnterior = timelineAnterior.find(
        e => e.idOriginal === 'SOBRESTAMENTO_01' && e.status === 'CONCLUIDA'
    );
    const outrasConcluidasRelevantes = timelineAnterior.filter(
        e => e.status === 'CONCLUIDA' && 
             e.idOriginal !== 'SOBRESTAMENTO_01' && 
             e.idOriginal !== 'INDICADOR_EXIGENCIA_ATIVA_01' // Não manter exigência ativa como concluída ainda
    );
    novaTimeline.push(...outrasConcluidasRelevantes);
    if (sobrestamentoConcluidoAnterior && !novaTimeline.some(e => e.idOriginal === 'SOBRESTAMENTO_01')) {
        novaTimeline.push(sobrestamentoConcluidoAnterior);
    }

    // Concluir a etapa de Exigência Ativa, se houver uma como ATUAL na timelineAnterior
    const exigenciaAtivaIndexNaTimelineAnterior = timelineAnterior.findIndex(e => e.idOriginal === 'INDICADOR_EXIGENCIA_ATIVA_01' && e.status === 'ATUAL');
    if (exigenciaAtivaIndexNaTimelineAnterior !== -1) {
        const exigenciaConcluida = {
            ...timelineAnterior[exigenciaAtivaIndexNaTimelineAnterior],
            status: 'CONCLUIDA' as const,
            dataFim: dataDespacho, // Concluída na data do indeferimento
            observacaoGerada: (timelineAnterior[exigenciaAtivaIndexNaTimelineAnterior].observacaoGerada || "") + " Indeferimento do pedido superveniente."
        };
        novaTimeline.push(exigenciaConcluida);
    }
    
    // 2. Adicionar etapas históricas básicas (Protocolo, Publicação, Oposição) se ainda não estiverem
    // Essas são adicionadas se não existirem, para garantir que o histórico até o indeferimento seja completo.
    if (temDataDeposito && processo.dataDeposito && !novaTimeline.some(e => e.idOriginal === 'PROTOCOLO_01')) {
      const defProto = encontrarDefinicaoEtapaPorId('PROTOCOLO_01');
      if (defProto) novaTimeline.unshift({ idOriginal: defProto.id, etapaDefinicao: defProto, status: 'CONCLUIDA', dataInicio: new Date(processo.dataDeposito) });
    }

    // Considera despachos até a data do indeferimento para construir o histórico
    const despachosAteIndeferimento = despachosOrdenados.filter(d => {
        const dtDesp = d.RPI?.dataPublicacao ? new Date(d.RPI.dataPublicacao) : (d.ProtocoloDespacho?.[0]?.data ? new Date(d.ProtocoloDespacho[0].data) : null);
        return dtDesp ? dtDesp <= dataDespacho : false;
    });

    const dataPublicacaoOriginal = encontrarDataPublicacaoMaisRecente(despachosAteIndeferimento.filter(d => d !== despacho && isDespachoDePublicacao(d)));

    if (dataPublicacaoOriginal && !novaTimeline.some(e => e.etapaDefinicao.nomeOriginal === 'Publicação')) {
      const houveOposicao = verificarOposicaoAposData(despachosAteIndeferimento, dataPublicacaoOriginal);
      const idDefPub = houveOposicao ? 'PUBLICACAO_COM_OPOSICAO_01' : 'PUBLICACAO_01';
      const defPub = encontrarDefinicaoEtapaPorId(idDefPub);
      if (defPub) {
        novaTimeline.push({idOriginal: defPub.id, etapaDefinicao: defPub, status: 'CONCLUIDA', dataInicio: dataPublicacaoOriginal});

        const idDefPrazoOposicao = houveOposicao ? 'PRAZO_OPOSICAO_COM_OPOSICAO_01' : 'PRAZO_OPOSICAO_SEM_OPOSICAO_01';
        const defPrazoOposicao = encontrarDefinicaoEtapaPorId(idDefPrazoOposicao);
        if (defPrazoOposicao) {
          const dataFimPrazoOposicao = new Date(dataPublicacaoOriginal);
          dataFimPrazoOposicao.setDate(dataFimPrazoOposicao.getDate() + 60);
          novaTimeline.push({
            idOriginal: defPrazoOposicao.id, etapaDefinicao: defPrazoOposicao, status: 'CONCLUIDA',
            dataInicio: dataPublicacaoOriginal, dataFim: dataFimPrazoOposicao
          });

          if (houveOposicao) {
            const dataNotificacaoOpo = encontrarDataNotificacaoOposicaoMaisRecente(despachosAteIndeferimento, dataPublicacaoOriginal);
            if (dataNotificacaoOpo) {
                const defPrazoManifestacao = encontrarDefinicaoEtapaPorId('PRAZO_MANIFESTACAO_OPOSICAO_01');
                if (defPrazoManifestacao) {
                    const dataFimPrazoManifestacao = new Date(dataNotificacaoOpo);
                    dataFimPrazoManifestacao.setDate(dataFimPrazoManifestacao.getDate() + 60);
                    novaTimeline.push({
                        idOriginal: defPrazoManifestacao.id, etapaDefinicao: defPrazoManifestacao, status: 'CONCLUIDA',
                        dataInicio: dataNotificacaoOpo, dataFim: dataFimPrazoManifestacao
                    });

                    const dataManifestacaoApresentada = encontrarDataProtocoloManifestacao(despachosAteIndeferimento.filter(d => {
                        const dtProtocolo = d.ProtocoloDespacho?.[0]?.data ? new Date(d.ProtocoloDespacho[0].data) : null;
                        return dtProtocolo && dtProtocolo > dataNotificacaoOpo && dtProtocolo <= dataFimPrazoManifestacao;
                    }));
                    if (dataManifestacaoApresentada) {
                        const defManifestacaoApresentada = encontrarDefinicaoEtapaPorId('MANIFESTACAO_OPOSICAO_APRESENTADA_01');
                        if (defManifestacaoApresentada) {
                            novaTimeline.push({
                                idOriginal: defManifestacaoApresentada.id, etapaDefinicao: defManifestacaoApresentada, status: 'CONCLUIDA',
                                dataInicio: dataManifestacaoApresentada
                            });
                        }
                    }
                }
            }
          }
        }
      }
    }

    // 3. Adicionar etapa de Indeferimento como CONCLUIDA
    const defIndeferimento = encontrarDefinicaoEtapaPorId('INDEFERIMENTO_PEDIDO_01');
    if (defIndeferimento) {
      novaTimeline.push({ 
        idOriginal: defIndeferimento.id, 
        etapaDefinicao: defIndeferimento, 
        status: 'CONCLUIDA', 
        dataInicio: dataDespacho, 
        dataFim: dataDespacho 
      });
    }

    // 4. Adicionar etapa de Prazo para Recurso como ATUAL
    const defPrazoRecurso = encontrarDefinicaoEtapaPorId('PRAZO_RECURSO_INDEFERIMENTO_01');
    if (defPrazoRecurso) {
      const dataFimPrazoRecurso = new Date(dataDespacho);
      dataFimPrazoRecurso.setDate(dataFimPrazoRecurso.getDate() + 60);
      novaTimeline.push({
        idOriginal: defPrazoRecurso.id,
        etapaDefinicao: defPrazoRecurso,
        status: 'ATUAL',
        dataInicio: dataDespacho,
        dataFim: dataFimPrazoRecurso,
        exigenciaInfo: null, // Exigência já foi tratada/limpa do estado
      });
    }
    
    // Limpa a timeline de duplicatas de concluídas, mantendo a primeira ocorrência.
    const etapasVistas = new Set<string>();
    const timelineFiltradaFinal = [];
    for (const etapa of novaTimeline) {
        if (etapa.status === 'CONCLUIDA') {
            if (!etapasVistas.has(etapa.idOriginal)) {
                timelineFiltradaFinal.push(etapa);
                etapasVistas.add(etapa.idOriginal);
            }
        } else {
            timelineFiltradaFinal.push(etapa); // Adiciona não concluídas (ATUAL, PROXIMA)
        }
    }

    return {
      timelineAtualizada: timelineFiltradaFinal.sort((a,b) => (a.dataInicio?.getTime() || 0) - (b.dataInicio?.getTime() || 0) ),
      estadoAtualizado: { ...novoEstado, etapaAtualIdentificada: true }, // Usa o novoEstado modificado
      finalizarProcessamento: false, 
    };
  }

  return { 
    timelineAtualizada: timelineAnterior, 
    estadoAtualizado: estadoAnterior, // Retorna estadoAnterior original se não entrou no if
    finalizarProcessamento: false 
  };
}

export function processarApresentacaoRecursoIndeferimentoHandler(params: HandlerParams): HandlerResultado {
  const { despacho, dataDespacho, processo, despachosOrdenados, timelineAnterior, estadoAnterior, temDataDeposito } = params;

  // Condição para acionar este handler: é um despacho "Notificação de recurso" (publicação na RPI)
  // E a etapa anterior na timeline é o prazo para recurso contra indeferimento (ou seja, o recurso é tempestivo)
  // E o processo ainda não tem uma concessão ou um arquivamento por falta de pagamento.
  const etapaAnteriorImediata = timelineAnterior.length > 0 ? timelineAnterior[timelineAnterior.length -1] : null;
  
  if (
    isDespachoDeApresentacaoRecursoIndeferimento(despacho) && // Verifica se o despacho atual é uma notificação de apresentação de recurso
    // Verifica se a timeline anterior realmente estava no prazo de recurso como etapa ATUAL.
    (timelineAnterior.some(e => e.idOriginal === 'PRAZO_RECURSO_INDEFERIMENTO_01' && e.status === 'ATUAL')) && 
    !estadoAnterior.dataConcessao && 
    !estadoAnterior.arquivadoPorFaltaPagamentoTaxa
  ) {
    let novaTimeline = [...timelineAnterior];
    // Usar a data do despacho "Notificação de recurso" que é quando o recurso é publicado na RPI
    // Esta é a data oficial de apresentação do recurso
    const dataApresentacaoRecursoReal = dataDespacho;

    // 1. Marcar PRAZO_RECURSO_INDEFERIMENTO_01 como CONCLUIDA
    // Encontra a etapa de prazo de recurso que estava ATUAL e a conclui.
    const prazoRecursoIndex = novaTimeline.findIndex(e => e.idOriginal === 'PRAZO_RECURSO_INDEFERIMENTO_01' && e.status === 'ATUAL');
    if (prazoRecursoIndex !== -1) {
      // CORREÇÃO: Manter o dataFim original (prazo limite) em vez de usar a data de apresentação
      novaTimeline[prazoRecursoIndex] = {
        ...novaTimeline[prazoRecursoIndex],
        status: 'CONCLUIDA',
        // dataFim já está correto (prazo limite de 60 dias), não alterar
      };
    } else {
      // Se não encontrou o prazo como ATUAL, mas o despacho de apresentação de recurso existe,
      // pode ser um recurso tardio ou uma situação não prevista. Adiciona o prazo como concluído retroativamente.
      const defPrazoRecurso = encontrarDefinicaoEtapaPorId('PRAZO_RECURSO_INDEFERIMENTO_01');
      const indeferimentoAnterior = timelineAnterior.find(e => e.idOriginal === 'INDEFERIMENTO_PEDIDO_01');
      if (defPrazoRecurso && indeferimentoAnterior && indeferimentoAnterior.dataInicio) {
        const dataInicioPrazo = indeferimentoAnterior.dataInicio;
        const dataFimPrazoLimite = new Date(dataInicioPrazo);
        dataFimPrazoLimite.setDate(dataFimPrazoLimite.getDate() + 60); // Prazo limite correto
        // Insere antes das etapas de recurso, mas após o indeferimento
        const insertIndex = indeferimentoAnterior ? novaTimeline.findIndex(e => e.idOriginal === indeferimentoAnterior.idOriginal) + 1 : novaTimeline.length;
        novaTimeline.splice(insertIndex, 0, {
            idOriginal: defPrazoRecurso.id,
            etapaDefinicao: defPrazoRecurso,
            status: 'CONCLUIDA',
            dataInicio: dataInicioPrazo,
            dataFim: dataFimPrazoLimite, // CORREÇÃO: Usar prazo limite, não data de apresentação
        });
      }
    }

    // 2. Adicionar RECURSO_INDEFERIMENTO_APRESENTADO_01 como CONCLUIDA
    const defRecursoApresentado = encontrarDefinicaoEtapaPorId('RECURSO_INDEFERIMENTO_APRESENTADO_01');
    if (defRecursoApresentado) {
      // Remove qualquer ocorrência anterior para evitar duplicatas
      novaTimeline = novaTimeline.filter(e => e.idOriginal !== defRecursoApresentado.id);
      novaTimeline.push({
        idOriginal: defRecursoApresentado.id,
        etapaDefinicao: defRecursoApresentado,
        status: 'CONCLUIDA',
        dataInicio: dataApresentacaoRecursoReal,
        dataFim: dataApresentacaoRecursoReal, // Evento instantâneo
      });
    }

    // 3. Adicionar ANALISE_RECURSO_INDEFERIMENTO_01 como ATUAL
    const defAnaliseRecurso = encontrarDefinicaoEtapaPorId('ANALISE_RECURSO_INDEFERIMENTO_01');
    if (defAnaliseRecurso) {
      // Remove qualquer ocorrência anterior para evitar duplicatas
      novaTimeline = novaTimeline.filter(e => e.idOriginal !== defAnaliseRecurso.id);
      const dataInicioAnalise = dataApresentacaoRecursoReal;
      const dataFimEstimadaAnalise = new Date(dataInicioAnalise);
      dataFimEstimadaAnalise.setDate(dataFimEstimadaAnalise.getDate() + 567); // TODO: Usar estimativa do BD

      novaTimeline.push({
        idOriginal: defAnaliseRecurso.id,
        etapaDefinicao: defAnaliseRecurso,
        status: 'ATUAL',
        dataInicio: dataInicioAnalise,
        dataFim: dataFimEstimadaAnalise, 
        observacaoGerada: defAnaliseRecurso.prazoProximaEtapaDescricao || `Estimativa de decisão para ${formatarData(dataFimEstimadaAnalise)}.`
      });
    }

    return {
      timelineAtualizada: novaTimeline.sort((a,b) => (a.dataInicio?.getTime() || 0) - (b.dataInicio?.getTime() || 0)),
      estadoAtualizado: { ...estadoAnterior, etapaAtualIdentificada: true }, 
      finalizarProcessamento: false, // Permite que o loop principal de despachos continue para processar despachos subsequentes.
    };
  }

  return { 
    timelineAtualizada: timelineAnterior, 
    estadoAtualizado: estadoAnterior, 
    finalizarProcessamento: false 
  };
}

export function processarDecisaoRecursoHandler(params: HandlerParams): HandlerResultado {
  const { despacho, dataDespacho, processo, despachosOrdenados, timelineAnterior, estadoAnterior, temDataDeposito } = params;

  // const etapaAtualTimeline = timelineAnterior.find(e => e.status === 'ATUAL'); // Não depende mais SÓ da etapa atual.

  const recursoApresentadoConcluido = timelineAnterior.some(
    e => e.idOriginal === 'RECURSO_INDEFERIMENTO_APRESENTADO_01' && e.status === 'CONCLUIDA'
  );
  const decisaoRecursoJaTomada = timelineAnterior.some(
    e => (e.idOriginal === 'RECURSO_PROVIDO_TRANSICAO_DEFERIMENTO_01' || e.idOriginal === 'RECURSO_NEGADO_01') && e.status === 'CONCLUIDA'
  );

  // Condição: Despacho é de decisão de recurso E um recurso foi apresentado E ainda não foi decidido
  // E o processo ainda não foi finalizado por concessão/arquivamento prioritário.
  if (
    (isDespachoDeRecursoProvido(despacho) || isDespachoDeRecursoNaoProvido(despacho)) &&
    recursoApresentadoConcluido &&
    !decisaoRecursoJaTomada &&
    !estadoAnterior.dataConcessao &&
    !estadoAnterior.arquivadoPorFaltaPagamentoTaxa && 
    !timelineAnterior.some(e => e.idOriginal === 'ARQUIVAMENTO_POS_RECURSO_NEGADO_01' && e.status === 'ATUAL') // Evitar reprocessar se já arquivado por recurso negado
  ) {
    let novaTimeline = [...timelineAnterior];
    const novoEstado = { ...estadoAnterior };

    // 1. Concluir a etapa que estava ATUAL, se houver e não for uma etapa final já.
    const etapaAtualIndex = novaTimeline.findIndex(e => e.status === 'ATUAL');
    if (etapaAtualIndex !== -1) {
      const etapaAtual = novaTimeline[etapaAtualIndex];
      // Só conclui se não for uma etapa que já indica um fim de fluxo (como arquivamento)
      if (etapaAtual.idOriginal !== 'ARQUIVAMENTO_01' && 
          etapaAtual.idOriginal !== 'ARQUIVAMENTO_FALTA_PAGAMENTO_TAXA_01' &&
          etapaAtual.idOriginal !== 'ARQUIVAMENTO_POS_RECURSO_NEGADO_01') {
        novaTimeline[etapaAtualIndex] = {
          ...etapaAtual,
          status: 'CONCLUIDA',
          dataFim: dataDespacho,
          observacaoGerada: (etapaAtual.observacaoGerada || "") + " Decisão de recurso superveniente."
        };
      }
    }

    // 2. Concluir a etapa de Análise de Recurso (ANALISE_RECURSO_INDEFERIMENTO_01) se ela existir e não estiver concluída.
    const analiseRecursoIndex = novaTimeline.findIndex(e => e.idOriginal === 'ANALISE_RECURSO_INDEFERIMENTO_01' && e.status !== 'CONCLUIDA');
    if (analiseRecursoIndex !== -1) {
      novaTimeline[analiseRecursoIndex] = {
        ...novaTimeline[analiseRecursoIndex],
        status: 'CONCLUIDA',
        dataFim: dataDespacho, // A análise se conclui com o despacho de decisão
      };
    } else if (!novaTimeline.some(e => e.idOriginal === 'ANALISE_RECURSO_INDEFERIMENTO_01' && e.status === 'CONCLUIDA')){ // Adiciona se não existe E não está como CONCLUIDA
        // Se ANALISE_RECURSO_INDEFERIMENTO_01 não existe na timeline ou não está concluída, adiciona/atualiza como concluída.
        // Isso pode acontecer se uma exigência em grau de recurso foi a última etapa ATUAL antes da decisão.
        const defAnaliseRecurso = encontrarDefinicaoEtapaPorId('ANALISE_RECURSO_INDEFERIMENTO_01');
        const recursoApresentadoEtapa = novaTimeline.find(e => e.idOriginal === 'RECURSO_INDEFERIMENTO_APRESENTADO_01' && e.status === 'CONCLUIDA');
        if (defAnaliseRecurso && recursoApresentadoEtapa && recursoApresentadoEtapa.dataInicio) {
            // Remove qualquer instância anterior de ANALISE_RECURSO_INDEFERIMENTO_01 para evitar duplicatas
            novaTimeline = novaTimeline.filter(e => e.idOriginal !== 'ANALISE_RECURSO_INDEFERIMENTO_01');
            novaTimeline.push({
                idOriginal: defAnaliseRecurso.id, etapaDefinicao: defAnaliseRecurso,
                status: 'CONCLUIDA', 
                dataInicio: recursoApresentadoEtapa.dataInicio, // Começa quando o recurso foi apresentado
                dataFim: dataDespacho // Termina com a decisão
            });
        }
    }

    if (isDespachoDeRecursoProvido(despacho)) {
      // CENÁRIO: RECURSO PROVIDO
      const defRecursoProvidoTrans = encontrarDefinicaoEtapaPorId('RECURSO_PROVIDO_TRANSICAO_DEFERIMENTO_01');
      if (defRecursoProvidoTrans) {
        novaTimeline.push({ 
          idOriginal: defRecursoProvidoTrans.id, etapaDefinicao: defRecursoProvidoTrans, 
          status: 'CONCLUIDA', dataInicio: dataDespacho, dataFim: dataDespacho 
        });
      }

      const defDeferimentoPosRecurso = encontrarDefinicaoEtapaPorId('DEFERIMENTO_POS_RECURSO_01');
      if (defDeferimentoPosRecurso) {
        novaTimeline.push({ 
          idOriginal: defDeferimentoPosRecurso.id, etapaDefinicao: defDeferimentoPosRecurso, 
          status: 'CONCLUIDA', dataInicio: dataDespacho, dataFim: dataDespacho 
        });
        novoEstado.dataDeferimento = dataDespacho; // IMPORTANTE: Seta a data de deferimento para o fluxo de pagamento funcionar
      }

      const defPrazoPagamento = encontrarDefinicaoEtapaPorId('PRAZO_PAGAMENTO_TAXA_CONCESSAO_ORDINARIO_01');
      if (defPrazoPagamento) {
        const dataFimPrazoPagamento = new Date(dataDespacho);
        dataFimPrazoPagamento.setDate(dataFimPrazoPagamento.getDate() + 60);
        novaTimeline.push({
          idOriginal: defPrazoPagamento.id, etapaDefinicao: defPrazoPagamento,
          status: 'ATUAL', dataInicio: dataDespacho, dataFim: dataFimPrazoPagamento,
          exigenciaInfo: null // Limpa exigências anteriores
        });
      }
      novoEstado.etapaAtualIdentificada = true;
      // Não finalizar o processamento para permitir que despachos subsequentes (como o de Concessão) sejam processados.
      return { timelineAtualizada: novaTimeline, estadoAtualizado: novoEstado, finalizarProcessamento: false };

    } else if (isDespachoDeRecursoNaoProvido(despacho)) {
      // CENÁRIO: RECURSO NÃO PROVIDO (NEGADO)
      const defRecursoNegado = encontrarDefinicaoEtapaPorId('RECURSO_NEGADO_01');
      if (defRecursoNegado) {
        novaTimeline.push({ 
          idOriginal: defRecursoNegado.id, etapaDefinicao: defRecursoNegado, 
          status: 'CONCLUIDA', dataInicio: dataDespacho, dataFim: dataDespacho 
        });
      }

      const defArquivamentoPosRecurso = encontrarDefinicaoEtapaPorId('ARQUIVAMENTO_POS_RECURSO_NEGADO_01');
      if (defArquivamentoPosRecurso) {
        novaTimeline.push({
          idOriginal: defArquivamentoPosRecurso.id, etapaDefinicao: defArquivamentoPosRecurso,
          status: 'ATUAL', dataInicio: dataDespacho,
          exigenciaInfo: null, // Limpa exigências anteriores
          observacaoGerada: defArquivamentoPosRecurso.statusDetalhado // Adiciona observação sobre o arquivamento
        });
      }
      novoEstado.etapaAtualIdentificada = true;
      novoEstado.exigenciaAtiva = null; // Garante que não há exigência ativa após arquivamento
      // Poderíamos setar um estado como `arquivadoPorRecursoNegado: dataDespacho` se necessário para outros handlers.
      return { timelineAtualizada: novaTimeline, estadoAtualizado: novoEstado, finalizarProcessamento: true };
    }
  }

  // Se não for um despacho de decisão de recurso relevante ou as condições não forem atendidas
  return { 
    timelineAtualizada: timelineAnterior, 
    estadoAtualizado: estadoAnterior, 
    finalizarProcessamento: false 
  };
}

export function processarArquivamentoPorRecursoNaoApresentadoPosLoopHandler(params: PosLoopHandlerParams): HandlerResultado {
  const { processo, despachosOrdenados, timelineAnterior, estadoAtual, temDataDeposito, hoje } = params;
  const novoEstado = { ...estadoAtual };
  let novaTimeline = [...timelineAnterior];

  // Condições para acionar este handler:
  // 1. Não há concessão ou arquivamento por falta de pagamento (processo ainda "aberto" para esta lógica).
  // 2. A timeline anterior indica que a última ação significativa foi um indeferimento seguido de um prazo para recurso.
  // 3. O prazo para recurso já expirou.
  // 4. Nenhum recurso foi de fato apresentado (verificado pela ausência da etapa RECURSO_INDEFERIMENTO_APRESENTADO_01).

  const etapaPrazoRecurso = novaTimeline.find(e => e.idOriginal === 'PRAZO_RECURSO_INDEFERIMENTO_01' && e.status === 'ATUAL');
  const indeferimentoOcorreu = novaTimeline.some(e => e.idOriginal === 'INDEFERIMENTO_PEDIDO_01' && e.status === 'CONCLUIDA');
  const recursoFoiApresentado = novaTimeline.some(e => e.idOriginal === 'RECURSO_INDEFERIMENTO_APRESENTADO_01' && e.status === 'CONCLUIDA');

  if (
    !novoEstado.dataConcessao &&
    !novoEstado.arquivadoPorFaltaPagamentoTaxa &&
    indeferimentoOcorreu &&
    etapaPrazoRecurso && // Garante que o prazo de recurso era a etapa atual
    etapaPrazoRecurso.dataFim && // Garante que o prazo tem uma data fim calculada
    hoje > etapaPrazoRecurso.dataFim && // O prazo expirou
    !recursoFoiApresentado && // Nenhum recurso foi apresentado
    true // Placeholder para manter a estrutura do if, já que a linha acima foi comentada
  ) {
    // Modifica a timeline para refletir o arquivamento por não apresentação de recurso
    // 1. Marcar PRAZO_RECURSO_INDEFERIMENTO_01 como CONCLUIDA
    const prazoRecursoIndex = novaTimeline.findIndex(e => e.idOriginal === 'PRAZO_RECURSO_INDEFERIMENTO_01' && e.status === 'ATUAL');
    if (prazoRecursoIndex !== -1) {
      novaTimeline[prazoRecursoIndex] = {
        ...novaTimeline[prazoRecursoIndex],
        status: 'CONCLUIDA',
        // dataFim já está correta, marcando o fim do prazo
      };
    }

    // 2. Adicionar AGUARDANDO_ARQUIVAMENTO_SEM_RECURSO_01 como ATUAL
    const defAguardandoArquivamento = encontrarDefinicaoEtapaPorId('AGUARDANDO_ARQUIVAMENTO_SEM_RECURSO_01');
    if (defAguardandoArquivamento) {
      novaTimeline = novaTimeline.filter(e => e.status === 'CONCLUIDA' || e.idOriginal === 'PRAZO_RECURSO_INDEFERIMENTO_01');
      
      const dataInicioAguardando = etapaPrazoRecurso.dataFim; 
      const dataFimEstimadaAguardando = new Date(dataInicioAguardando!);
      dataFimEstimadaAguardando.setDate(dataFimEstimadaAguardando.getDate() + 30); 

      const defArquivamentoFinal = encontrarDefinicaoEtapaPorId('ARQUIVAMENTO_RECURSO_NAO_APRESENTADO_01');

      if (hoje > dataFimEstimadaAguardando && defArquivamentoFinal) {
        // Cenário 2: Prazo de "Aguardando Arquivamento" também expirou, então arquiva de vez.
        novaTimeline.push({
          idOriginal: defAguardandoArquivamento.id,
          etapaDefinicao: defAguardandoArquivamento,
          status: 'CONCLUIDA',
          dataInicio: dataInicioAguardando,
          dataFim: dataFimEstimadaAguardando, 
          exigenciaInfo: null,
          observacaoGerada: defAguardandoArquivamento.prazoProximaEtapaDescricao
        });
        novaTimeline.push({
          idOriginal: defArquivamentoFinal.id,
          etapaDefinicao: defArquivamentoFinal,
          status: 'ATUAL',
          dataInicio: dataFimEstimadaAguardando, // Arquivamento começa quando o período de espera termina
        });
      } else {
        // Cenário 1: Prazo de recurso expirou, mas ainda estamos no período de "Aguardando Arquivamento".
        novaTimeline.push({
          idOriginal: defAguardandoArquivamento.id,
          etapaDefinicao: defAguardandoArquivamento,
          status: 'ATUAL',
          dataInicio: dataInicioAguardando,
          dataFim: dataFimEstimadaAguardando, 
          exigenciaInfo: null,
          observacaoGerada: defAguardandoArquivamento.prazoProximaEtapaDescricao
        });
        if (defArquivamentoFinal) {
          novaTimeline.push({
            idOriginal: defArquivamentoFinal.id,
            etapaDefinicao: defArquivamentoFinal,
            status: 'PROXIMA_ESTIMADA',
            dataInicio: dataFimEstimadaAguardando,
          });
        }
      }
    }

    novoEstado.etapaAtualIdentificada = true;
    novoEstado.exigenciaAtiva = null; 
    // Poderíamos adicionar um campo como `arquivadoPorNaoApresentacaoRecurso: etapaPrazoRecurso.dataFim` ao novoEstado se necessário.
    
    return {
      timelineAtualizada: novaTimeline,
      estadoAtualizado: novoEstado,
      finalizarProcessamento: true, // Este é um estado final para o fluxo de recurso.
    };
  }

  return {
    timelineAtualizada: timelineAnterior,
    estadoAtualizado: novoEstado,
    finalizarProcessamento: false,
  };
}

// --- HANDLERS PARA FLUXO DE NULIDADE E RENOVAÇÃO ---

export function processarInstauracaoNulidadeHandler(params: HandlerParams): HandlerResultado {
  const { despacho, dataDespacho, timelineAnterior, estadoAnterior, processo, despachosOrdenados, temDataDeposito } = params;
  const novoEstado = { ...estadoAnterior };
  let novaTimeline = [...timelineAnterior];

  // Condição: Despacho de instauração de nulidade E processo já concedido e não arquivado.
  if (
    isDespachoDeInstauracaoNulidade(despacho) &&
    novoEstado.dataConcessao && 
    !novoEstado.arquivadoPorFaltaPagamentoTaxa &&
    !novaTimeline.some(e => e.idOriginal === 'ARQUIVAMENTO_01' && e.status === 'ATUAL')
  ) {
    // Limpar etapas futuras ou atuais que seriam invalidadas pela instauração da nulidade.
    // Mantém CONCLUIDAS, exceto aquelas que são parte de um fluxo de nulidade anterior que não se completou.
    novaTimeline = novaTimeline.filter(e => 
        e.status === 'CONCLUIDA' || 
        e.idOriginal === 'CONCESSAO_REGISTRO_01' || 
        e.idOriginal === 'PROTOCOLO_01' || 
        e.idOriginal.startsWith('PUBLICACAO_') || 
        e.idOriginal.startsWith('PRAZO_OPOSICAO_') ||
        e.idOriginal.startsWith('PRAZO_MANIFESTACAO_OPOSICAO_') ||
        e.idOriginal.startsWith('MANIFESTACAO_OPOSICAO_') ||
        e.idOriginal.startsWith('ANALISE_MERITO_') ||
        e.idOriginal.startsWith('DEFERIMENTO_') ||
        e.idOriginal.startsWith('PRAZO_PAGAMENTO_TAXA_') ||
        e.idOriginal.startsWith('TAXA_CONCESSAO_PAGA_') ||
        e.idOriginal === 'SOBRESTAMENTO_01'
    );

    // 1. Marcar FIM_PRAZO_NULIDADE_01 como CONCLUIDA (se existia) ou adicionar se não existia
    const fimPrazoNulidadeIndex = novaTimeline.findIndex(e => e.idOriginal === 'FIM_PRAZO_NULIDADE_01');
    if (fimPrazoNulidadeIndex !== -1) {
      // Se já existe, marcar como concluída
      novaTimeline[fimPrazoNulidadeIndex] = {
        ...novaTimeline[fimPrazoNulidadeIndex],
        status: 'CONCLUIDA',
        observacaoGerada: "Processo de nulidade instaurado antes do fim do prazo."
      };
    } else {
        // Se não existia, adicionar como CONCLUIDA
        const etapaConcessao = timelineAnterior.find(e => e.idOriginal === 'CONCESSAO_REGISTRO_01');
        if (etapaConcessao && etapaConcessao.dataInicio) {
            const defFimPrazoNul = encontrarDefinicaoEtapaPorId('FIM_PRAZO_NULIDADE_01');
            if (defFimPrazoNul) {
                // Calcular a data correta (180 dias da concessão)
                const dataFimPrazoNulidade = new Date(etapaConcessao.dataInicio);
                dataFimPrazoNulidade.setDate(dataFimPrazoNulidade.getDate() + 180);
                novaTimeline.push({
                    idOriginal: defFimPrazoNul.id, etapaDefinicao: defFimPrazoNul, status: 'CONCLUIDA',
                    dataInicio: dataFimPrazoNulidade, dataFim: dataFimPrazoNulidade,
                    observacaoGerada: "Processo de nulidade instaurado antes do fim do prazo."
                });
            }
        }
    }
    // Remover apenas FIM_PRAZO_NULIDADE_01, pois a nulidade interrompe esse fluxo específico
    // A RENOVACAO_01 deve ser mantida, pois é independente do processo de nulidade
    novaTimeline = novaTimeline.filter(e => e.idOriginal !== 'FIM_PRAZO_NULIDADE_01');

    // 2. Adicionar PROCESSO_NULIDADE_INSTAURADO_01 como CONCLUIDA
    const defNulidadeInstaurada = encontrarDefinicaoEtapaPorId('PROCESSO_NULIDADE_INSTAURADO_01');
    if (defNulidadeInstaurada) {
      novaTimeline.push({
        idOriginal: defNulidadeInstaurada.id,
        etapaDefinicao: defNulidadeInstaurada,
        status: 'CONCLUIDA',
        dataInicio: dataDespacho,
        dataFim: dataDespacho, // Evento instantâneo
      });
    }

    // 3. Adicionar PRAZO_MANIFESTACAO_NULIDADE_01 como ATUAL
    const defPrazoManifestacaoNulidade = encontrarDefinicaoEtapaPorId('PRAZO_MANIFESTACAO_NULIDADE_01');
    if (defPrazoManifestacaoNulidade) {
      const dataFimPrazoManifestacao = new Date(dataDespacho);
      dataFimPrazoManifestacao.setDate(dataFimPrazoManifestacao.getDate() + 60);
      novaTimeline.push({
        idOriginal: defPrazoManifestacaoNulidade.id,
        etapaDefinicao: defPrazoManifestacaoNulidade,
        status: 'ATUAL',
        dataInicio: dataDespacho,
        dataFim: dataFimPrazoManifestacao,
        exigenciaInfo: null, // Limpa exigências anteriores
      });
    }
    
    // 4. Garantir que RENOVACAO_01 existe como PROXIMA_ESTIMADA (independente da nulidade)
    if (novoEstado.dataConcessao) {
      const renovacaoExiste = novaTimeline.some(e => e.idOriginal === 'RENOVACAO_01');
      if (!renovacaoExiste) {
        const defRenovacao = encontrarDefinicaoEtapaPorId('RENOVACAO_01');
        if (defRenovacao) {
          const dataConcessaoOriginal = new Date(novoEstado.dataConcessao);
          let anosAdicionais = 10; // Por padrão, para o segundo decênio
          
          if (novoEstado.dataUltimaRenovacaoConcluida) {
            anosAdicionais = 20;
          }
          
          const dataInicioProximaJanela = new Date(dataConcessaoOriginal);
          dataInicioProximaJanela.setFullYear(dataConcessaoOriginal.getFullYear() + anosAdicionais);
          
          const observacaoRenovacao = `Próxima janela de renovação. Registro válido até ${formatarData(dataInicioProximaJanela)}, calculado a partir da data de concessão (${formatarData(dataConcessaoOriginal)}).`;
          
          novaTimeline.push({
            idOriginal: defRenovacao.id,
            etapaDefinicao: defRenovacao,
            status: 'PROXIMA_ESTIMADA',
            dataInicio: dataInicioProximaJanela,
            observacaoGerada: observacaoRenovacao
          });
        }
      }
    }
    
    // Ordenar a timeline pelas datas de início após as manipulações
    novaTimeline.sort((a, b) => (a.dataInicio?.getTime() || 0) - (b.dataInicio?.getTime() || 0));

    novoEstado.etapaAtualIdentificada = true; // A etapa atual agora é o prazo de manifestação à nulidade.
    // Pode-se adicionar um campo ao estado para indicar que um processo de nulidade está ativo, ex: `nulidadeEmCurso: true`

    return {
      timelineAtualizada: novaTimeline,
      estadoAtualizado: novoEstado,
      finalizarProcessamento: false, // Alterado para false para permitir handlers pós-loop
    };
  }

  return {
    timelineAtualizada: timelineAnterior,
    estadoAtualizado: novoEstado,
    finalizarProcessamento: false,
  };
}

export function processarFimPrazoNulidadeSemInstauracaoHandler(params: PosLoopHandlerParams): HandlerResultado {
  const { timelineAnterior, estadoAtual, hoje, processo, temDataDeposito } = params;
  const novoEstado = { ...estadoAtual };
  let novaTimeline = [...timelineAnterior];

  const fimPrazoNulidadeIndex = novaTimeline.findIndex(e => e.idOriginal === 'FIM_PRAZO_NULIDADE_01');
  const fimPrazoNulidadeEtapa = fimPrazoNulidadeIndex !== -1 ? novaTimeline[fimPrazoNulidadeIndex] : null;

  const nulidadeInstaurada = novaTimeline.some(e => e.idOriginal === 'PROCESSO_NULIDADE_INSTAURADO_01');

  if (
    novoEstado.dataConcessao &&
    !nulidadeInstaurada &&
    fimPrazoNulidadeEtapa &&
    fimPrazoNulidadeEtapa.dataInicio &&
    hoje > fimPrazoNulidadeEtapa.dataInicio &&
    fimPrazoNulidadeEtapa.status === 'PROXIMA_EXATA'
  ) {
    // Marcar FIM_PRAZO_NULIDADE_01 como CONCLUIDA
    if(fimPrazoNulidadeIndex !== -1) {
        novaTimeline[fimPrazoNulidadeIndex] = {
            ...novaTimeline[fimPrazoNulidadeIndex],
            status: 'CONCLUIDA',
            observacaoGerada: "Prazo para instauração de nulidade encerrado sem pedidos."
        };
    }

    // Garantir que RENOVACAO_01 seja adicionada/atualizada como PROXIMA_ESTIMADA
    if (novoEstado.dataConcessao) { 
        const defRenovacao = encontrarDefinicaoEtapaPorId('RENOVACAO_01');
        if (defRenovacao) {
            const dataConcessaoOriginal = new Date(novoEstado.dataConcessao);
            let anosAdicionais = 10; // Por padrão, para o segundo decênio

            if (estadoAtual.dataUltimaRenovacaoConcluida) { 
              anosAdicionais = 20;
            }
            
            const dataInicioProximaJanela = new Date(dataConcessaoOriginal);
            dataInicioProximaJanela.setFullYear(dataConcessaoOriginal.getFullYear() + anosAdicionais);

            const observacaoRenovacao = `Próxima janela de renovação. Registro válido até ${formatarData(dataInicioProximaJanela)}, calculado a partir da data de concessão (${formatarData(dataConcessaoOriginal)}).`;

            novaTimeline = novaTimeline.filter(e => !(e.idOriginal === 'RENOVACAO_01' && e.status === 'PROXIMA_ESTIMADA'));

            novaTimeline.push({
                idOriginal: defRenovacao.id,
                etapaDefinicao: defRenovacao,
                status: 'PROXIMA_ESTIMADA',
                dataInicio: dataInicioProximaJanela,
                observacaoGerada: observacaoRenovacao
            });
        }
    }

    // Remover duplicatas e ordenar
    const etapasVistas = new Set<string>();
    const timelineFiltrada: EtapaTimelineGerada[] = [];
    novaTimeline.forEach(etapa => {
        const chave = `${etapa.idOriginal}-${etapa.status}`; // Chave considera o status
        let adicionar = true;
        if (etapa.status === 'CONCLUIDA') {
            if (etapasVistas.has(etapa.idOriginal)) { // Para concluídas, só checa o ID original
                adicionar = false;
            } else {
                etapasVistas.add(etapa.idOriginal);
            }
        }
        // Para ATUAL ou PROXIMA, sempre tenta adicionar, a limpeza final pode tratar melhor os conflitos de ID
        // ou a lógica de remoção antes de adicionar (como feito para RENOVACAO_01) já previne duplicatas com mesmo status.

        if(adicionar) {
            timelineFiltrada.push(etapa);
        }
    });
    timelineFiltrada.sort((a,b) => (a.dataInicio?.getTime() || 0) - (b.dataInicio?.getTime() || 0));
    
    // Não definir etapaAtualIdentificada = true aqui, deixar o limparTimelineFinal ou fluxo padrão decidir
    // novoEstado.etapaAtualIdentificada = false; // Resetar se este handler não define uma ATUAL clara
    // Ou melhor, preservar o que veio de estadoAtual se não mexemos:
    novoEstado.etapaAtualIdentificada = estadoAtual.etapaAtualIdentificada;


    return {
      timelineAtualizada: timelineFiltrada,
      estadoAtualizado: novoEstado,
      finalizarProcessamento: true, 
    };
  }

  return {
    timelineAtualizada: timelineAnterior,
    estadoAtualizado: { ...novoEstado, etapaAtualIdentificada: estadoAtual.etapaAtualIdentificada },
    finalizarProcessamento: false,
  };
}

export function processarManifestacaoNulidadeHandler(params: HandlerParams): HandlerResultado {
  const { despacho, dataDespacho, timelineAnterior, estadoAnterior } = params; // Removido 'hoje'
  const novoEstado = { ...estadoAnterior };
  let novaTimeline = [...timelineAnterior];

  const etapaAtualTimeline = novaTimeline.find(e => e.status === 'ATUAL');

  if (
    etapaAtualTimeline?.idOriginal === 'PRAZO_MANIFESTACAO_NULIDADE_01' && // Corrigido ID da etapa
    isDespachoDeManifestacaoNulidade(despacho) &&
    etapaAtualTimeline.dataInicio && dataDespacho >= etapaAtualTimeline.dataInicio &&
    etapaAtualTimeline.dataFim && dataDespacho <= etapaAtualTimeline.dataFim
  ) {
    const prazoManifestacaoIndex = novaTimeline.findIndex(e => e.idOriginal === 'PRAZO_MANIFESTACAO_NULIDADE_01' && e.status === 'ATUAL');
    if (prazoManifestacaoIndex !== -1) {
      // Se a manifestação foi apresentada, removemos a etapa de prazo que estava ATUAL.
      novaTimeline.splice(prazoManifestacaoIndex, 1);
    }

    const defManifestacaoApresentada = encontrarDefinicaoEtapaPorId('MANIFESTACAO_NULIDADE_APRESENTADA_01');
    if (defManifestacaoApresentada) {
      novaTimeline = novaTimeline.filter(e => e.idOriginal !== defManifestacaoApresentada.id);
      novaTimeline.push({
        idOriginal: defManifestacaoApresentada.id,
        etapaDefinicao: defManifestacaoApresentada,
        status: 'ATUAL', // Mudou para ATUAL - agora aguarda decisão diretamente
        dataInicio: dataDespacho,
        dataFim: dataDespacho,
        observacaoGerada: "Manifestação apresentada. Aguardando decisão do INPI sobre a nulidade."
      });
    }
    
    novaTimeline.sort((a, b) => (a.dataInicio?.getTime() || 0) - (b.dataInicio?.getTime() || 0));
    novoEstado.etapaAtualIdentificada = true;

    return {
      timelineAtualizada: novaTimeline,
      estadoAtualizado: novoEstado,
      finalizarProcessamento: true,
    };
  }

  return {
    timelineAtualizada: timelineAnterior,
    estadoAtualizado: novoEstado,
    finalizarProcessamento: false,
  };
}

export function processarExpiracaoPrazoManifestacaoNulidadeHandler(params: PosLoopHandlerParams): HandlerResultado {
  const { timelineAnterior, estadoAtual, hoje } = params;
  const novoEstado = { ...estadoAtual };
  let novaTimeline = [...timelineAnterior];

  const prazoManifestacaoIndex = novaTimeline.findIndex(e => e.idOriginal === 'PRAZO_MANIFESTACAO_NULIDADE_01' && e.status === 'ATUAL');
  const prazoManifestacaoEtapa = prazoManifestacaoIndex !== -1 ? novaTimeline[prazoManifestacaoIndex] : null;

  if (
    prazoManifestacaoEtapa &&
    prazoManifestacaoEtapa.dataFim &&
    hoje > prazoManifestacaoEtapa.dataFim &&
    !novaTimeline.some(e => e.idOriginal === 'MANIFESTACAO_NULIDADE_APRESENTADA_01' && e.dataInicio && prazoManifestacaoEtapa.dataFim && e.dataInicio <= prazoManifestacaoEtapa.dataFim)
  ) {
    if (prazoManifestacaoIndex !== -1) {
        novaTimeline[prazoManifestacaoIndex] = {
            ...novaTimeline[prazoManifestacaoIndex],
            status: 'ATUAL', // Mantém como ATUAL aguardando decisão
            observacaoGerada: "Prazo para manifestação sobre nulidade expirou sem apresentação. Aguardando decisão do INPI."
        };
          novoEstado.etapaAtualIdentificada = true;
    }
    
    novaTimeline.sort((a, b) => (a.dataInicio?.getTime() || 0) - (b.dataInicio?.getTime() || 0));

    return {
      timelineAtualizada: novaTimeline,
      estadoAtualizado: novoEstado,
      finalizarProcessamento: true,
    };
  }

  return {
    timelineAtualizada: timelineAnterior,
    estadoAtualizado: novoEstado,
    finalizarProcessamento: false,
  };
}

export function processarDecisaoNulidadeHandler(params: HandlerParams): HandlerResultado {
  const { despacho, dataDespacho, timelineAnterior, estadoAnterior, processo, despachosOrdenados, temDataDeposito } = params;
  const novoEstado = { ...estadoAnterior };
  let novaTimeline = [...timelineAnterior];

  // Pré-condição: só atua se for um despacho de decisão de nulidade E o registro já foi concedido.
  if (!(isDespachoDeConcessaoMantidaPosNulidade(despacho) || isDespachoDeRegistroAnulado(despacho)) || !novoEstado.dataConcessao) {
    return { timelineAtualizada: timelineAnterior, estadoAtualizado: novoEstado, finalizarProcessamento: false };
  }

  // Usar a data da RPI do despacho de decisão como a data efetiva.
  const dataEfetivaDecisaoNulidade = despacho.RPI?.dataPublicacao ? new Date(despacho.RPI.dataPublicacao) : dataDespacho; // Fallback para dataDespacho se RPI não disponível

  // Encontrar o despacho de instauração de nulidade para obter a data correta de instauração (data da RPI da instauração).
  const despachoInstauracaoNulidadeOriginal = despachosOrdenados.find(d => isDespachoDeInstauracaoNulidade(d));
  let dataRealInstauracaoNulidade: Date | null = null;
  if (despachoInstauracaoNulidadeOriginal?.RPI?.dataPublicacao) {
    dataRealInstauracaoNulidade = new Date(despachoInstauracaoNulidadeOriginal.RPI.dataPublicacao);
  } else if (despachoInstauracaoNulidadeOriginal) { // Fallback se não tiver RPI.dataPublicacao, usar a data do próprio despacho de instauração
      const dataProtocoloInst = despachoInstauracaoNulidadeOriginal.ProtocoloDespacho?.[0]?.data;
      const dataRPIInst = despachoInstauracaoNulidadeOriginal.RPI?.dataPublicacao; // Adicionado para caso de ter RPI mas não protocolo
      if (dataProtocoloInst) dataRealInstauracaoNulidade = new Date(dataProtocoloInst);
      else if (dataRPIInst) dataRealInstauracaoNulidade = new Date(dataRPIInst);
  }

  // Se não houver data de instauração, ou se a data efetiva da decisão for anterior à instauração,
  // a decisão não pode ser processada logicamente (ou os dados estão inconsistentes).
  if (!dataRealInstauracaoNulidade || dataEfetivaDecisaoNulidade < dataRealInstauracaoNulidade) {
    // console.warn("Decisão de nulidade não pode ser processada: data de instauração ausente ou decisão anterior à instauração.");
    return { timelineAtualizada: timelineAnterior, estadoAtualizado: novoEstado, finalizarProcessamento: false };
  }

  // Assegurar que as etapas prévias da nulidade estão concluídas
  // 1. Concluir FIM_PRAZO_NULIDADE_01 (da concessão original)
  const fimPrazoNulConcessaoIdx = novaTimeline.findIndex(e => e.idOriginal === 'FIM_PRAZO_NULIDADE_01');
  if (fimPrazoNulConcessaoIdx !== -1) {
    if (novaTimeline[fimPrazoNulConcessaoIdx].status === 'PROXIMA_EXATA') {
        novaTimeline[fimPrazoNulConcessaoIdx] = {...novaTimeline[fimPrazoNulConcessaoIdx], status: 'CONCLUIDA', observacaoGerada: "Processo de nulidade instaurado antes do fim do prazo."};
    }
  } else if (novoEstado.dataConcessao) { // Adicionar se ausente e houve concessao
      const defFimPrazoNul = encontrarDefinicaoEtapaPorId('FIM_PRAZO_NULIDADE_01');
      if (defFimPrazoNul) {
          // Calcular a data correta (180 dias da concessão)
          const dataFimPrazoNulidade = new Date(novoEstado.dataConcessao);
          dataFimPrazoNulidade.setDate(dataFimPrazoNulidade.getDate() + 180);
          novaTimeline.push({ idOriginal: defFimPrazoNul.id, etapaDefinicao: defFimPrazoNul, status: 'CONCLUIDA', dataInicio: dataFimPrazoNulidade, dataFim: dataFimPrazoNulidade, observacaoGerada: "Processo de nulidade instaurado antes do fim do prazo." });
      }
  }

  // 2. Adicionar/Concluir PROCESSO_NULIDADE_INSTAURADO_01
  const defNulInstaurada = encontrarDefinicaoEtapaPorId('PROCESSO_NULIDADE_INSTAURADO_01');
  if (defNulInstaurada) {
    novaTimeline = novaTimeline.filter(e => e.idOriginal !== defNulInstaurada.id); 
    novaTimeline.push({ idOriginal: defNulInstaurada.id, etapaDefinicao: defNulInstaurada, status: 'CONCLUIDA', dataInicio: dataRealInstauracaoNulidade, dataFim: dataRealInstauracaoNulidade });
  }

  // 3. Adicionar/Concluir PRAZO_MANIFESTACAO_NULIDADE_01
  const defPrazoManif = encontrarDefinicaoEtapaPorId('PRAZO_MANIFESTACAO_NULIDADE_01');
  if (defPrazoManif) {
    // Calcular a data fim correta (60 dias da instauração)
    const dataFimPrazoManifestacao = new Date(dataRealInstauracaoNulidade);
    dataFimPrazoManifestacao.setDate(dataFimPrazoManifestacao.getDate() + 60);
    
    novaTimeline = novaTimeline.filter(e => e.idOriginal !== defPrazoManif.id);
    novaTimeline.push({ idOriginal: defPrazoManif.id, etapaDefinicao: defPrazoManif, status: 'CONCLUIDA', dataInicio: dataRealInstauracaoNulidade, dataFim: dataFimPrazoManifestacao, observacaoGerada: "Prazo para manifestação sobre nulidade concluído pela decisão." });
  }

  // Remover apenas FIM_PRAZO_NULIDADE_01 pois o fluxo de nulidade altera esse caminho específico
  // A RENOVACAO_01 deve ser mantida, pois é independente do resultado da nulidade
  novaTimeline = novaTimeline.filter(e => e.idOriginal !== 'FIM_PRAZO_NULIDADE_01');

  // Agora, processar a decisão em si
  if (isDespachoDeConcessaoMantidaPosNulidade(despacho)) {
    const defConcessaoMantida = encontrarDefinicaoEtapaPorId('CONCESSAO_MANTIDA_POS_NULIDADE_01');
    if (defConcessaoMantida) {
      novaTimeline.push({ idOriginal: defConcessaoMantida.id, etapaDefinicao: defConcessaoMantida, status: 'ATUAL', dataInicio: dataEfetivaDecisaoNulidade });
    }
    const defRenovacao = encontrarDefinicaoEtapaPorId('RENOVACAO_01');
    if (defRenovacao && novoEstado.dataConcessao) { 
      const dataConcessaoOriginal = new Date(novoEstado.dataConcessao);
      let anosAdicionais = 10; // Por padrão, para o segundo decênio

      // Se já houve uma renovação concluída, a próxima é para o terceiro decênio
      if (novoEstado.dataUltimaRenovacaoConcluida) {
        anosAdicionais = 20;
      }
      
      const dataInicioProximaJanela = new Date(dataConcessaoOriginal);
      dataInicioProximaJanela.setFullYear(dataConcessaoOriginal.getFullYear() + anosAdicionais);

      const observacaoRenovacao = `Próxima janela de renovação. Registro válido até ${formatarData(dataInicioProximaJanela)}, calculado a partir da data de concessão (${formatarData(dataConcessaoOriginal)}).`;

      // Remover qualquer RENOVACAO_01 PROXIMA_ESTIMADA existente para evitar duplicatas
      novaTimeline = novaTimeline.filter(e => !(e.idOriginal === 'RENOVACAO_01' && e.status === 'PROXIMA_ESTIMADA'));

      novaTimeline.push({ 
        idOriginal: defRenovacao.id, 
        etapaDefinicao: defRenovacao, 
        status: 'PROXIMA_ESTIMADA', 
        dataInicio: dataInicioProximaJanela, 
        observacaoGerada: observacaoRenovacao 
      });
    }
  } else if (isDespachoDeRegistroAnulado(despacho)) {
    const jaHouveAnulacaoConcluidaAnteriormente = timelineAnterior.some(
        e => e.idOriginal === 'REGISTRO_ANULADO_01' && e.status === 'CONCLUIDA' && e.dataFim && e.dataFim < dataEfetivaDecisaoNulidade
      );

    if (jaHouveAnulacaoConcluidaAnteriormente) { // É um segundo recurso sendo negado / segunda decisão de anulação
      const defRecursoNulidadeNegado = encontrarDefinicaoEtapaPorId('RECURSO_NULIDADE_NEGADO_01');
      if (defRecursoNulidadeNegado) {
        novaTimeline.push({ idOriginal: defRecursoNulidadeNegado.id, etapaDefinicao: defRecursoNulidadeNegado, status: 'ATUAL', dataInicio: dataEfetivaDecisaoNulidade });
        const defArquivamento = encontrarDefinicaoEtapaPorId('ARQUIVAMENTO_01');
        if (defArquivamento) {
          const dataEstimadaArquivamento = new Date(dataEfetivaDecisaoNulidade);
          dataEstimadaArquivamento.setDate(dataEstimadaArquivamento.getDate() + 30);
          novaTimeline.push({ idOriginal: defArquivamento.id, etapaDefinicao: defArquivamento, status: 'PROXIMA_ESTIMADA', dataInicio: dataEstimadaArquivamento, observacaoGerada: "Arquivamento estimado após negação do recurso de nulidade." });
        }
      }
    } else { // Primeira anulação
      const defRegistroAnulado = encontrarDefinicaoEtapaPorId('REGISTRO_ANULADO_01');
      if (defRegistroAnulado) {
        novaTimeline.push({ idOriginal: defRegistroAnulado.id, etapaDefinicao: defRegistroAnulado, status: 'ATUAL', dataInicio: dataEfetivaDecisaoNulidade });
      }
      const defPrazoManifNulidadeRecurso = encontrarDefinicaoEtapaPorId('PRAZO_MANIFESTACAO_NULIDADE_01'); 
      if (defPrazoManifNulidadeRecurso) {
        const dataFimPrazoManifRecurso = new Date(dataEfetivaDecisaoNulidade);
        dataFimPrazoManifRecurso.setDate(dataFimPrazoManifRecurso.getDate() + 60);
        novaTimeline.push({ idOriginal: defPrazoManifNulidadeRecurso.id, etapaDefinicao: defPrazoManifNulidadeRecurso, status: 'PROXIMA_EXATA', dataInicio: dataEfetivaDecisaoNulidade, dataFim: dataFimPrazoManifRecurso, observacaoGerada: "Prazo para novo recurso/manifestação contra anulação." });
      }
    }
  }

  // Garantir que RENOVACAO_01 existe independentemente do resultado da nulidade
  if (novoEstado.dataConcessao) {
    const renovacaoExiste = novaTimeline.some(e => e.idOriginal === 'RENOVACAO_01');
    if (!renovacaoExiste) {
      const defRenovacao = encontrarDefinicaoEtapaPorId('RENOVACAO_01');
      if (defRenovacao) {
        const dataConcessaoOriginal = new Date(novoEstado.dataConcessao);
        let anosAdicionais = 10; // Por padrão, para o segundo decênio
        
        if (novoEstado.dataUltimaRenovacaoConcluida) {
          anosAdicionais = 20;
        }
        
        const dataInicioProximaJanela = new Date(dataConcessaoOriginal);
        dataInicioProximaJanela.setFullYear(dataConcessaoOriginal.getFullYear() + anosAdicionais);
        
        const observacaoRenovacao = `Próxima janela de renovação. Registro válido até ${formatarData(dataInicioProximaJanela)}, calculado a partir da data de concessão (${formatarData(dataConcessaoOriginal)}).`;
        
        novaTimeline.push({
          idOriginal: defRenovacao.id,
          etapaDefinicao: defRenovacao,
          status: 'PROXIMA_ESTIMADA',
          dataInicio: dataInicioProximaJanela,
          observacaoGerada: observacaoRenovacao
        });
      }
    }
  }

  const etapasConcluidasVistas = new Set<string>();
  const timelineFinalFiltrada: EtapaTimelineGerada[] = [];
  novaTimeline.forEach(etapa => {
    if (etapa.status === 'CONCLUIDA') {
      if (!etapasConcluidasVistas.has(etapa.idOriginal)) {
        timelineFinalFiltrada.push(etapa);
        etapasConcluidasVistas.add(etapa.idOriginal);
      }
    } else {
      timelineFinalFiltrada.push(etapa); 
    }
  });
  timelineFinalFiltrada.sort((a, b) => (a.dataInicio?.getTime() || 0) - (b.dataInicio?.getTime() || 0));
  
  novoEstado.etapaAtualIdentificada = true;
  return {
    timelineAtualizada: timelineFinalFiltrada,
    estadoAtualizado: novoEstado,
    finalizarProcessamento: true,
  };
}

export function processarArquivamentoPosRecursoNulidadeNegadoHandler(params: PosLoopHandlerParams): HandlerResultado {
  const { timelineAnterior, estadoAtual, hoje } = params;
  const novoEstado = { ...estadoAtual };
  let novaTimeline = [...timelineAnterior];

  const recursoNegadoIndex = novaTimeline.findIndex(e => e.idOriginal === 'RECURSO_NULIDADE_NEGADO_01' && e.status === 'ATUAL');
  const recursoNegadoEtapa = recursoNegadoIndex !== -1 ? novaTimeline[recursoNegadoIndex] : null;

  // Condição: Etapa atual é RECURSO_NULIDADE_NEGADO_01 e o prazo estimado para arquivamento passou.
  if (recursoNegadoEtapa && recursoNegadoEtapa.dataInicio) {
    const dataPublicacaoRecursoNegado = recursoNegadoEtapa.dataInicio;
    const dataEstimadaArquivamento = new Date(dataPublicacaoRecursoNegado);
    dataEstimadaArquivamento.setDate(dataEstimadaArquivamento.getDate() + 30); 

    // Se hoje já passou da data estimada para arquivamento
    // E ainda não há um ARQUIVAMENTO_01 ATUAL (para evitar reprocessar se já foi arquivado por despacho explícito)
    if (hoje > dataEstimadaArquivamento && 
        !novaTimeline.some(e => e.idOriginal === 'ARQUIVAMENTO_01' && e.status === 'ATUAL')) {
      
      // Marcar RECURSO_NULIDADE_NEGADO_01 como CONCLUIDA
      if (recursoNegadoIndex !== -1) {
        novaTimeline[recursoNegadoIndex] = {
          ...novaTimeline[recursoNegadoIndex],
          status: 'CONCLUIDA',
          dataFim: dataEstimadaArquivamento, 
          observacaoGerada: "Prazo para arquivamento após recurso de nulidade negado atingido."
        };
      }

      // Adicionar ARQUIVAMENTO_01 como ATUAL
      const defArquivamento = encontrarDefinicaoEtapaPorId('ARQUIVAMENTO_01');
      if (defArquivamento) {
        // Remover qualquer PROXIMA_ESTIMADA de ARQUIVAMENTO_01 para não duplicar
        novaTimeline = novaTimeline.filter(e => !(e.idOriginal === 'ARQUIVAMENTO_01' && e.status === 'PROXIMA_ESTIMADA'));
        
        novaTimeline.push({
          idOriginal: defArquivamento.id,
          etapaDefinicao: defArquivamento,
          status: 'ATUAL',
          dataInicio: dataEstimadaArquivamento,
          observacaoGerada: "Arquivado por decurso de prazo após recurso de nulidade negado."
        });
        novoEstado.etapaAtualIdentificada = true;
      }
      
      novaTimeline.sort((a,b)=>(a.dataInicio?.getTime() || 0) - (b.dataInicio?.getTime() || 0));
      return { timelineAtualizada: novaTimeline, estadoAtualizado: novoEstado, finalizarProcessamento: true };
    }
  }

  return {
    timelineAtualizada: timelineAnterior,
    estadoAtualizado: novoEstado,
    finalizarProcessamento: false,
  };
}

export function processarRenovacoesAnterioresHandler(params: HandlerParams): HandlerResultado {
  const { despacho, dataDespacho, timelineAnterior, estadoAnterior } = params;
  let novaTimeline = [...timelineAnterior];
  const novoEstado = { ...estadoAnterior };

  const eRenovacaoOrdinaria = isDespachoDeRenovacaoPagaOrdinaria(despacho);
  const eRenovacaoExtraordinaria = isDespachoDeRenovacaoPagaExtraordinaria(despacho);

  if (eRenovacaoOrdinaria || eRenovacaoExtraordinaria) {
    const tipoRenovacao = eRenovacaoOrdinaria ? 'RENOVACAO_CONCLUIDA_ORDINARIA_01' : 'RENOVACAO_CONCLUIDA_EXTRAORDINARIA_01';
    const defRenovacaoConcluida = encontrarDefinicaoEtapaPorId(tipoRenovacao);

    if (defRenovacaoConcluida) {
      // Marcar RENOVACAO_01 anterior como CONCLUIDA se existir e sua dataFim for esta renovação
      const renovacaoAnteriorIndex = novaTimeline.findIndex(e => 
        e.idOriginal === 'RENOVACAO_01' && 
        e.dataFim && 
        e.dataFim.getTime() === dataDespacho.getTime()
      );
      if (renovacaoAnteriorIndex !== -1) {
        novaTimeline[renovacaoAnteriorIndex].status = 'CONCLUIDA';
        novaTimeline[renovacaoAnteriorIndex].observacaoGerada = (novaTimeline[renovacaoAnteriorIndex].observacaoGerada || "") + ` Renovação efetivada em ${formatarData(dataDespacho)}.`;
      }

      // Adicionar a etapa de renovação concluída
      const observacaoEstaRenovacao = `Renovação efetivada em ${formatarData(dataDespacho)}. Próxima renovação em 10 anos.`;
      novaTimeline.push({
        idOriginal: defRenovacaoConcluida.id,
        etapaDefinicao: defRenovacaoConcluida,
        status: 'CONCLUIDA',
        dataInicio: dataDespacho,
        dataFim: dataDespacho, // Data de fim é a mesma data da renovação concluída
        observacaoGerada: observacaoEstaRenovacao
      });
    }

    novoEstado.dataUltimaRenovacaoConcluida = dataDespacho;

    // Remover QUALQUER etapa RENOVACAO_01 com status PROXIMA_ESTIMADA,
    // para garantir que será recalculada e adicionada com a data correta abaixo.
    novaTimeline = novaTimeline.filter(e => !(e.idOriginal === 'RENOVACAO_01' && e.status === 'PROXIMA_ESTIMADA'));

    // Adicionar a PRÓXIMA etapa de RENOVACAO_01 (PROXIMA_ESTIMADA)
    const defRenovacaoFutura = encontrarDefinicaoEtapaPorId('RENOVACAO_01');
    if (defRenovacaoFutura && novoEstado.dataConcessao) {
      const dataConcessaoOriginal = new Date(novoEstado.dataConcessao);
      
      // A contagem é feita na timeline que JÁ TEM a renovação concluída que acabamos de processar.
      const renovacoesConcluidasCount = novaTimeline.filter(e => 
          (e.idOriginal === 'RENOVACAO_CONCLUIDA_ORDINARIA_01' || e.idOriginal === 'RENOVACAO_CONCLUIDA_EXTRAORDINARIA_01') &&
          e.status === 'CONCLUIDA'
      ).length;

      const dataInicioProximaJanela = new Date(dataConcessaoOriginal);
      // O próximo vencimento (dataInicio da RENOVACAO_01 estimada) é dataConcessao + (N+1)*10 anos, onde N é o número de renovações JÁ CONCLUÍDAS.
      dataInicioProximaJanela.setFullYear(dataConcessaoOriginal.getFullYear() + (renovacoesConcluidasCount) * 10); // Correção: (N) * 10 para o início da próxima janela
      
      // A validade é dataConcessao + (N+1)*10
      const dataValidadeRegistro = new Date(dataConcessaoOriginal);
      dataValidadeRegistro.setFullYear(dataConcessaoOriginal.getFullYear() + (renovacoesConcluidasCount + 1) * 10);


      const observacaoRenovacao = `Próxima janela de renovação. Registro válido até ${formatarData(dataValidadeRegistro)}, calculado a partir da data de concessão (${formatarData(dataConcessaoOriginal)}).`;

      novaTimeline.push({
        idOriginal: defRenovacaoFutura.id,
        etapaDefinicao: defRenovacaoFutura,
        status: 'PROXIMA_ESTIMADA',
        dataInicio: dataInicioProximaJanela, // Início da *janela* de renovação
        dataFim: dataValidadeRegistro, // Validade do registro (fim da próxima janela)
        observacaoGerada: observacaoRenovacao
      });
    }
    // Ordenar a timeline pelas datas de início, pois adicionamos etapas.
    novaTimeline.sort((a, b) => (a.dataInicio?.getTime() || 0) - (b.dataInicio?.getTime() || 0));
  } // Fim do if (eRenovacaoOrdinaria || eRenovacaoExtraordinaria)

  return {
    timelineAtualizada: novaTimeline,
    estadoAtualizado: novoEstado,
    finalizarProcessamento: false,
  };
}

// Novo handler para adicionar análise de recurso apenas quando apropriado
export function processarAnaliseRecursoSemApresentacaoPosLoopHandler(params: PosLoopHandlerParams): HandlerResultado {
  const { processo, despachosOrdenados, timelineAnterior, estadoAtual, temDataDeposito, hoje } = params;
  const novoEstado = { ...estadoAtual };
  const novaTimeline = [...timelineAnterior];

  // Condições para acionar este handler:
  // 1. Não há concessão ou arquivamento por falta de pagamento
  // 2. Há um indeferimento concluído na timeline
  // 3. A etapa atual é PRAZO_RECURSO_INDEFERIMENTO_01
  // 4. A análise de recurso ainda não foi adicionada

  const etapaPrazoRecurso = novaTimeline.find(e => e.idOriginal === 'PRAZO_RECURSO_INDEFERIMENTO_01' && e.status === 'ATUAL');
  const indeferimentoOcorreu = novaTimeline.some(e => e.idOriginal === 'INDEFERIMENTO_PEDIDO_01' && e.status === 'CONCLUIDA');
  const recursoFoiApresentado = novaTimeline.some(e => e.idOriginal === 'RECURSO_INDEFERIMENTO_APRESENTADO_01' && e.status === 'CONCLUIDA');
  const analiseRecursoJaExiste = novaTimeline.some(e => e.idOriginal === 'ANALISE_RECURSO_INDEFERIMENTO_01');

  if (
    !novoEstado.dataConcessao &&
    !novoEstado.arquivadoPorFaltaPagamentoTaxa &&
    indeferimentoOcorreu &&
    etapaPrazoRecurso && // Garante que o prazo de recurso é a etapa atual
    !analiseRecursoJaExiste // A análise de recurso ainda não foi adicionada
  ) {
    // Verificar se o prazo já expirou
    const prazoJaExpirou = etapaPrazoRecurso.dataFim && hoje > etapaPrazoRecurso.dataFim;
    
    // Só adicionar análise de recurso se:
    // - O prazo ainda não expirou OU
    // - Há recurso apresentado (independentemente do prazo)
    const deveAdicionarAnalise = !prazoJaExpirou || recursoFoiApresentado;
    
    if (deveAdicionarAnalise && etapaPrazoRecurso.dataFim) {
      // Adicionar ANALISE_RECURSO_INDEFERIMENTO_01 como PROXIMA_ESTIMADA
      const defAnaliseRecurso = encontrarDefinicaoEtapaPorId('ANALISE_RECURSO_INDEFERIMENTO_01');
      if (defAnaliseRecurso) {
        const dataInicioAnalise = new Date(etapaPrazoRecurso.dataFim);
        const dataFimEstimadaAnalise = new Date(dataInicioAnalise);
        dataFimEstimadaAnalise.setDate(dataFimEstimadaAnalise.getDate() + 567); // Estimativa padrão

        const observacao = recursoFoiApresentado 
          ? "Análise de recurso após apresentação formal."
          : "Análise de recurso será iniciada após fim do prazo.";

        novaTimeline.push({
          idOriginal: defAnaliseRecurso.id,
          etapaDefinicao: defAnaliseRecurso,
          status: 'PROXIMA_ESTIMADA',
          dataInicio: dataInicioAnalise,
          dataFim: dataFimEstimadaAnalise,
          observacaoGerada: observacao
        });

        // Ordenar timeline por data de início
        novaTimeline.sort((a, b) => (a.dataInicio?.getTime() || 0) - (b.dataInicio?.getTime() || 0));
      }
    }
    // Se prazo expirou SEM recurso apresentado, NÃO adiciona análise
    // O marcador vermelho será exibido pela lógica existente da timeline
  }

  return {
    timelineAtualizada: novaTimeline,
    estadoAtualizado: novoEstado,
    finalizarProcessamento: false,
  };
}

/**
 * Verifica se já existe uma etapa que representa o resultado do mérito
 * (deferimento, indeferimento, concessão, arquivamento)
 */
function jaMeritoFoiProcessado(timeline: EtapaTimelineGerada[]): boolean {
  return timeline.some(etapa => {
    const idOriginal = etapa.idOriginal;
    const nome = etapa.etapaDefinicao.nomeOriginal.toLowerCase();
    
    // Verificar por IDs específicos de resultados de mérito
    const resultadosMerito = [
      'DEFERIMENTO_PEDIDO_01',
      'DEFERIMENTO_POS_RECURSO_01', 
      'INDEFERIMENTO_PEDIDO_01',
      'CONCESSAO_REGISTRO_01',
      'ARQUIVAMENTO_01'
    ];
    
    if (resultadosMerito.includes(idOriginal)) {
      return true;
    }
    
    // Verificar por nomes que indicam resultado de mérito
    const palavrasResultadoMerito = [
      'deferimento',
      'indeferimento', 
      'concessão',
      'arquivamento'
    ];
    
    return palavrasResultadoMerito.some(palavra => nome.includes(palavra));
  });
  }
