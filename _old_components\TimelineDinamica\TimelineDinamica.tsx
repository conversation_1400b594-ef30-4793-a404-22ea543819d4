import React from 'react';
import { EtapaTimelineGerada } from '@/lib/timelineNovasTypes';
import EtapaItem from './EtapaItem';
import { calcularProgressoTimeline } from './timelineDinamica.utils'; // Importar a nova função
// Poderíamos adicionar um hook para detecção de mobile aqui se preferir não passar isMobile como prop
// import { useMediaQuery } from 'react-responsive'; // Exemplo

interface TimelineDinamicaProps {
  etapas: EtapaTimelineGerada[];
  isMobile?: boolean; // Ou detectar internamente
  // Adicionar props para controle de progresso se necessário, ex: progressoAtualPercent: number;
}

const TimelineDinamica: React.FC<TimelineDinamicaProps> = ({ etapas, isMobile: isMobileProp }) => {
  // Exemplo de como usar um hook para detectar se é mobile, caso não queira passar a prop
  // const isMobileDevice = useMediaQuery({ query: '(max-width: 768px)' });
  // const currentIsMobile = isMobileProp !== undefined ? isMobileProp : isMobileDevice;

  // Por simplicidade, vamos usar a prop isMobileProp por enquanto
  const currentIsMobile = isMobileProp || false;
    console.log('etapas', etapas);
  if (!etapas || etapas.length === 0) {
    return (
      <div className="p-4 text-center text-gray-500">
        Nenhuma etapa para exibir na timeline.
      </div>
    );
  }

  const progressoTotalPercent = calcularProgressoTimeline(etapas);
  const numeroDeEtapas = etapas.length;

  return (
    <div className={`relative w-full ${currentIsMobile ? 'py-6 px-2' : 'py-10 px-4'} min-h-[120px]`}> {/* Padding vertical reduzido, min-h reduzido */}
      {/* Linha de fundo (Trilha) */}
      <div className="absolute top-1/2 left-0 w-full h-[2px] bg-gray-300 transform -translate-y-1/2" /> {/* Cor simplificada */}

      {/* Barra de progresso azul */}
      <div 
        className="absolute top-1/2 left-0 h-[6px] bg-blue-500 transform -translate-y-1/2 transition-all duration-500 ease-in-out" // Cor e altura simplificadas, sombra removida
        style={{ width: `${progressoTotalPercent}%` }}
      />

      {/* Container para os Itens da Etapa */}
      <div className="relative flex justify-between items-start w-full h-full">
        {etapas.map((etapa, index) => {
          const posicaoPercent = numeroDeEtapas <= 1 ? 50 : (100 / (Math.max(1, numeroDeEtapas - 1))) * index;
          
          return (
            <div 
              key={`${etapa.idOriginal}-${index}-${etapa.status}`}
              className="absolute top-1/2 transform -translate-y-1/2 z-10" // Adicionado z-10 para garantir que marcadores fiquem sobre a linha de progresso
              style={{
                left: `${posicaoPercent}%`,
                transform: `translateX(-${posicaoPercent === 0 ? 0 : posicaoPercent === 100 ? 100 : 50}%) translateY(-50%)`,
              }}
            >
              <EtapaItem
                etapa={etapa}
                isMobile={currentIsMobile}
              />
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default TimelineDinamica; 