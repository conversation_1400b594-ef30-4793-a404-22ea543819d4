'use client';

import React from 'react';
import ProcessoCard from '@/components/ProcessoCard';

interface BuscaResultados {
  processos: any[];
  totalProcessos: number;
  currentPage: number;
  totalPages: number;
  pageSize: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  filtrosAplicados: any;
}

interface ProcessoResultadosProps {
  resultados: BuscaResultados | null;
  loading: boolean;
  onChangePage: (page: number) => void;
  onExportar?: () => void;
}

const ProcessoResultados: React.FC<ProcessoResultadosProps> = ({ 
  resultados, 
  loading, 
  onChangePage,
  onExportar 
}) => {
  if (loading) {
    return (
      <div className="bg-white p-8 rounded-lg shadow-md">
        <div className="flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mr-3"></div>
          <span className="text-gray-600">Buscando processos...</span>
        </div>
      </div>
    );
  }

  if (!resultados) {
    return (
      <div className="bg-white p-8 rounded-lg shadow-md text-center">
        <div className="text-gray-500">
          <svg className="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Realizar busca</h3>
          <p className="text-gray-500">Use os filtros acima para buscar processos no sistema</p>
        </div>
      </div>
    );
  }

  if (resultados.processos.length === 0) {
    return (
      <div className="bg-white p-8 rounded-lg shadow-md text-center">
        <div className="text-gray-500">
          <svg className="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Nenhum processo encontrado</h3>
          <p className="text-gray-500">Tente ajustar os filtros de busca</p>
        </div>
      </div>
    );
  }

  const startResult = (resultados.currentPage - 1) * resultados.pageSize + 1;
  const endResult = Math.min(startResult + resultados.pageSize - 1, resultados.totalProcessos);

  return (
    <div className="space-y-6">
      {/* Header com informações e ações */}
      <div className="bg-white p-4 rounded-lg shadow-md">
        <div className="flex justify-between items-center">
          <div>
            <h3 className="text-lg font-semibold text-gray-800">
              Resultados da Busca
            </h3>
            <p className="text-sm text-gray-600">
              Mostrando {startResult}-{endResult} de {resultados.totalProcessos} processos
            </p>
          </div>
          
          <div className="flex gap-2">
            {onExportar && (
              <button
                onClick={onExportar}
                className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors font-medium"
              >
                📊 Exportar
              </button>
            )}
            
            <div className="text-sm text-gray-500 bg-gray-100 px-3 py-2 rounded-md">
              Página {resultados.currentPage} de {resultados.totalPages}
            </div>
          </div>
        </div>

        {/* Mostrar filtros aplicados */}
        {Object.keys(resultados.filtrosAplicados).some(key => 
          key !== 'page' && key !== 'pageSize' && resultados.filtrosAplicados[key]
        ) && (
          <div className="mt-3 pt-3 border-t border-gray-200">
            <span className="text-sm font-medium text-gray-700">Filtros aplicados: </span>
            <div className="flex flex-wrap gap-2 mt-1">
              {Object.entries(resultados.filtrosAplicados).map(([key, value]) => {
                if (key === 'page' || key === 'pageSize' || !value) return null;
                
                const labels: { [key: string]: string } = {
                  termo: 'Busca geral',
                  nomeMarca: 'Nome da marca',
                  numeroProcesso: 'Número do processo',
                  identificadorCliente: 'Identificador',
                  clienteId: 'ID do cliente',
                  dataInicio: 'Data início',
                  dataFim: 'Data fim',
                  apenasProcuradorRegistrese: 'REGISTRE-SE LTDA',
                };
                
                return (
                  <span 
                    key={key}
                    className={`inline-block text-xs px-2 py-1 rounded-full ${
                      key === 'apenasProcuradorRegistrese' 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-blue-100 text-blue-800'
                    }`}
                  >
                    {key === 'apenasProcuradorRegistrese' && value 
                      ? '✓ Apenas REGISTRE-SE LTDA' 
                      : `${labels[key] || key}: ${String(value)}`
                    }
                  </span>
                );
              })}
            </div>
          </div>
        )}
      </div>

      {/* Lista de processos usando ProcessoCard existente */}
      <div className="space-y-4">
        {resultados.processos.map((processo) => (
          <div key={processo.id} className="relative">
            {/* Indicador do cliente no canto superior direito */}
            {processo.Cliente && (
              <div className="absolute top-2 right-2 z-10 bg-blue-600 text-white text-xs px-2 py-1 rounded-md">
                Cliente: {processo.Cliente.identificador || processo.Cliente.nome || `ID ${processo.Cliente.id}`}
              </div>
            )}
            
            <ProcessoCard 
              processo={processo}
              estimativasMerito={null} // Pode ser passado se necessário
            />
          </div>
        ))}
      </div>

      {/* Paginação */}
      {resultados.totalPages > 1 && (
        <div className="bg-white p-4 rounded-lg shadow-md">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-700">
              <span>Página </span>
              <span className="font-medium">{resultados.currentPage}</span>
              <span> de </span>
              <span className="font-medium">{resultados.totalPages}</span>
            </div>

            <div className="flex gap-2">
              <button
                onClick={() => onChangePage(1)}
                disabled={!resultados.hasPreviousPage}
                className="px-3 py-2 text-sm border border-gray-300 rounded-md disabled:bg-gray-100 disabled:text-gray-400 disabled:cursor-not-allowed hover:bg-gray-50"
              >
                Primeira
              </button>
              
              <button
                onClick={() => onChangePage(resultados.currentPage - 1)}
                disabled={!resultados.hasPreviousPage}
                className="px-3 py-2 text-sm border border-gray-300 rounded-md disabled:bg-gray-100 disabled:text-gray-400 disabled:cursor-not-allowed hover:bg-gray-50"
              >
                ← Anterior
              </button>

              {/* Páginas próximas */}
              {Array.from({ length: Math.min(5, resultados.totalPages) }, (_, i) => {
                const pageStart = Math.max(1, resultados.currentPage - 2);
                const pageNumber = pageStart + i;
                
                if (pageNumber > resultados.totalPages) return null;
                
                return (
                  <button
                    key={pageNumber}
                    onClick={() => onChangePage(pageNumber)}
                    className={`px-3 py-2 text-sm border rounded-md ${
                      pageNumber === resultados.currentPage
                        ? 'bg-blue-600 text-white border-blue-600'
                        : 'border-gray-300 hover:bg-gray-50'
                    }`}
                  >
                    {pageNumber}
                  </button>
                );
              })}

              <button
                onClick={() => onChangePage(resultados.currentPage + 1)}
                disabled={!resultados.hasNextPage}
                className="px-3 py-2 text-sm border border-gray-300 rounded-md disabled:bg-gray-100 disabled:text-gray-400 disabled:cursor-not-allowed hover:bg-gray-50"
              >
                Próxima →
              </button>
              
              <button
                onClick={() => onChangePage(resultados.totalPages)}
                disabled={!resultados.hasNextPage}
                className="px-3 py-2 text-sm border border-gray-300 rounded-md disabled:bg-gray-100 disabled:text-gray-400 disabled:cursor-not-allowed hover:bg-gray-50"
              >
                Última
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProcessoResultados; 