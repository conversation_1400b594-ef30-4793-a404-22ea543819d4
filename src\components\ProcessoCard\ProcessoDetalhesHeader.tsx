import React from 'react';
import { format, addYears, addDays, addMonths } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { gerarTimelineSimplificada } from '@/lib/timelineLogica';
import { useProcessoStatus } from '@/hooks/useProcessoStatus';

// Componente interno para uma linha de detalhe (movido para cá)
interface DetalheRowProps {
  label1: string;
  value1: React.ReactNode;
  label2: string;
  value2: React.ReactNode;
}
const DetalheRow: React.FC<DetalheRowProps> = ({ label1, value1, label2, value2 }) => (
  <div className="py-2 grid gap-x-4 items-center pl-[45px] grid-cols-[400px_1fr]">
    <div className="flex items-center">
      <span className="text-xs font-normal text-black mr-2 inline-block w-[150px] flex-shrink-0">{label1}:</span>
      <span className="text-[15px] font-semibold text-black truncate min-w-0" title={typeof value1 === 'string' ? value1 : ''}>{value1}</span>
    </div>
    <div className="flex items-center">
      <span className="text-xs font-normal text-black mr-2 inline-block w-[90px] flex-shrink-0">{label2}:</span>
      <span className="text-[15px] font-semibold text-black truncate min-w-0" title={typeof value2 === 'string' ? value2 : ''}>{value2}</span>
    </div>
  </div>
);

// Helper para formatar data (movido para cá)
const formatDate = (dateString: string | null | undefined): string => {
  if (!dateString) return '-';
  try {
    return format(new Date(dateString), 'dd/MM/yyyy', { locale: ptBR });
  } catch (e) {
    return 'Data inválida';
  }
};

interface ProcessoDetalhesHeaderProps {
  processo: any; // Recebe o objeto processo completo
}

const ProcessoDetalhesHeader: React.FC<ProcessoDetalhesHeaderProps> = ({ processo }) => {

  // Usar o hook useProcessoStatus para obter o status calculado
  const { statusText, etapasTimeline } = useProcessoStatus(processo);
  
  // Gerar timeline para debug local
  const etapaAtual = etapasTimeline.find(etapa => etapa.status === 'ATUAL');
  
  // Lógica de preparação dos dados (movida para cá)
  const dataDeposito = formatDate(processo.dataDeposito);
  const tipoPedido = processo.Marca?.apresentacao || '-'; 
  const inicioVigencia = formatDate(processo.dataConcessao);
  const titular = processo.Titular[0]?.nomeRazaoSocial || '-'; 
  
  // Gerar timeline para debug
  const etapasTimelineGerada = gerarTimelineSimplificada(processo);
  const etapaAtualGerada = etapasTimelineGerada.find(etapa => etapa.status === 'ATUAL');
  
  // Hierarquia de prioridade para etapas concluídas (igual ao useProcessoStatus)
  const prioridadeEtapas: { [key: string]: number } = {
    // Etapas finais/decisivas (alta prioridade)
    'CONCESSAO_REGISTRO_01': 100,
    'ARQUIVAMENTO_01': 95,
    'DESISTENCIA_HOMOLOGADA_01': 95,
    'SOBRESTAMENTO_01': 95,
    'INDEFERIMENTO_PEDIDO_01': 90,
    'ARQUIVAMENTO_FALTA_PAGAMENTO_TAXA_01': 90,
    'ARQUIVAMENTO_POS_RECURSO_NEGADO_01': 90,
    'ARQUIVAMENTO_RECURSO_NAO_APRESENTADO_01': 90,
    'REGISTRO_ANULADO_01': 85,
    'RECURSO_NEGADO_01': 85,
    
    // Etapas de decisão/deferimento (média-alta prioridade)
    'DEFERIMENTO_PEDIDO_01': 80,
    'DEFERIMENTO_POS_RECURSO_01': 80,
    'RECURSO_PROVIDO_TRANSICAO_DEFERIMENTO_01': 75,
    'TAXA_CONCESSAO_PAGA_01': 70,
    
    // Etapas de processo (média prioridade)
    'RECURSO_INDEFERIMENTO_APRESENTADO_01': 60,
    'MANIFESTACAO_OPOSICAO_APRESENTADA_01': 55,
    'MANIFESTACAO_NULIDADE_APRESENTADA_01': 55,
    
    // Etapas intermediárias (baixa prioridade)
    'PUBLICACAO_01': 30,
    'PUBLICACAO_COM_OPOSICAO_01': 30,
    'PRAZO_OPOSICAO_SEM_OPOSICAO_01': 20,
    'PRAZO_OPOSICAO_COM_OPOSICAO_01': 20,
    'PROTOCOLO_01': 10
  };
  
  // Debug adicional para casos de fallback
  const isFallback = etapaAtualGerada?.idOriginal === 'LOGICA_EM_DESENVOLVIMENTO_01';
  const debugInfo = isFallback ? {
    temDespachos: processo.Despacho?.length > 0,
    quantidadeDespachos: processo.Despacho?.length || 0,
    ultimoDespacho: processo.Despacho?.length > 0 ? processo.Despacho[processo.Despacho.length - 1]?.nome : 'Nenhum',
    temDataDeposito: !!processo.dataDeposito,
    temDataPublicacao: !!processo.dataPublicacaoRPI,
    temDataConcessao: !!processo.dataConcessao,
    temOposicao: !!processo.oposicao
  } : null;
  
  // Mostrar todas as etapas para debug
  const todasEtapas = etapasTimelineGerada.map(etapa => ({
    nome: etapa.etapaDefinicao.nomeOriginal,
    status: etapa.status,
    statusSimples: etapa.etapaDefinicao.statusSimples,
    idOriginal: etapa.idOriginal,
    prioridade: prioridadeEtapas[etapa.idOriginal] || 0
  }));
  
  const etapasConcluidas = etapasTimelineGerada.filter(etapa => etapa.status === 'CONCLUIDA');
  
  const ultimaEtapaConcluida = etapasConcluidas.length > 0 ? 
    etapasConcluidas
      .sort((a, b) => {
        // Primeiro critério: prioridade da etapa
        const prioridadeA = prioridadeEtapas[a.idOriginal] || 0;
        const prioridadeB = prioridadeEtapas[b.idOriginal] || 0;
        
        if (prioridadeA !== prioridadeB) {
          return prioridadeB - prioridadeA; // Maior prioridade primeiro
        }
        
        // Segundo critério: data mais recente (dentro da mesma prioridade)
        const dataA = a.dataFim || a.dataInicio || new Date(0);
        const dataB = b.dataFim || b.dataInicio || new Date(0);
        return dataB.getTime() - dataA.getTime();
      })[0] : null;
  
  // Prioridade: última concluída > etapa atual
  const etapaUsadaParaStatus = ultimaEtapaConcluida || etapaAtualGerada;
  
  let prorrogacaoOrdinaria = '-';
  let prorrogacaoExtraordinaria = '-';

  if (processo.dataConcessao) {
      try {
        const dataConcessaoObj = new Date(processo.dataConcessao);
        const dataFimVigencia = addYears(dataConcessaoObj, 10); // Data de Vigência (Fim)

        // --- Cálculo Prazo Ordinário (Revisado) ---
        const prazoOrdInicio = addDays(addYears(dataFimVigencia, -1), 1); // Dia seguinte a (Vigência - 1 ano)
        // O fim é o dia seguinte ao fim da vigência
        const prazoOrdFim = addDays(dataFimVigencia, 1);
        prorrogacaoOrdinaria = `${format(prazoOrdInicio, 'dd/MM/yyyy', { locale: ptBR })} até ${format(prazoOrdFim, 'dd/MM/yyyy', { locale: ptBR })}`;

        // --- Cálculo Prazo Extraordinário (Revisado) ---
        // O início é a própria data de fim de vigência
        const prazoExtInicio = dataFimVigencia;
        const prazoExtFim = addMonths(dataFimVigencia, 6); // Vigência + 6 meses
        prorrogacaoExtraordinaria = `${format(prazoExtInicio, 'dd/MM/yyyy', { locale: ptBR })} até ${format(prazoExtFim, 'dd/MM/yyyy', { locale: ptBR })}`;

      } catch(e) {
        console.error("Erro ao calcular datas de prorrogação:", e);
        // Mantém '-' em caso de erro
      }
  }

  const origem = `${ processo.Titular[0]?.uf ? processo.Titular[0]?.uf : ''}  / ${ processo.Titular[0]?.pais ? processo.Titular[0]?.pais : ''}` || ''; 
  const procurador = processo.Procurador?.nome || '';

  return (
    <div className="mb-6 border-t border-b border-black divide-y divide-black">
        {/* DEBUG: Etapa Atual - Comentado para produção
        <div className="py-2 bg-yellow-50 border-l-4 border-yellow-400 px-4">
          <div className="text-xs font-bold text-yellow-800 mb-1">🐛 DEBUG - Timeline:</div>
          <div className="flex gap-4 mb-2">
            <span className="text-sm">
              <strong>Etapa Atual:</strong> {etapaAtualGerada?.etapaDefinicao.nomeOriginal || 'Nenhuma etapa atual encontrada'}
            </span>
            <span className="text-sm">
              <strong>Status:</strong> {etapaAtualGerada?.etapaDefinicao.statusSimples || 'N/A'}
            </span>
          </div>
          
          {etapaUsadaParaStatus && (
            <div className="mb-2 p-2 bg-blue-50 border border-blue-200 rounded">
              <div className="text-xs font-semibold text-blue-800">
                📊 Etapa Usada para Status: 
                <span className="ml-1 font-normal">
                  {etapaUsadaParaStatus.etapaDefinicao.nomeOriginal}
                  {ultimaEtapaConcluida && etapaUsadaParaStatus === ultimaEtapaConcluida && " (última concluída)"}
                  {!ultimaEtapaConcluida && etapaUsadaParaStatus === etapaAtualGerada && " (etapa atual - fallback)"}
                </span>
              </div>
              <div className="text-xs text-blue-700 mt-1">
                <strong>Status Simples:</strong> "{etapaUsadaParaStatus.etapaDefinicao.statusSimples}"
              </div>
            </div>
          )}
          
          <div className="mb-2 p-2 bg-green-50 border border-green-200 rounded">
            <div className="text-xs font-semibold text-green-800">
              ✅ Status Final (useProcessoStatus):
            </div>
            <div className="text-xs text-green-700 mt-1 font-medium">
              "{statusText}"
            </div>
          </div>
          
          <div className="mb-2">
            <div className="text-xs font-semibold text-yellow-800 mb-1">Todas as Etapas Geradas:</div>
            <div className="text-xs space-y-1">
              {todasEtapas.map((etapa, index) => (
                <div key={index} className={`p-1 rounded ${etapa.status === 'ATUAL' ? 'bg-green-100 border border-green-300' : 'bg-gray-100'}`}>
                  <span className="font-medium">{etapa.nome}</span> 
                  <span className="ml-2 text-gray-600">({etapa.status})</span>
                  {etapa.status === 'CONCLUIDA' && (
                    <span className="ml-1 text-orange-600 text-xs">P:{etapa.prioridade}</span>
                  )}
                  <span className="ml-2 text-blue-600">→ "{etapa.statusSimples}"</span>
                </div>
              ))}
            </div>
          </div>
          
          {etapaAtualGerada?.observacaoGerada && (
            <div className="text-xs text-gray-600 mb-2">
              <strong>Obs:</strong> {etapaAtualGerada.observacaoGerada}
            </div>
          )}
          
          {isFallback && debugInfo && (
            <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-xs">
              <div className="font-bold text-red-800 mb-1">⚠️ FALLBACK DETECTADO - Dados do Processo:</div>
              <div className="grid grid-cols-2 gap-2 text-red-700">
                <span>Tem Despachos: {debugInfo.temDespachos ? 'Sim' : 'Não'} ({debugInfo.quantidadeDespachos})</span>
                <span>Tem Data Depósito: {debugInfo.temDataDeposito ? 'Sim' : 'Não'}</span>
                <span>Tem Data Publicação: {debugInfo.temDataPublicacao ? 'Sim' : 'Não'}</span>
                <span>Tem Data Concessão: {debugInfo.temDataConcessao ? 'Sim' : 'Não'}</span>
                <span>Tem Oposição: {debugInfo.temOposicao ? 'Sim' : 'Não'}</span>
              </div>
              {debugInfo.ultimoDespacho !== 'Nenhum' && (
                <div className="mt-1 text-red-600">
                  <strong>Último Despacho:</strong> {debugInfo.ultimoDespacho}
                </div>
              )}
            </div>
          )}
        </div>
        */}

        {/* Linhas existentes */}
        <DetalheRow label1="Depósito" value1={dataDeposito} label2="Tipo de marca" value2={tipoPedido} />
        <DetalheRow label1="Início da Vigência" value1={inicioVigencia} label2="Titular" value2={titular} />
        <DetalheRow label1="Prorrogação Ordinária" value1={prorrogacaoOrdinaria} label2="Origem" value2={origem} />
        <DetalheRow label1="Prorrogação Extraordinária" value1={prorrogacaoExtraordinaria} label2="Procurador" value2={procurador} />
    </div>
  );
};

export default ProcessoDetalhesHeader; 