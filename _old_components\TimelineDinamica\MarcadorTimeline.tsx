import React from 'react';
import { StatusEtapaGerada } from '@/lib/timelineNovasTypes';
import { TipoEtapa } from '@/lib/etapasConfig'; // Assegure que este caminho está correto
import { FaCheck, FaExclamationTriangle, FaHourglassHalf, FaRegFileAlt, FaCalendarAlt, FaFlag } from 'react-icons/fa'; // Exemplos de ícones

interface MarcadorTimelineProps {
  status: StatusEtapaGerada;
  tipoEtapa: TipoEtapa;
  isMobile?: boolean;
}

const MarcadorTimeline: React.FC<MarcadorTimelineProps> = ({ status, tipoEtapa, isMobile }) => {
  let bgColor = 'bg-gray-300'; // Default
  let borderColor = 'border-gray-400';
  let iconColor = 'text-gray-700'; // Default icon color
  let icon = null;
  const iconSize = isMobile ? 'w-2.5 h-2.5' : 'w-3 h-3';
  const markerSize = isMobile ? 'w-5 h-5' : 'w-6 h-6'; // Ajustado para mobile

  switch (status) {
    case 'CONCLUIDA':
      bgColor = 'bg-[#4597B5]';
      borderColor = 'border-blue-700'; // Um azul um pouco mais escuro para a borda
      icon = <FaCheck className={`text-white ${iconSize}`} />;
      break;
    case 'ATUAL':
      bgColor = 'bg-[#4597B5]'; 
      borderColor = 'border-blue-700';
      // Para 'ATUAL', podemos não usar ícone ou um diferente, para diferenciar de 'CONCLUIDA'
      // Se tipoEtapa for 'ACAO_NECESSARIA', será sobrescrito abaixo
      break;
    case 'PROXIMA_ESTIMADA':
    case 'PROXIMA_EXATA':
      bgColor = 'bg-white';
      borderColor = 'border-[#C3C3C3]';
      iconColor = 'text-gray-500'; // Ou text-[#A3A3A3]
      switch (tipoEtapa) {
        case 'PRAZO':
          icon = <FaHourglassHalf className={`${iconColor} ${iconSize}`} />;
          break;
        default:
          icon = <FaCalendarAlt className={`${iconColor} ${iconSize}`} />;
          break;
      }
      break;
    case 'ALERTA_ACAO':
      bgColor = 'bg-red-500';
      borderColor = 'border-red-700';
      icon = <FaExclamationTriangle className={`text-white ${iconSize}`} />;
      break;
    case 'FUTURA_PENDENTE':
      bgColor = 'bg-white';
      borderColor = 'border-[#C3C3C3]';
      iconColor = 'text-gray-400'; // Ou text-[#A3A3A3]
      icon = <FaRegFileAlt className={`${iconColor} ${iconSize}`} />;
      break;
    default:
      bgColor = 'bg-gray-200';
      borderColor = 'border-gray-400';
      iconColor = 'text-gray-500';
      icon = <FaFlag className={`${iconColor} ${iconSize}`} />;
      break;
  }
  
  // Caso especial para ATUAL + ACAO_NECESSARIA (para "Cumprir Exigência" etc.)
  if (status === 'ATUAL' && tipoEtapa === 'ACAO_NECESSARIA') {
    bgColor = 'bg-yellow-500'; 
    borderColor = 'border-yellow-600'; 
    // Ícone pode ser o mesmo de ALERTA_ACAO mas com cor interna diferente se necessário, ou um ícone de 'ação'
    icon = <FaExclamationTriangle className={`text-white ${iconSize}`} />; 
  }

  return (
    <div 
      className={`
        ${markerSize} 
        rounded-full 
        flex 
        items-center 
        justify-center 
        border-2 
        shadow-md 
        relative 
        z-10 
        ${bgColor} 
        ${borderColor}
      `}
      title={`Status: ${status}, Tipo: ${tipoEtapa}`} // Para debug no hover
    >
      {icon}
    </div>
  );
};

export default MarcadorTimeline; 