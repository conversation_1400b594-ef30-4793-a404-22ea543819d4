{"name": "rgse-clientes", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@prisma/client": "^6.5.0", "bcryptjs": "^3.0.2", "date-fns": "^4.1.0", "dom-to-image-more": "^3.5.0", "html2canvas": "^1.4.1", "icons": "^1.0.0", "jose": "^6.0.11", "jsonwebtoken": "^9.0.2", "jspdf": "^3.0.1", "nanoid": "^5.1.5", "next": "^15.2.4", "prisma": "^6.5.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.2.4", "tailwindcss": "^4", "typescript": "^5"}}