import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { jwtVerify } from 'jose'; // Usaremos 'jose' que é compatível com Edge Runtime

const JWT_SECRET = process.env.JWT_SECRET;

// Função para obter o payload do token (compatível com Edge Runtime)
async function verifyToken(token: string, secret: string): Promise<any | null> {
  try {
    const { payload } = await jwtVerify(
      token,
      new TextEncoder().encode(secret)
    );
    return payload;
  } catch (error) {
    console.error("Erro ao verificar token:", error);
    return null;
  }
}

// Função para verificar token admin
async function verifyAdminToken(token: string, secret: string): Promise<any | null> {
  try {
    const { payload } = await jwtVerify(
      token,
      new TextEncoder().encode(secret)
    );
    return payload;
  } catch (error) {
    console.error("Erro ao verificar token admin:", error);
    return null;
  }
}

export async function middleware(request: NextRequest) {
  const { pathname, search } = request.nextUrl;
  const requestedUrl = `${pathname}${search}`;

  console.log(`Middleware: Pathname: ${pathname}`);

  // =====================================
  // LÓGICA PARA ROTAS ADMIN (/admin/*)
  // =====================================
  if (pathname.startsWith('/admin')) {
    // Permitir acesso à página de login sem autenticação
    if (pathname === '/admin/login') {
      return NextResponse.next();
    }

    const adminToken = request.cookies.get('admin-session')?.value;
    console.log(`Middleware Admin: Token recebido: ${adminToken ? 'Presente' : 'Ausente'}`);

    // Se não há token admin, redireciona para login admin
    if (!adminToken) {
      console.log('Middleware Admin: Sem token, redirecionando para login admin.');
      const loginUrl = new URL('/admin/login', request.url);
      return NextResponse.redirect(loginUrl);
    }

    // Verificar se JWT_SECRET está configurado
    if (!JWT_SECRET) {
      console.error('Middleware Admin: JWT_SECRET não configurada!');
      return NextResponse.redirect(new URL('/admin/login', request.url)); 
    }

    // Verificar validade do token admin
    console.log(`Middleware Admin: Verificando token admin`);
    const adminPayload = await verifyAdminToken(adminToken, JWT_SECRET);

    // Se o token admin for inválido, redireciona para login admin
    if (!adminPayload) {
      console.log('Middleware Admin: Token admin inválido, redirecionando para login.');
      const response = NextResponse.redirect(new URL('/admin/login', request.url));
      response.cookies.delete('admin-session');
      return response;
    }

    // Controle de acesso baseado em role
    const userRole = adminPayload.role;
    console.log(`Middleware Admin: User role: ${userRole}`);

    // Definir regras de acesso por rota
    const masterOnlyRoutes = ['/admin/clientes'];
    const isMasterOnlyRoute = masterOnlyRoutes.some(route => pathname.startsWith(route));

    // Se é rota exclusiva para MASTER e usuário não é MASTER
    if (isMasterOnlyRoute && userRole !== 'MASTER') {
      console.log('Middleware Admin: Acesso negado - rota exclusiva MASTER');
      return NextResponse.redirect(new URL('/admin/busca', request.url));
    }

    // Se está na página raiz /admin e não é MASTER, redirecionar para /admin/busca
    if (pathname === '/admin' && userRole !== 'MASTER') {
      console.log('Middleware Admin: Redirecionando usuário COMUM para /admin/busca');
      return NextResponse.redirect(new URL('/admin/busca', request.url));
    }

    // Usuários MASTER podem acessar /admin (dashboard) sem redirecionamento

    // Redirect da página antiga /admin/comum para /admin/busca (compatibilidade com favoritos)
    if (pathname === '/admin/comum') {
      console.log('Middleware Admin: Redirecionando /admin/comum para /admin/busca (compatibilidade)');
      const redirectUrl = new URL('/admin/busca', request.url);
      // Preservar query parameters se houver
      if (search) {
        redirectUrl.search = search;
      }
      return NextResponse.redirect(redirectUrl);
    }

    console.log('Middleware Admin: Acesso permitido');
    return NextResponse.next();
  }

  // =====================================
  // LÓGICA EXISTENTE PARA OUTRAS ROTAS
  // =====================================
  const token = request.cookies.get('auth_token')?.value;
  console.log(`Middleware: Token recebido: ${token ? 'Presente' : 'Ausente'}`);

  // Se não há token, redireciona para login COM o parâmetro redirect
  if (!token) {
    console.log('Middleware: Sem token, redirecionando para login com redirect.');
    const loginUrl = new URL('/', request.url);
    loginUrl.searchParams.set('redirect', requestedUrl);
    return NextResponse.redirect(loginUrl);
  }

  // Se há token, verifica a validade
  if (!JWT_SECRET) {
    console.error('Middleware: JWT_SECRET não configurada!');
    // Em produção, você pode querer retornar um erro 500 ou redirecionar
    return NextResponse.redirect(new URL('/', request.url)); 
  }

  console.log(`Middleware: Verificando token com secret: ${JWT_SECRET ? '***' : 'SECRET AUSENTE!'}`);
  const payload = await verifyToken(token, JWT_SECRET);
  console.log(`Middleware: Payload da verificação:`, payload);

  // Se o token for inválido, redireciona para login (TAMBÉM com redirect, por segurança)
  if (!payload) {
    console.log('Middleware: Token inválido detectado, redirecionando para login com redirect.');
    const loginUrl = new URL('/', request.url);
    loginUrl.searchParams.set('redirect', requestedUrl);
    // Limpa o cookie inválido antes de redirecionar
    const response = NextResponse.redirect(loginUrl);
    response.cookies.delete('auth_token');
    return response;
  }

  // Se chegou aqui, o token é válido, permite continuar
  console.log('Middleware: Token válido, acesso permitido.');
  return NextResponse.next();
}

// Configuração do Matcher: Especifica quais rotas o middleware deve proteger
export const config = {
  matcher: [
    // Aplica o middleware apenas às rotas /cliente e /processo e suas sub-rotas.
    // Isso evita que o middleware intercepte a página de login ou outros assets.
    '/cliente/:path*',
    '/processo/:path*',
    // Adiciona proteção para rotas admin
    '/admin/:path*',
  ],
}; 