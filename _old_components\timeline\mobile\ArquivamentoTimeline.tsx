import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';

// --- Props do Componente --- 
interface ArquivamentoTimelineProps {
  dataDeposito: Date;
  dataArquivamento: Date;
}

// --- Interfaces e Funções Auxiliares de Progresso --- 
interface ProgressoInfo {
  etapaAtual: number; // 0: Dep->Arq
  progressoNaEtapaAtual: number; // 0 a 1
}

function calcularSeMarcadorCompleto(index: number, progInfo: ProgressoInfo): boolean {
  // Como arquivamento é sempre completo neste componente, ambos marcadores estarão completos
  return true; 
}

function calcularProgressoEntreDatas(hojeMs: number, inicioMs: number | null, fimMs: number | null): number {
  if (inicioMs === null || fimMs === null || fimMs <= inicioMs) {
    return 0; 
  }
  const duracaoEtapa = fimMs - inicioMs;
  const tempoDecorrido = hojeMs - inicioMs;
  return Math.max(0, Math.min(1, tempoDecorrido / duracaoEtapa));
}

// --- Componente Principal --- 
export default function ArquivamentoTimeline({
  dataDeposito,
  dataArquivamento,
}: ArquivamentoTimelineProps) {
  const hoje = new Date();
  const hojeMs = hoje.getTime();

  // --- Cálculo de Datas --- 
  const dataDepositoMs = dataDeposito.getTime();
  const dataArquivamentoMs = dataArquivamento.getTime();

  // --- Cálculo do Progresso --- 
  const calcularProgresso = (): ProgressoInfo => {
    // A lógica de progresso aqui é trivial, pois sempre estará 100% 
    // Mas mantemos a estrutura para consistência
    // 0. Depósito -> Arquivamento
    if (hojeMs < dataArquivamentoMs) { // Tecnicamente não deveria acontecer neste componente
       return { 
        etapaAtual: 0, 
        progressoNaEtapaAtual: calcularProgressoEntreDatas(hojeMs, dataDepositoMs, dataArquivamentoMs)
      };
    }
    // 1. Após Arquivamento
    return { etapaAtual: 1, progressoNaEtapaAtual: 1 };
  };

  const progressoInfo = calcularProgresso();

  // --- Componente Interno TimelineMarker --- 
  const TimelineMarker = ({ 
    data, 
    titulo, 
    descricao, 
    index,
    isUltimoMarcador,
    progressoInfo
  }: { 
    data: Date, 
    titulo: string, 
    descricao: string,
    index: number,
    isUltimoMarcador: boolean,
    progressoInfo: ProgressoInfo
  }) => {
    const isMarkerCompleted = true; // Sempre completo aqui
    // A linha azul sempre terá 100% se não for o último, pois ambas etapas estão completas
    const showBlueLine = !isUltimoMarcador; 
    const blueLineHeightPercent = 100;

    return (
      <div className="flex relative"> {/* Removido mb-6 */} 
        {/* Data (lado esquerdo) */}
        <div className="w-24 flex-shrink-0 mr-4 text-right">
          <span className="text-sm font-medium text-[#A3A3A3]">
            {format(data, "dd/MM/yyyy")}
          </span>
        </div>

        {/* Marcador e Linha de Conexão (Centro) */}
        <div className="flex flex-col items-center w-5"> 
          {/* Marcador */}
          <div className={`w-5 h-5 rounded-full shadow-md flex items-center justify-center flex-shrink-0 
             bg-[#4597B5] border-2 border-[#4597B5] relative z-10`}> {/* Sempre azul */} 
            <svg className="w-3.5 h-3.5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M5 13l4 4L19 7" />
            </svg>
          </div>

          {/* Container da Linha */}
          {!isUltimoMarcador && (
            <div className="w-1 flex-grow bg-[#C3C3C3] relative mt-[-2px]"> 
              {/* Linha Azul - Sempre 100% aqui */}
              <div
                className="absolute top-0 left-0 w-full bg-[#4597B5]"
                style={{ height: `100%` }}
              ></div>
            </div>
          )}
        </div>

        {/* Texto (lado direito) */}
        <div className="ml-4 flex-grow pb-6"> {/* Adicionado pb-6 */} 
          <h3 className="text-base font-semibold text-black leading-tight">{titulo}</h3>
          <p className="text-sm text-gray-500 mt-0.5">{descricao}</p>
        </div>
      </div>
    );
  };

  // --- Renderização Principal --- 
  return (
    <div className="pt-4 pb-2 px-2">
        {/* Marcador 0: Protocolo */}
        <TimelineMarker
          index={0}
          data={dataDeposito}
          titulo="Protocolo"
          descricao="Data em que o pedido foi depositado no INPI."
          isUltimoMarcador={false} 
          progressoInfo={progressoInfo}
        />
        
        {/* Marcador 1: Arquivamento */}
        <TimelineMarker
          index={1}
          data={dataArquivamento}
          titulo="Arquivamento"
          descricao="Data em que o pedido foi arquivado definitivamente."
          isUltimoMarcador={true} 
          progressoInfo={progressoInfo}
        />
    </div>
  );
} 