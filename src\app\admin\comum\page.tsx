'use client';

import { useEffect, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';

function AdminComumRedirectContent() {
  const router = useRouter();
  const searchParams = useSearchParams();

  useEffect(() => {
    // Preservar query parameters no redirect
    const queryString = searchParams.toString();
    const redirectPath = queryString ? `/admin/busca?${queryString}` : '/admin/busca';
    
    console.log('Redirecionando /admin/comum para /admin/busca');
    router.replace(redirectPath);
  }, [router, searchParams]);

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
        <p className="text-gray-600 mb-2">Redirecionando...</p>
        <p className="text-sm text-gray-500">
          Esta página foi movida para <strong>/admin/busca</strong>
        </p>
        <p className="text-xs text-gray-400 mt-2">
          Atualize seus favoritos para a nova URL
        </p>
      </div>
    </div>
  );
}

export default function AdminComumRedirect() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Carregando...</p>
        </div>
      </div>
    }>
      <AdminComumRedirectContent />
    </Suspense>
  );
} 