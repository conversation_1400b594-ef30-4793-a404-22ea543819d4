import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { decryptCredentials } from '@/lib/autoLogin';
import { getAdminFromRequest, requireAdminRole } from '@/lib/adminAuth';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ clienteId: string }> }
) {
  try {
    // Verificar autenticação e role MASTER
    const user = await getAdminFromRequest(request);
    if (!user || !requireAdminRole(['MASTER'])(user)) {
      return NextResponse.json(
        { error: 'Acesso negado. Apenas usuários MASTER podem acessar esta funcionalidade.' },
        { status: 403 }
      );
    }
    const resolvedParams = await params;
    const clienteId = parseInt(resolvedParams.clienteId);

    if (isNaN(clienteId)) {
      return NextResponse.json(
        { error: 'ID do cliente inválido' },
        { status: 400 }
      );
    }

    // Buscar o cliente para obter dados atuais
    const cliente = await prisma.cliente.findUnique({
      where: { id: clienteId },
      select: {
        id: true,
        nome: true,
        identificador: true,
        numeroDocumento: true,
      },
    });

    if (!cliente) {
      return NextResponse.json(
        { error: 'Cliente não encontrado' },
        { status: 404 }
      );
    }

    // Buscar todos os short URLs do cliente
    const shortUrls = await prisma.shortUrl.findMany({
      where: { clienteId },
      orderBy: { createdAt: 'desc' },
    });

    // Calcular senha atual baseada no documento
    let senhaAtual = null;
    if (cliente.numeroDocumento) {
      const numeroLimpo = cliente.numeroDocumento.replace(/[\D]/g, '');
      senhaAtual = numeroLimpo.slice(-3);
    }

    // Analisar cada short URL
    const shortUrlsComAnalise = shortUrls.map((shortUrl) => {
      let tokenInfo = null;
      let precisaCorrecao = false;
      let erroDescriptografia = null;

      try {
        tokenInfo = decryptCredentials(shortUrl.longToken);
        
        // Verificar se a senha do token bate com a senha atual
        if (senhaAtual && tokenInfo?.senha !== senhaAtual) {
          precisaCorrecao = true;
        }
      } catch (error) {
        erroDescriptografia = 'Não foi possível descriptografar o token';
        precisaCorrecao = true;
      }

      return {
        id: shortUrl.id,
        shortCode: shortUrl.shortCode,
        createdAt: shortUrl.createdAt,
        expiresAt: shortUrl.expiresAt,
        usageCount: shortUrl.usageCount,
        isActive: shortUrl.isActive,
        analise: {
          identificadorNoToken: tokenInfo?.identificador || null,
          senhaNoToken: tokenInfo?.senha || null,
          senhaAtualCliente: senhaAtual,
          precisaCorrecao,
          erroDescriptografia,
          podeSerCorrigido: !!(cliente.identificador && cliente.numeroDocumento),
        },
      };
    });

    // Contar quantos precisam de correção
    const totalPrecisandoCorrecao = shortUrlsComAnalise.filter(
      (item) => item.analise.precisaCorrecao
    ).length;

    return NextResponse.json({
      cliente: {
        id: cliente.id,
        nome: cliente.nome,
        identificador: cliente.identificador,
        temDocumento: !!cliente.numeroDocumento,
      },
      shortUrls: shortUrlsComAnalise,
      resumo: {
        total: shortUrls.length,
        ativos: shortUrls.filter((s) => s.isActive).length,
        precisandoCorrecao: totalPrecisandoCorrecao,
        podeCorrigir: !!(cliente.identificador && cliente.numeroDocumento),
      },
    });

  } catch (error) {
    console.error('Erro ao buscar short URLs do cliente:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
} 