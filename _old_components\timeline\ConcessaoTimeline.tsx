import { format, addDays, addYears } from 'date-fns';
import { ptBR } from 'date-fns/locale';

// Interfaces mais precisas baseadas no JSON fornecido
interface RPI {
  dataPublicacao: string;
  numero: number | string; // Pode ser número ou string
}

interface ProtocoloDespacho {
  id: string;
  despachoId: string;
  numero: string;
  data: string;
  codigoServico: string; // Campo chave para renovação
  requerenteNomeRazaoSocial: string;
  requerentePais: string;
  requerenteUf: string;
  procuradorId: string | null;
}

interface DetalhesDespacho {
  nome: string | null;
}

interface Despacho {
  id: string;
  processoId: string;
  rpiId?: string | null;
  codigo: string;
  nome: string | null;
  textoComplementar?: string | null;
  detalhesDespachoId?: string | null;
  DetalhesDespacho: DetalhesDespacho | null;
  // ProtocoloDespacho é um array, pode estar vazio, mas não é opcional no despacho em si
  ProtocoloDespacho: ProtocoloDespacho[]; 
  RPI: RPI | null;
}

interface ConcessaoTimelineProps {
  dataDeposito: Date;
  dataConcessao: Date;
  // Usando any[] para despachos para contornar erro de tipo entre componentes
  despachos: any[]; 
}

export default function ConcessaoTimeline({
  dataDeposito,
  dataConcessao,
  despachos,
}: ConcessaoTimelineProps) {
  const hoje = new Date();
  
  // Cálculo das datas
  const dataFimNulidade = addDays(dataConcessao, 180);

  // Verifica se a PRIMEIRA renovação foi confirmada
  const primeiraRenovacaoConfirmada = despachos.some(despacho => {
    const nomeDespacho = despacho.nome?.toLowerCase() || '';
    const dataPubDespacho = despacho.RPI?.dataPublicacao ? new Date(despacho.RPI.dataPublicacao) : null;
    
    if (nomeDespacho.includes("deferimento da petição") && 
        dataPubDespacho && 
        dataPubDespacho > dataConcessao) { 
      
      const protocolos = despacho.ProtocoloDespacho as any[];
      if (protocolos && protocolos.length > 0) {
        return protocolos.some(protocolo => 
          protocolo?.codigoServico === "3745"
        );
      }
    }
    return false;
  });

  // Determina as datas de renovação e o fim da timeline
  const dataPrimeiraRenovacao = addYears(dataConcessao, 10);
  const dataSegundaRenovacao = addYears(dataConcessao, 20);
  const dataFimTimeline = primeiraRenovacaoConfirmada ? dataSegundaRenovacao : dataPrimeiraRenovacao;

  // Define os pontos de referência visuais na timeline (%)
  const posConcessao = 25;
  const posFimNulidade = 40;
  const posRenovacao = 100; 

  // Calcula o progresso atual baseado nas datas e posições visuais
  const getProgressoAtual = () => {
    const tHoje = hoje.getTime();
    if (tHoje < dataDeposito.getTime()) return 0;

    const tDeposito = dataDeposito.getTime();
    const tConcessao = dataConcessao.getTime();
    const tFimNulidade = dataFimNulidade.getTime();
    const tFimTimeline = dataFimTimeline.getTime(); 
    
    const interpolate = (startT: number, endT: number, startP: number, endP: number) => {
      if (tHoje < startT) return startP;
      if (tHoje >= endT) return endP;
      if (endT <= startT) return startP;
      const progressRatio = (tHoje - startT) / (endT - startT);
      return startP + progressRatio * (endP - startP);
    };

    if (tHoje >= tFimTimeline) return posRenovacao;
    if (tHoje >= tFimNulidade) {
      return interpolate(tFimNulidade, tFimTimeline, posFimNulidade, posRenovacao);
    }
    if (tHoje >= tConcessao) {
      return interpolate(tConcessao, tFimNulidade, posConcessao, posFimNulidade);
    }
    return interpolate(tDeposito, tConcessao, 0, posConcessao);
  };

  return (
    <div className="mt-8 mb-4">
      <div className="relative">
        {/* Container para datas (acima da linha) */}
        <div className="absolute w-full" style={{ bottom: "42px" }}>
          {/* Data Protocolo */}
          <div className="absolute left-0">
            <span className="text-[12px] font-medium text-[#A3A3A3] drop-shadow-sm">
              {format(dataDeposito, "dd/MM/yyyy")}
            </span>
          </div>

          {/* Data Concessão */}
          <div className="absolute" style={{ left: `${posConcessao}%`, transform: 'translateX(-50%)' }}>
            <span className="text-[12px] font-medium text-[#A3A3A3] drop-shadow-sm">
              {format(dataConcessao, "dd/MM/yyyy")}
            </span>
          </div>

          {/* Data Fim Nulidade */}
          <div className="absolute" style={{ left: `${posFimNulidade}%`, transform: 'translateX(-50%)' }}>
            <span className="text-[12px] font-medium text-[#A3A3A3] drop-shadow-sm">
              {format(dataFimNulidade, "dd/MM/yyyy")}
            </span>
          </div>

          {/* Data Renovação - Mostra a data final correta (10 ou 20 anos) */}
          <div className="absolute right-0 text-right">
            <span className="text-[12px] font-medium text-[#A3A3A3] drop-shadow-sm">
              {format(dataFimTimeline, "dd/MM/yyyy")}
            </span>
          </div>
        </div>

        {/* Linha do tempo e marcadores */}
        <div className="relative ml-2">
          {/* Barra de progresso */}
          <div className="relative">
            {/* Barra cinza de fundo */}
            <div className="h-[2px] bg-[#C3C3C3]" />
            
            {/* Barra azul de progresso - Usa getProgressoAtual que considera dataFimTimeline */}
            <div
              className="absolute top-[-2.5px] left-0 h-[7px] bg-[#4597B5] transition-all duration-300"
              style={{ width: `${getProgressoAtual()}%` }}
            />
          </div>

          {/* Marcadores */}
          <div className="absolute w-full" style={{ top: "-10px" }}>
            {/* Protocolo */}
            <div className="absolute -left-2.5">
              <div className="w-5 h-5 rounded-full bg-[#4597B5] shadow-md flex items-center justify-center">
                <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2.5} d="M5 13l4 4L19 7" />
                </svg>
              </div>
            </div>

            {/* Concessão */}
            <div className="absolute" style={{ left: `${posConcessao}%`, transform: 'translateX(-50%)' }}>
              <div className="w-5 h-5 rounded-full bg-[#4597B5] shadow-md flex items-center justify-center">
                <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2.5} d="M5 13l4 4L19 7" />
                </svg>
              </div>
            </div>

            {/* Fim Nulidade */}
            <div className="absolute" style={{ left: `${posFimNulidade}%`, transform: 'translateX(-50%)' }}>
              <div className={`w-5 h-5 rounded-full shadow-md flex items-center justify-center ${
                hoje >= dataFimNulidade ? 'bg-[#4597B5]' : 'border-2 border-[#C3C3C3] bg-white'
              }`}>
                {hoje >= dataFimNulidade && (
                  <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2.5} d="M5 13l4 4L19 7" />
                  </svg>
                )}
              </div>
            </div>

            {/* Renovação - Sempre cinza/branco, representa o prazo final */}
            <div className="absolute right-0">
              <div className={`w-5 h-5 rounded-full shadow-md flex items-center justify-center border-2 border-[#C3C3C3] bg-white`}>
                {/* Nenhum check aqui, pois representa o prazo futuro */}
              </div>
            </div>
          </div>
        </div>

        {/* Container para nomes das etapas (abaixo da linha) */}
        <div className="absolute w-full" style={{ top: "20px" }}>
          {/* Nome Protocolo */}
          <div className="absolute left-0">
            <span className="text-[14px] font-semibold text-black drop-shadow-sm block">
              Protocolo
            </span>
          </div>

          {/* Nome Concessão */}
          <div className="absolute" style={{ left: `${posConcessao}%`, transform: 'translateX(-50%)' }}>
            <span className="text-[14px] font-semibold text-black drop-shadow-sm block text-center">
              Concessão
            </span>
          </div>

          {/* Nome Fim Nulidade */}
          <div className="absolute" style={{ left: `${posFimNulidade}%`, transform: 'translateX(-50%)' }}>
            <span className="text-[14px] font-semibold text-black drop-shadow-sm block text-left">
              Prazo de nulidade
            </span>
          </div>

          {/* Nome Renovação */}
          <div className="absolute right-0">
            <span className="text-[14px] font-semibold text-black drop-shadow-sm text-right block">
              Renovação
            </span>
          </div>
        </div>
      </div>
    </div>
  );
} 