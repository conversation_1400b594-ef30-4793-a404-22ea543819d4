import React, { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import Image from 'next/image';
import { usePathname } from 'next/navigation';
import { useProcessoStatus } from '@/hooks/useProcessoStatus';
import styles from './ProcessoCard.module.css';
import { IoIosArrowDown } from "react-icons/io";
import { DemoLogos } from './DemoLogos';

// Importa os subcomponentes
import ProcessoCardHeader from './ProcessoCard/ProcessoCardHeader';
import ProcessoCardTimelineContainer from './ProcessoCard/ProcessoCardTimelineContainer';
import ProcessoCardSidebar from './ProcessoCard/ProcessoCardSidebar';
// Importa o componente de detalhes
import ProcessoDetalhes from './ProcessoCard/ProcessoDetalhes';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
// Importa a timeline mobile
import TimelineUnificadaMobile from '@/components/timeline/mobile/TimelineUnificadaMobile';

interface NCL {
  codigo: string | null;
  especificacao: string | null;
}

interface Marca {
  nome: string | null;
  NCL: NCL[];
  apresentacao: string | null;
  natureza: string | null;
}

interface DetalhesDespacho {
  nome: string | null;
}

interface RPI {
  dataPublicacao: string;
  numero: string;
}

interface Despacho {
  codigo: string;
  nome: string | null;
  DetalhesDespacho: DetalhesDespacho | null;
  RPI: RPI | null;
  ProtocoloDespacho?: {
    codigoServico?: string;
  }[];
}

interface Processo {
  id: string;
  numero: string;
  dataDeposito: string | null;
  dataPublicacaoRPI: string | null;
  dataConcessao?: string | null;
  dataVigencia?: string | null;
  dataMeritoEstimada: string | null;
  dataOposicao: string | null;
  Marca: Marca | null;
  Despacho: Despacho[];
  oposicao: boolean;
  Cliente?: { crmStageId?: number };
  taxaConcessaoPaga?: boolean;
  logoUrl: string | null;
}

interface ProcessoCardProps {
  processo: Processo | null;
  estimativasMerito?: {
    mediaSemIntervencoes?: number | null;
    mediaComOposicao?: number | null;
    mediaComSobrestamento?: number | null;
    mediaComExigencia?: number | null;
  } | null;
}



export default function ProcessoCard({ processo, estimativasMerito }: ProcessoCardProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const [isMobileView, setIsMobileView] = useState(false);
  const [imageError, setImageError] = useState(false);
  const [showDemoModal, setShowDemoModal] = useState(false);
  const pathname = usePathname();
  

  
  // Verificar se estamos na página de demonstração
  const isViewPage = pathname === '/view';
  
  console.log(JSON.stringify(processo, null, 2));
  // Se processo for null, renderizar um card de erro
  if (!processo) {
    return (
      <div className="relative z-2">
        <div className="shadow-md overflow-hidden bg-gray-100 rounded-[17px] p-6">
          <div className="text-center">
            <h3 className="text-lg font-bold text-gray-700 mb-2">Processo não encontrado</h3>
            <p className="text-gray-600">Não foi possível carregar os dados do processo.</p>
          </div>
        </div>
      </div>
    );
  }
  
  // Efeito para verificar o tamanho da tela
  useEffect(() => {
    const checkScreenSize = () => {
      setIsMobileView(window.innerWidth < 1024);
    };
    
    // Verificar inicialmente
    checkScreenSize();
    
    // Adicionar listener para redimensionamento
    window.addEventListener('resize', checkScreenSize);
    
    // Limpar listener ao desmontar
    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);
  
  const {
    statusText,
    cardBgColorClass,
    sidebarBgColorClass,
    sidebarIconComponent: SidebarIcon,
    showSidebar,
    logoImage,
    nomeMarca,
    linkInpi,
    codigoNCL,
    showOposicaoBadge,
    etapasTimeline,
    dataMeritoEstimadaFormatada,
    useSpacingForTimeline,
    rawDespachos,
  } = useProcessoStatus(processo, estimativasMerito);

  // Verificar se deve mostrar MarcaInfoDisplay em vez da imagem
  const shouldShowMarcaInfo = !isViewPage && (logoImage === "IMAGEM_NAO_IDENTIFICADA" || imageError);

  // Componente de Logo Fictícia
  const DemoLogo = isViewPage && processo?.numero && processo.numero in DemoLogos 
    ? DemoLogos[processo.numero as keyof typeof DemoLogos] 
    : null;

  // Resetar erro de imagem quando logoImage muda
  useEffect(() => {
    setImageError(false);
  }, [logoImage]);

  const handleDownloadProtocolo = async (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    
    // Se estamos na página de demonstração, mostrar modal informativo
    if (isViewPage) {
      setShowDemoModal(true);
      return;
    }
    
    try {
      console.log('Iniciando download do protocolo para processo:', processo.numero);
      
      const downloadUrl = `https://api-v3-rgsys.registrese.app.br/api/protocolo/download/${processo.numero}`;
      const response = await fetch(downloadUrl);
      
      if (!response.ok) {
        throw new Error(`Erro ao baixar protocolo: ${response.status} ${response.statusText}`);
      }
      
      const blob = await response.blob();
      const blobUrl = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = blobUrl;
      link.download = `protocolo_${processo.numero}.pdf`;
      
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(blobUrl);
      
      console.log('Download do protocolo concluído com sucesso');
      
      try {
        await fetch('/api/tracking/download', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            processoId: processo.id,
            numeroProcesso: processo.numero,
            fileSize: blob.size,
            success: true
          })
        });
      } catch (trackingError) {
        console.warn('Erro ao registrar log de download:', trackingError);
      }
      
    } catch (error) {
      console.error('Erro ao fazer download do protocolo:', error);
      
      try {
        await fetch('/api/tracking/download', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            processoId: processo.id,
            numeroProcesso: processo.numero,
            success: false,
            errorMessage: error instanceof Error ? error.message : 'Erro desconhecido'
          })
        });
      } catch (trackingError) {
        console.warn('Erro ao registrar log de erro de download:', trackingError);
      }
      
      alert('Erro ao baixar o protocolo. Tente novamente mais tarde.');
    }
  };

  const handleToggleExpand = (e?: React.MouseEvent) => {
    // Debug temporário para mobile
    console.log('🔍 handleToggleExpand chamado:', {
      hasEvent: !!e,
      target: e?.target,
      ctrlKey: e?.ctrlKey,
      metaKey: e?.metaKey,
      currentExpanded: isExpanded
    });

    // Se não há evento (chamada programática), apenas toggle
    if (!e) {
      setIsExpanded(!isExpanded);
      return;
    }

    // Verificar se o clique foi em um elemento que deve ter comportamento especial
    const target = e.target as HTMLElement;
    
    // Se o clique foi em um elemento com data-no-expand, não expandir
    if (target.closest('[data-no-expand]')) {
      console.log('🚫 Clique em elemento data-no-expand, não expandindo');
      return;
    }
    
    // Se foi Ctrl+Click, abrir página do processo em nova aba
    if (e.ctrlKey || e.metaKey) {
      console.log('🔗 Ctrl+Click detectado, abrindo nova aba');
      window.open(`/processo/${processo.numero}`, '_blank', 'noopener,noreferrer');
      return;
    }
    
    console.log('✅ Expandindo/fechando card');
    e.preventDefault();
    setIsExpanded(!isExpanded);
  };

  const handleImageError = () => {
    console.log('Erro ao carregar imagem:', logoImage);
    setImageError(true);
  };

  // Componente para exibir informações da marca quando não há imagem
  const MarcaInfoDisplay = ({ className }: { className?: string }) => (
    <div className={`${className} bg-gray-50 border-2 border-dashed border-gray-300 rounded-lg flex flex-col items-center justify-center p-4 text-center`}>
      {processo.Marca?.apresentacao && (
        <div className="text-sm text-gray-600 mb-1">
          <span className="font-medium">Apresentação:</span> {processo.Marca.apresentacao}
        </div>
      )}
    </div>
  );
  
  // Datas formatadas para versão mobile
  const dataDepositoFormatada = processo.dataDeposito 
    ? format(new Date(processo.dataDeposito), "dd/MM/yyyy")
    : null;

  return (
    <div className="relative z-2">
      <div 
        className={`${
          isHovered ? 'shadow-lg' : 'shadow-md'
        } ${!isExpanded ? '' : ''} overflow-hidden bg-white rounded-[17px] transition-all duration-300 hover:shadow-lg`}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        {/* VERSÃO DESKTOP - Alterado para lg:block */}
        <div className="hidden lg:block relative">
          <div className={`flex rounded-tl-[17px] rounded-tr-[17px] transition-all duration-300 overflow-hidden ${
            isHovered && !isExpanded ? 'h-[280px]' : 'h-[249px]'
          }`}>
            <div
              className={`rounded-tl-[17px] p-6 flex-grow cursor-pointer transition-colors duration-200 h-full ${isExpanded ? 'bg-white' : cardBgColorClass}`}
              onClick={handleToggleExpand}
            >
              <div className="flex h-full">
                <div className="w-[200px] h-[200px] flex-shrink-0 mr-8">
                  {DemoLogo ? (
                    <DemoLogo className="w-full h-full" />
                  ) : shouldShowMarcaInfo ? (
                    <MarcaInfoDisplay className="w-full h-full" />
                  ) : (
                    <Image
                      src={logoImage}
                      alt={nomeMarca}
                      width={200}
                      height={200}
                      className="w-full h-full select-none"
                      objectFit='contain'
                      unoptimized={logoImage.includes('/nominativa')}
                      onError={handleImageError}
                    />
                  )}
                </div>

                <div className="flex-1 flex flex-col">
                  <ProcessoCardHeader 
                    nomeMarca={nomeMarca}
                    linkInpi={linkInpi}
                    numeroProcesso={processo.numero}
                    codigoNCL={codigoNCL}
                    showOposicaoBadge={showOposicaoBadge}
                    processo={processo}
                    onToggleExpand={handleToggleExpand}
                  />
                  
                  <ProcessoCardTimelineContainer 
                    etapasTimeline={etapasTimeline}
                    dataMeritoEstimadaFormatada={dataMeritoEstimadaFormatada}
                    useSpacingForTimeline={useSpacingForTimeline}
                    showOposicaoBadge={showOposicaoBadge}
                    estimativasDb={estimativasMerito}
                    processo={processo}
                  />
                </div>
              </div>
            </div>

            <div className="flex-shrink-0">
              {showSidebar && (
                <ProcessoCardSidebar 
                  className="rounded-tr-[17px]"
                  sidebarBgColorClass={sidebarBgColorClass}
                  SidebarIcon={SidebarIcon}
                  onToggleExpand={handleToggleExpand}
                />
              )}
            </div>
          </div>

          {/* Seta indicativa de expansão - DESKTOP */}
          <div className="hidden lg:block">
            <div className={`absolute bottom-2 left-1/2 transform -translate-x-1/2 transition-all duration-300 ${
              isHovered && !isExpanded ? 'opacity-100' : 'opacity-0'
            }`}>
              <div>
                <IoIosArrowDown className="text-gray-600 text-2xl" />
              </div>
            </div>
          </div>

          {/* Container de Detalhes com Animação - Aumenta max-h (DESKTOP) */}
          <div className={`transition-all duration-300 ease-in-out ${isExpanded ? 'max-h-[5000px] opacity-100' : 'max-h-0 opacity-0'} overflow-hidden rounded-b-[17px]`}>
            <ProcessoDetalhes 
              processo={processo} 
              sidebarBgColorClass={sidebarBgColorClass}
              showSidebar={showSidebar}
            />
          </div>
        </div>

        {/* VERSÃO MOBILE - Alterado para lg:hidden */}
        <div className={`lg:hidden flex ${isMobileView ? 'flex-col' : 'flex-row'} relative ${isExpanded ? 'bg-white' : cardBgColorClass} rounded-[17px] overflow-hidden transition-all duration-300 ${
          isHovered ? 'min-h-[200px]' : 'h-auto'
        }`}>
          {/* Faixa de Oposição no estilo requerido - movida para o container pai */}
          {showOposicaoBadge && (
            <div className={styles.ribbonOposicao} aria-hidden="true">
              <span>Oposição</span>
            </div>
          )}
          
          {/* Conteúdo principal do card */}
          <div className="flex-1"> 
            <div 
              className="p-3 sm:p-4 md:p-5 cursor-pointer transition-all duration-300 flex flex-col min-h-[160px] sm:min-h-[180px]"
              onClick={handleToggleExpand}
            >
              {/* Layout em coluna com nome no topo e conteúdo abaixo */}
              <div className="flex flex-col h-full justify-center flex-grow">
                {/* Nome da marca e status no topo - Adicionar margem md:mb-4 e fontes md */}
                <div className="mb-2 sm:mb-3 md:mb-4">
                  <h2 
                    // Adicionar fonte md:text-2xl e classes de seleção
                    className="text-lg sm:text-xl md:text-2xl font-bold line-clamp-1 max-w-full overflow-hidden text-ellipsis select-text cursor-text" 
                    title={nomeMarca}
                    onClick={(e) => e.stopPropagation()} // Permite seleção de texto
                  >
                    {nomeMarca}
                  </h2>
                  {/* Adicionar fonte md:text-base e classes de seleção */}
                  <p className="text-[14px] sm:text-sm md:text-base text-gray-700 line-clamp-1">{statusText}</p>
                </div>
                
                {/* Container para imagem e informações */}
                <div className="flex flex-row flex-grow">
                  {/* Logo à esquerda - Adicionar margem md:mr-6 e tamanho md */}
                  <div className="flex items-center mr-2 sm:mr-4 md:mr-6">
                    {/* Adicionar tamanho md:w-[150px] md:h-[150px] */}
                    <div className="w-[124px] h-[124px] md:w-[140px] md:h-[140px] flex-shrink-0">
                      {DemoLogo ? (
                        <DemoLogo className="w-full h-full" />
                      ) : shouldShowMarcaInfo ? (
                        <MarcaInfoDisplay className="w-full h-full" />
                      ) : (
                        <Image
                          src={logoImage}
                          alt={nomeMarca}
                          width={124}
                          height={124}
                          className="h-full rounded-sm object-contain select-none"
                          unoptimized={logoImage.includes('/nominativa')}
                          onError={handleImageError}
                        />
                      )}
                    </div>
                  </div>
                  
                  {/* Informações à direita da imagem */}
                  <div className="flex-1 flex flex-col justify-center pr-1">
                    {/* Processo e classe - Adicionar fontes md e largura md:w-[130px] e classes de seleção */}
                    <div className="mb-1">
                      <div className="flex items-center">
                         {/* Adicionar fonte md:text-base e largura md:w-[130px] e classes de seleção */}
                        <span className="font-medium text-[14px] sm:text-sm md:text-base flex-shrink-0 w-[70px] sm:w-[115px] md:w-[130px]">Processo:</span>
                        <div className="flex items-center gap-2 flex-1">
                          <span 
                            // Adicionar fonte md:text-base e classes de seleção
                            className="font-bold text-[14px] sm:text-sm md:text-base hover:underline overflow-hidden text-ellipsis select-text cursor-pointer"
                            onClick={(e) => {
                              e.stopPropagation(); 
                              window.open(linkInpi, '_blank', 'noopener,noreferrer');
                            }}
                            title="Abrir processo no INPI"
                            data-no-expand="true"
                          >
                            {processo.numero?.slice(0, 10)} 
                          </span>
                          {/* Botão de download mobile */}
                          <div
                            onClick={handleDownloadProtocolo}
                            className="cursor-pointer hover:opacity-80 transition-opacity flex-shrink-0"
                            title="Baixar protocolo do processo"
                            data-no-expand="true"
                          >
                            <svg
                              width="24"
                              height="22"
                              viewBox="0 0 45 42"
                              fill="none"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <rect width="45" height="42" rx="7" fill="#45B063" />
                              <path
                                d="M9.87097 22.5548C9.37476 22.5548 8.89887 22.752 8.54799 23.1028C8.19712 23.4537 8 23.9296 8 24.4258V26.2968C8 27.7854 8.59136 29.2131 9.64398 30.2657C10.6966 31.3183 12.1243 31.9097 13.6129 31.9097H31.3871C32.8757 31.9097 34.3034 31.3183 35.356 30.2657C36.4086 29.2131 37 27.7854 37 26.2968V24.4258C37 23.9296 36.8029 23.4537 36.452 23.1028C36.1011 22.752 35.6252 22.5548 35.129 22.5548C34.6328 22.5548 34.1569 22.752 33.8061 23.1028C33.4552 23.4537 33.2581 23.9296 33.2581 24.4258V26.2968C33.2581 26.793 33.0609 27.2689 32.7101 27.6198C32.3592 27.9706 31.8833 28.1677 31.3871 28.1677H13.6129C13.1167 28.1677 12.6408 27.9706 12.2899 27.6198C11.9391 27.2689 11.7419 26.793 11.7419 26.2968V24.4258C11.7419 23.9296 11.5448 23.4537 11.1939 23.1028C10.8431 22.752 10.3672 22.5548 9.87097 22.5548Z"
                                fill="white"
                              />
                              <path
                                d="M22.5 10C22.0037 10 21.5279 10.1972 21.177 10.548C20.8261 10.8989 20.629 11.3748 20.629 11.871V19.1798L17.8225 16.3734C17.5097 16.0456 17.0923 15.8373 16.6423 15.7843C16.1923 15.7314 15.7379 15.8372 15.3575 16.0834C15.1321 16.2394 14.9436 16.4431 14.8056 16.68C14.6675 16.917 14.5833 17.1813 14.5587 17.4544C14.5342 17.7276 14.57 18.0027 14.6635 18.2605C14.7571 18.5182 14.9062 18.7522 15.1003 18.946L19.6093 23.4456C20.3774 24.2114 21.4177 24.6414 22.5023 24.6414C23.5869 24.6414 24.6272 24.2114 25.3953 23.4456L29.909 18.946C30.103 18.7522 30.2521 18.5182 30.3457 18.2605C30.4393 18.0027 30.4751 17.7276 30.4506 17.4544C30.426 17.1813 30.3417 16.917 30.2037 16.68C30.0657 16.4431 29.8772 16.2394 29.6517 16.0834C29.2714 15.8372 28.817 15.7314 28.367 15.7843C27.917 15.8373 27.4996 16.0456 27.1867 16.3734L24.3803 19.1798V11.871C24.3803 11.6245 24.3316 11.3805 24.237 11.1529C24.1424 10.9252 24.0037 10.7186 23.829 10.5447C23.6543 10.3709 23.4469 10.2332 23.2188 10.1398C22.9907 10.0463 22.7464 9.99881 22.5 10Z"
                                fill="white"
                              />
                            </svg>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center mt-1">
                        {/* Adicionar fonte md:text-base e largura md:w-[130px] e classes de seleção */}
                        <span className="font-medium text-[14px] sm:text-sm md:text-base flex-shrink-0 w-[70px] sm:w-[115px] md:w-[130px]">Classe:</span>
                         {/* Adicionar fonte md:text-base e classes de seleção */}
                        <span className="font-bold text-[14px] sm:text-sm md:text-base ml-1">{codigoNCL || "01"}</span>
                      </div>
                    </div>
                    
                    {/* Linha divisória - Adicionar margem md:my-3 */}
                    <div className="border-t border-gray-300 my-1 sm:my-2 md:my-3"></div>
                    
                    {/* Depósito - Adicionar fontes md e largura md:w-[130px] e classes de seleção */}
                    <div className="mb-1">
                      <div className="flex items-center">
                         {/* Adicionar fonte md:text-base e largura md:w-[130px] e classes de seleção */}
                        <span className="font-medium text-[14px] sm:text-sm md:text-base flex-shrink-0 w-[70px] sm:w-[115px] md:w-[130px]">Depósito:</span>
                         {/* Adicionar fonte md:text-base e classes de seleção */}
                        <span className="font-bold text-[14px] sm:text-sm md:text-base ml-1">{dataDepositoFormatada}</span>
                      </div>
                    </div>
                  </div>
                  </div>
                </div>
              </div>
            </div>
            
            {/* Timeline Container Mobile (expandido quando clicado) */}
            <div className={`transition-all duration-300 ease-in-out bg-white ${isExpanded ? 'max-h-[5000px] opacity-100' : 'max-h-0 opacity-0'} overflow-hidden`}>
            {etapasTimeline && etapasTimeline.length > 0 && (
                <div className="p-3 sm:p-4">
                <TimelineUnificadaMobile etapas={etapasTimeline} estimativasDb={estimativasMerito} processo={processo} />
                </div>
              )}
          </div>
          
          {/* Sidebar Mobile */}
          {showSidebar && (
            <div className={`${sidebarBgColorClass} ${isMobileView ? 'h-10 sm:h-12 w-full' : 'w-10 sm:w-12 h-auto'} flex-shrink-0 flex items-center justify-center cursor-pointer`}
                 onClick={handleToggleExpand}>
              {SidebarIcon && <SidebarIcon />}
            </div>
          )}
        </div>
      </div>

      {/* Modal Informativo para Página de Demonstração usando Portal */}
      {showDemoModal && typeof window !== 'undefined' && createPortal(
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4" 
          style={{ zIndex: 10000 }}
          onClick={() => setShowDemoModal(false)}
        >
          <div 
            className="bg-white rounded-lg max-w-md w-full p-6 shadow-xl relative"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Botão X no canto superior direito */}
            <button
              onClick={() => setShowDemoModal(false)}
              className="absolute -top-2 -right-2 w-8 h-8 bg-gray-600 hover:bg-gray-700 text-white rounded-full flex items-center justify-center transition-colors shadow-lg"
              aria-label="Fechar modal"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>

            <div className="text-center">
              {/* Ícone de registro */}
              <div className="mx-auto mb-4 w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
                <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              
              <h3 className="text-xl font-bold text-gray-900 mb-3">
                Proteja sua marca agora!
              </h3>
              
              <p className="text-gray-600 mb-6 leading-relaxed">
                Vamos protocolar seu pedido de registro de marca de forma ágil e descomplicada. 
                Nossa equipe especializada cuidará de todo o processo junto ao INPI.
              </p>
              
              <div className="flex justify-center">
                <button
                  onClick={() => {
                    window.open('https://registre.se/', '_blank', 'noopener,noreferrer');
                    setShowDemoModal(false);
                  }}
                  className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium"
                >
                  Registrar Minha Marca
                </button>
              </div>
            </div>
          </div>
        </div>,
        document.body
      )}
    </div>
  );
} 