'use client';

import React from 'react';
import Image from 'next/image';
import { useProcessoStatus } from '@/hooks/useProcessoStatus';
import { useEstimativasMerito } from '@/hooks/useEstimativasMerito';
import ProcessoCardHeader from '@/components/ProcessoCard/ProcessoCardHeader';
import ProcessoCardTimelineContainer from '@/components/ProcessoCard/ProcessoCardTimelineContainer';
import ProcessoCardSidebar from '@/components/ProcessoCard/ProcessoCardSidebar';
import ProcessoDetalhes from '@/components/ProcessoCard/ProcessoDetalhes';

interface ProcessoClientWrapperProps {
  processo: any | null; // Processo pode ser null
}

export default function ProcessoClientWrapper({ processo }: ProcessoClientWrapperProps) {
  // Hook para buscar estimativas de mérito
  const { estimativas: estimativasMerito } = useEstimativasMerito();

  // Utiliza o mesmo hook que é usado no ProcessoCard original
  const {
    statusText,
    sidebarBgColorClass,
    sidebarIconComponent: SidebarIcon,
    showSidebar,
    logoImage,
    nomeMarca,
    linkInpi,
    codigoNCL,
    showOposicaoBadge,
    etapasTimeline,
    dataMeritoEstimadaFormatada,
    useSpacingForTimeline,
  } = useProcessoStatus(processo, estimativasMerito);

  // Verificar se deve mostrar MarcaInfoDisplay em vez da imagem
  const shouldShowMarcaInfo = logoImage === "IMAGEM_NAO_IDENTIFICADA";

  // Componente para exibir informações da marca quando não há imagem
  const MarcaInfoDisplay = ({ className }: { className?: string }) => (
    <div className={`${className} bg-gray-50 border-2 border-dashed border-gray-300 rounded-lg flex flex-col items-center justify-center p-4 text-center`}>
      <div className="text-lg font-bold text-gray-700 mb-2">Aguardando Publicação</div>
      {processo?.Marca?.apresentacao && (
        <div className="text-sm text-gray-600 mb-1">
          <span className="font-medium">Apresentação:</span> {processo.Marca.apresentacao}
        </div>
      )}
    </div>
  );

  return (
    <div className="shadow-md rounded-[17px] overflow-hidden bg-white">
      <div className="flex">
        {/* Área principal do card */}
        <div className={`rounded-tl-[17px] p-6 flex-grow bg-white`}>
          <div className="flex h-full">
            {/* Logo */}
            <div className="w-[200px] h-[200px] flex-shrink-0 mr-8">
              {shouldShowMarcaInfo ? (
                <MarcaInfoDisplay className="w-full h-full" />
              ) : (
              <Image
                src={logoImage}
                alt={nomeMarca}
                width={200}
                height={200}
                className="w-full h-full object-contain"
                  unoptimized={logoImage.includes('/nominativa')}
                  onError={() => console.log('Erro ao carregar imagem:', logoImage)}
              />
              )}
            </div>

            {/* Conteúdo */}
            <div className="flex-1 flex flex-col">
              <ProcessoCardHeader 
                nomeMarca={nomeMarca}
                linkInpi={linkInpi}
                numeroProcesso={processo.numero}
                codigoNCL={codigoNCL}
                showOposicaoBadge={showOposicaoBadge}
                processo={processo}
              />
              
              <ProcessoCardTimelineContainer 
                etapasTimeline={etapasTimeline}
                dataMeritoEstimadaFormatada={dataMeritoEstimadaFormatada}
                useSpacingForTimeline={useSpacingForTimeline}
                showOposicaoBadge={showOposicaoBadge}
                estimativasDb={estimativasMerito}
                processo={processo}
              />
            </div>
          </div>
        </div>

        {/* Sidebar (se aplicável) */}
        <div className="flex-shrink-0">
          {showSidebar && (
            <ProcessoCardSidebar 
              className="rounded-tr-[17px]"
              sidebarBgColorClass={sidebarBgColorClass}
              SidebarIcon={SidebarIcon}
            />
          )}
        </div>
      </div>

      {/* Detalhes do processo (sempre visíveis nesta página) */}
      <ProcessoDetalhes 
        processo={processo} 
        sidebarBgColorClass={sidebarBgColorClass}
        showSidebar={showSidebar}
      />
    </div>
  );
} 