import React from 'react';
import { EtapaTimelineGerada } from '@/lib/timelineNovasTypes';
import MarcadorTimeline from './MarcadorTimeline';
import { formatDate } from './timelineDinamica.utils'; // Importar para formatar a data
// import LinhaConexao from './LinhaConexao'; // Removido
// import ConteudoEtapa from './ConteudoEtapa';

interface EtapaItemProps {
  etapa: EtapaTimelineGerada;
  // isUltimaEtapa não é mais necessário aqui, a linha horizontal cuidará disso
  isMobile?: boolean;
  // Novas props para posicionamento horizontal podem ser adicionadas depois
  // exemplo: style?: React.CSSProperties;
}

const EtapaItem: React.FC<EtapaItemProps> = ({ etapa, isMobile /*, style */ }) => {
  const { etapaDefinicao, status, dataInicio, dataFim, observacaoGerada } = etapa;

  const tituloEtapa = etapaDefinicao.nomeOriginal;
  
  // Determina qual data exibir com base no tipo da etapa
  const dataExibicao = etapaDefinicao.tipo === 'PRAZO' ? dataFim : dataInicio;

  return (
    <div 
      className={`flex flex-col items-center text-center px-2 ${isMobile ? 'min-w-[5rem]' : 'min-w-[7rem]'}`}
      // style={style} // Se o posicionamento absoluto for controlado aqui
    >
      {/* Data da Etapa (Acima do Marcador) */}
      <div className={`text-xs mb-1 whitespace-nowrap ${isMobile ? 'text-gray-500' : 'text-[#A3A3A3]'}`}>
        {dataExibicao ? formatDate(dataExibicao, "dd/MM/yyyy") : ' '} 
        {/* Adicionar (est.) se necessário */}
        {/* {status === 'PROXIMA_ESTIMADA' && ' (est.)'} */}
      </div>

      {/* Marcador da Timeline */}
      <MarcadorTimeline 
        status={status} 
        tipoEtapa={etapaDefinicao.tipo} 
        isMobile={isMobile} 
      />

      {/* Nome da Etapa (Abaixo do Marcador) */}
      <div className={`text-xs font-semibold mt-1 ${isMobile ? 'text-gray-700' : 'text-black'} leading-tight`}>
        {tituloEtapa}
      </div>

      {/* Observação/Subtítulo (Opcional, como "Prazo ordinário") */}
      {observacaoGerada && (
        <div className={`text-[10px] ${isMobile ? 'text-gray-500' : 'text-[#A3A3A3]'}`}>
          {observacaoGerada}
        </div>
      )}
       {/* Se precisarmos de outros detalhes de ConteudoEtapa, eles podem ser adicionados aqui
           ou ConteudoEtapa pode ser chamado para renderizar apenas partes específicas. */}
    </div>
  );
};

export default EtapaItem; 