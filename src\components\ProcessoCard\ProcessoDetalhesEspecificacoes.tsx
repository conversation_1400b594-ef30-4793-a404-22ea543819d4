import React, { useState } from 'react';

interface NCLData {
  codigo: string | null;
  especificacao: string | null;
}

interface ProcessoDetalhesEspecificacoesProps {
  ncl: NCLData | null | undefined; // Recebe o objeto NCL
}

const CHAR_LIMIT = 300;

const ProcessoDetalhesEspecificacoes: React.FC<ProcessoDetalhesEspecificacoesProps> = ({ ncl }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  // Não renderiza nada se não houver NCL ou especificação
  if (!ncl?.especificacao) {
    return null;
  }

  const toggleExpansion = () => {
    setIsExpanded(!isExpanded);
  };

  const codigoNCL = ncl?.codigo ? ` (NCL ${ncl.codigo})` : ' (Pré-definidas)';
  const especificacaoTexto = ncl.especificacao;

  const renderEspecificacao = () => {
    if (isExpanded || especificacaoTexto.length <= CHAR_LIMIT) {
      return (
        <span onClick={toggleExpansion} style={{ cursor: 'pointer' }}>
          {especificacaoTexto}
          {especificacaoTexto.length > CHAR_LIMIT && (
            <span style={{ textDecoration: 'underline', marginLeft: '4px' }}>(Recolher)</span>
          )}
        </span>
      );
    }
    return (
      <span onClick={toggleExpansion} style={{ cursor: 'pointer' }}>
        {`${especificacaoTexto.substring(0, CHAR_LIMIT)}`}
        <span style={{ textDecoration: 'underline' }}>... (Ver mais)</span>
      </span>
    );
  };

  return (
    <div className="mb-6 pl-[45px]"> {/* Adiciona margem inferior para separar da próxima seção */}
      <h4 className="text-[12px] font-bold mb-1 text-black uppercase">
        Especificações{codigoNCL}
      </h4>
      <p className="text-[14px] text-black leading-[170%]">
        {renderEspecificacao()}
      </p>
    </div>
  );
};

export default ProcessoDetalhesEspecificacoes; 