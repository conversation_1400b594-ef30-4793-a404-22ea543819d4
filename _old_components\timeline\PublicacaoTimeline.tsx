import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';

interface PublicacaoTimelineProps {
  dataDeposito: Date | null;
  dataPublicacao: Date | null;
  dataFimOposicao: Date | null;
  dataMeritoEstimada: Date | null;
  temDespachoMerito?: boolean;
  temNotificacaoOposicao?: boolean;
}

export default function PublicacaoTimeline({
  dataDeposito,
  dataPublicacao,
  dataFimOposicao,
  dataMeritoEstimada,
  temDespachoMerito = false,
  temNotificacaoOposicao = false
}: PublicacaoTimelineProps) {
  const getProgressoAtual = () => {
    const hoje = new Date();
    
    if (!dataDeposito) return 0;
    if (hoje < dataDeposito) return 0;
    
    const posicaoPublicacao = 15;
    const posicaoOposicao = 40;
    const posicaoMerito = 84;
    
    if (!dataPublicacao) return posicaoPublicacao / 2;
    if (hoje < dataPublicacao) {
      const progresso = (hoje.getTime() - dataDeposito.getTime()) / (dataPublicacao.getTime() - dataDeposito.getTime());
      return progresso * posicaoPublicacao;
    }
    
    if (!dataFimOposicao) return (posicaoPublicacao + posicaoOposicao) / 2;
    if (hoje < dataFimOposicao) {
      const progresso = (hoje.getTime() - dataPublicacao.getTime()) / (dataFimOposicao.getTime() - dataPublicacao.getTime());
      return posicaoPublicacao + (progresso * (posicaoOposicao - posicaoPublicacao));
    }
    
    if (!dataMeritoEstimada) return posicaoOposicao;
    if (temDespachoMerito) return posicaoMerito;
    if (hoje >= dataMeritoEstimada) return posicaoMerito - 1;
    
    const progresso = (hoje.getTime() - dataFimOposicao.getTime()) / (dataMeritoEstimada.getTime() - dataFimOposicao.getTime());
    return posicaoOposicao + (progresso * (posicaoMerito - posicaoOposicao));
  };

  return (
    <div className={`mt-8 mb-4 ${temNotificacaoOposicao ? "mt-2" : ""}`}>
      <div className="relative">
        {/* Container para datas (acima da linha) */}
        <div className="absolute w-full" style={{ bottom: "42px" }}>
          {/* Data Protocolo */}
          <div className="absolute left-0">
            {dataDeposito && (
              <span className="text-[12px] font-medium text-[#A3A3A3] drop-shadow-sm">
                {format(dataDeposito, "dd/MM/yyyy")}
              </span>
            )}
          </div>

          {/* Data Publicação */}
          <div className="absolute left-[15%]">
            {dataPublicacao && (
              <span className="text-[12px] font-medium text-[#A3A3A3] drop-shadow-sm">
                {format(dataPublicacao, "dd/MM/yyyy")}
              </span>
            )}
          </div>

          {/* Data Fim Oposição */}
          <div className="absolute left-[40%]">
            {dataFimOposicao && (
              <span className="text-[12px] font-medium text-[#A3A3A3] drop-shadow-sm">
                {format(dataFimOposicao, "dd/MM/yyyy")}
              </span>
            )}
          </div>

          {/* Data Mérito */}
          <div className="absolute left-[84%] top-[-20px]">
            {dataMeritoEstimada && (
              <div className="flex items-center gap-1 flex-col">
                <span className="text-[12px] font-medium italic text-[#45B063]">
                  Previsão
                </span>
                <span className="text-[12px] font-bold italic text-[#45B063]">
                  {format(dataMeritoEstimada, "MMM, yyyy", {
                    locale: ptBR,
                  }).replace(".", "").replace(/^\w/, c => c.toUpperCase())}
                </span>
              </div>
            )}
          </div>
        </div>

        {/* Linha do tempo e marcadores */}
        <div className="relative ml-2">
          {/* Barra de progresso */}
          <div className="relative">
            {/* Barra cinza de fundo */}
            <div className="h-[2px] bg-[#C3C3C3]" />
            
            {/* Barra azul de progresso */}
            <div
              className="absolute top-[-2.5px] left-0 h-[7px] bg-[#4597B5] transition-all duration-300"
              style={{ width: `${getProgressoAtual()}%` }}
            />
          </div>

          {/* Marcadores */}
          <div className="absolute w-full" style={{ top: "-10px" }}>
            {/* Protocolo */}
            <div className="absolute -left-2.5">
              {dataDeposito && new Date() >= dataDeposito ? (
                <div className="w-5 h-5 rounded-full bg-[#4597B5] shadow-md flex items-center justify-center">
                  <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2.5} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
              ) : (
                <div className="w-5 h-5 rounded-full border-2 border-[#C3C3C3] bg-white shadow-md" />
              )}
            </div>

            {/* Publicação */}
            <div className="absolute left-[15%]">
              {dataPublicacao && new Date() >= dataPublicacao ? (
                <div className="w-5 h-5 rounded-full bg-[#4597B5] shadow-md flex items-center justify-center">
                  <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2.5} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
              ) : (
                <div className="w-5 h-5 rounded-full border-2 border-[#C3C3C3] bg-white shadow-md" />
              )}
            </div>

            {/* Fim da fase de oposição */}
            <div className="absolute left-[40%]">
              {dataFimOposicao && new Date() >= dataFimOposicao ? (
                <div className="w-5 h-5 rounded-full bg-[#4597B5] shadow-md flex items-center justify-center">
                  <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2.5} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
              ) : (
                <div className="w-5 h-5 rounded-full border-2 border-[#C3C3C3] bg-white shadow-md" />
              )}
            </div>

            {/* Análise de mérito */}
            <div className="absolute left-[84%]">
              {temDespachoMerito ? (
                <div className="w-5 h-5 rounded-full bg-[#4597B5] shadow-md flex items-center justify-center">
                  <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2.5} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
              ) : (
                <div className="w-5 h-5 rounded-full border-2 border-[#C3C3C3] bg-white shadow-md" />
              )}
            </div>
          </div>
        </div>

        {/* Container para nomes das etapas (abaixo da linha) */}
        <div className="absolute w-full" style={{ top: "20px" }}>
          {/* Nome Protocolo */}
          <div className="absolute left-0">
            <span className="text-[14px] font-semibold text-black drop-shadow-sm">
              Protocolo
            </span>
          </div>

          {/* Nome Publicação */}
          <div className="absolute left-[15%]">
            <span className="text-[14px] font-semibold text-black drop-shadow-sm">
              Publicação
            </span>
          </div>

          {/* Nome Fim Oposição */}
          <div className="absolute left-[40%]">
            <span className="text-[14px] font-semibold text-black drop-shadow-sm">
              Fim da fase de oposição
            </span>
          </div>

          {/* Nome Mérito */}
          <div className="absolute left-[84.5%]">
            <span className="text-[14px] font-semibold text-black drop-shadow-sm">
              Análise de mérito
            </span>
          </div>
        </div>
      </div>
    </div>
  );
} 