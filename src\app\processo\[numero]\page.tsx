import React from 'react';
import Link from 'next/link';
import ProcessoClientWrapper from './ProcessoClientWrapper';

// Função para buscar dados do processo pelo número usando nossa API (Server Component)
async function getProcessoByNumeroServer(numero: string, baseUrl: string): Promise<any> {
  try {
    if(numero) {
      // Faz a chamada para nossa API interna com URL completa
      const url = `${baseUrl}/api/processo/${numero}`;
      const res = await fetch(url, {
        // Adiciona cache: 'no-store' para evitar cache em Server Components
        cache: 'no-store'
      });
      console.log(res);
    if (!res.ok) {
      console.error(`Erro ao buscar processo ${numero}:`, res.statusText);
      return null;
    }

      return await res.json();
    }
    return null;
  } catch (error) {
    console.error('Erro ao buscar processo:', error);
    return null;
  }
}

interface ProcessoPageProps {
  params: Promise<{
    numero: string;
  }>;
}

export default async function ProcessoPage({ params }: ProcessoPageProps) {
  // Aguardar params antes de acessar suas propriedades
  const resolvedParams = await params;
  const numero = resolvedParams.numero;
  
  // Construir a URL completa para o Server Component
  const baseUrl = "https://cliente.registre.se"
  
  const processo = await getProcessoByNumeroServer(numero, baseUrl);

  return (
    <div className="container mx-auto p-6">    
      <Link href="/cliente" className="mb-6 inline-block bg-verde hover:bg-verdeEscuro text-white font-semibold py-2 px-4 rounded shadow transition duration-300 ease-in-out">
        &larr; Voltar para Meus Processos
      </Link>
    
      {/* Se não encontrar o processo, exibe mensagem de erro */}
      {!processo ? (
        <div className="bg-red-50 border border-red-200 rounded-lg p-6">
          <h1 className="text-2xl font-bold mb-4 text-red-800">Processo não encontrado</h1>
          <p className="text-red-600">Não foi possível encontrar o processo com número {numero}.</p>
        </div>
      ) : (
        /* Usamos um Client Component wrapper para lidar com o hook useProcessoStatus */
        <ProcessoClientWrapper processo={processo} />
      )}
    </div>
  );
} 