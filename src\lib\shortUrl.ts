import { prisma } from '@/lib/prisma';

/**
 * Gera um código curto aleatório (URL-safe)
 */
function generateShortCode(length: number = 6): string {
  const chars = 'ABCDEFGHJKLMNPQRSTUVWXYZabcdefghjkmnpqrstuvwxyz23456789';
  let result = '';
  
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  
  return result;
}

/**
 * Cria uma URL encurtada para um token de auto-login
 */
export async function createShortUrl(
  longToken: string, 
  clienteId?: number,
  expiresInDays?: number
): Promise<string | null> {
  try {
    let shortCode: string;
    let attempts = 0;
    const maxAttempts = 10;
    
    // Tenta gerar um código único
    do {
      shortCode = generateShortCode(6);
      attempts++;
      
      if (attempts > maxAttempts) {
        // Se não conseguir em 10 tentativas, usa 8 caracteres
        shortCode = generateShortCode(8);
        break;
      }
      
      // Verifica se já existe
      const existing = await prisma.shortUrl.findUnique({
        where: { shortCode }
      });
      
      if (!existing) break;
      
    } while (attempts <= maxAttempts);
    
    // Calcular expiração se especificado
    const expiresAt = expiresInDays 
      ? new Date(Date.now() + expiresInDays * 24 * 60 * 60 * 1000)
      : null;
    
    // Criar registro no banco
    const createData = {
      shortCode,
      longToken,
      isActive: true,
      ...(clienteId && { clienteId }),
      ...(expiresAt && { expiresAt })
    };
    
    await prisma.shortUrl.create({
      data: createData
    });
    
    console.log(`Short URL criada: ${shortCode} -> ${longToken.substring(0, 20)}...`);
    return shortCode;
    
  } catch (error) {
    console.error('Erro ao criar short URL:', error);
    return null;
  }
}

/**
 * Recupera o token longo a partir do código curto
 */
export async function getLongToken(shortCode: string): Promise<{
  longToken: string;
  clienteId?: number;
} | null> {
  try {
    const shortUrl = await prisma.shortUrl.findUnique({
      where: { shortCode },
      include: { Cliente: true }
    });
    
    if (!shortUrl) {
      return null;
    }
    
    // Verificar se está ativo
    if (!shortUrl.isActive) {
      console.warn(`Short URL inativa: ${shortCode}`);
      return null;
    }
    
    // Verificar expiração
    if (shortUrl.expiresAt && shortUrl.expiresAt < new Date()) {
      console.warn(`Short URL expirada: ${shortCode}`);
      
      // Marcar como inativa
      await prisma.shortUrl.update({
        where: { id: shortUrl.id },
        data: { isActive: false }
      });
      
      return null;
    }
    
    // ✅ REMOVIDO: Verificação de limite de uso (campo maxUsage removido do schema)
    
    // Incrementar contador de uso
    await prisma.shortUrl.update({
      where: { id: shortUrl.id },
      data: { usageCount: { increment: 1 } }
    });
    
    console.log(`Short URL acessada: ${shortCode} (uso ${shortUrl.usageCount + 1})`);
    
    return {
      longToken: shortUrl.longToken,
      clienteId: shortUrl.clienteId || undefined
    };
    
  } catch (error) {
    console.error('Erro ao buscar short URL:', error);
    return null;
  }
}

/**
 * Gera um short URL completo para auto-login
 */
export async function createAutoLoginShortUrl(
  identificador: string,
  senha: string,
  baseUrl: string,
  clienteId?: number,
  expiresInDays?: number
): Promise<string | null> {
  try {
    // Importar função de auto-login
    const { encryptCredentials } = await import('./autoLogin');
    
    // Gerar token longo
    const longToken = encryptCredentials(identificador, senha);
    
    // Criar short URL
    const shortCode = await createShortUrl(longToken, clienteId, expiresInDays);
    
    if (!shortCode) {
      return null;
    }
    
    // Retornar URL completa (diretamente com shortcode)
    return `${baseUrl}/${shortCode}`;
    
  } catch (error) {
    console.error('Erro ao criar auto-login short URL:', error);
    return null;
  }
}

/**
 * Limpar short URLs expiradas (função de manutenção)
 */
export async function cleanupExpiredShortUrls(): Promise<number> {
  try {
    const result = await prisma.shortUrl.updateMany({
      where: {
        expiresAt: { lt: new Date() },
        isActive: true
      },
      data: { isActive: false }
    });
    
    console.log(`Limpeza: ${result.count} short URLs expiradas marcadas como inativas`);
    return result.count;
    
  } catch (error) {
    console.error('Erro na limpeza de short URLs:', error);
    return 0;
  }
} 