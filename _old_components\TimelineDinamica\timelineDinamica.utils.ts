import { format as formatFns, isValid } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { EtapaTimelineGerada, StatusEtapaGerada } from '@/lib/timelineNovasTypes'; // Importar tipos

export const formatDate = (
  date: Date | string | null | undefined, 
  formatString: string = "dd/MM/yyyy"
): string => {
  if (!date) return '';
  
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  if (!isValid(dateObj)) return 'Data inválida'; // Verifica se a data é válida

  try {
    return formatFns(dateObj, formatString, { locale: ptBR });
  } catch (error) {
    console.error("Erro ao formatar data:", date, error);
    return 'Erro data'; // Retorna algo em caso de erro na formatação
  }
};

/**
 * Calcula o percentual de progresso da timeline horizontal.
 * Esta é uma implementação inicial e precisará ser refinada com base nas regras de negócio específicas.
 */
export const calcularProgressoTimeline = (
  etapas: EtapaTimelineGerada[],
  // dataReferencia?: Date // Poderia ser hoje, ou uma data específica para simulação
): number => {
  if (!etapas || etapas.length === 0) return 0;

  const hoje = new Date(); // Ou dataReferencia
  const numeroDeEtapas = etapas.length;

  let indiceEtapaAtual = -1;
  let etapaConcluidaMaisRecente = -1;

  for (let i = 0; i < numeroDeEtapas; i++) {
    if (etapas[i].status === 'CONCLUIDA') {
      etapaConcluidaMaisRecente = i;
    }
    if (etapas[i].status === 'ATUAL') {
      indiceEtapaAtual = i;
      break; // Assume que há apenas uma etapa ATUAL
    }
  }

  if (indiceEtapaAtual !== -1) {
    // Se temos uma etapa ATUAL, o progresso vai até o início dela.
    // E se for um PRAZO, calcula o progresso dentro dela.
    const etapaAtual = etapas[indiceEtapaAtual];
    let progressoNaEtapaAtual = 0;

    if (etapaAtual.etapaDefinicao.tipo === 'PRAZO' && etapaAtual.dataInicio && etapaAtual.dataFim) {
      const inicio = new Date(etapaAtual.dataInicio).getTime();
      const fim = new Date(etapaAtual.dataFim).getTime();
      const agora = hoje.getTime();
      if (agora >= fim) {
        progressoNaEtapaAtual = 100;
      } else if (agora > inicio) {
        progressoNaEtapaAtual = ((agora - inicio) / (fim - inicio)) * 100;
      }
    }
    // Progresso até o início da etapa atual + progresso dentro da etapa atual (dividido pelo número de segmentos)
    if (numeroDeEtapas <= 1) return progressoNaEtapaAtual > 0 ? progressoNaEtapaAtual : (etapaAtual.status === 'CONCLUIDA' ? 100 : 0); 
    return (indiceEtapaAtual / (numeroDeEtapas - 1)) * 100 + (progressoNaEtapaAtual / (numeroDeEtapas - 1));

  } else if (etapaConcluidaMaisRecente !== -1) {
    // Se não há etapa ATUAL, mas há CONCLUIDAS, o progresso vai até o final da última concluída.
    if (numeroDeEtapas <= 1) return 100;
    return ((etapaConcluidaMaisRecente + 1) / (numeroDeEtapas -1)) * 100; // Ajuste para ir até o *fim* da última concluída
  }
  
  // Se nenhuma etapa ATUAL ou CONCLUIDA, progresso é 0.
  // Ou se a primeira etapa for PROXIMA e tiver data de início no passado, podemos ajustar.
  return 0;
}; 