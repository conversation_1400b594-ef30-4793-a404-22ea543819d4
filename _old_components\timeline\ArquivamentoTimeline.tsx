import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';

interface ArquivamentoTimelineProps {
  dataDeposito: Date;
  dataArquivamento: Date;
}

export default function ArquivamentoTimeline({
  dataDeposito,
  dataArquivamento,
}: ArquivamentoTimelineProps) {
  const hoje = new Date();
  
  // Define os pontos de referência visuais na timeline (%)
  const posArquivamento = 100;

  // Calcula o progresso atual baseado nas datas
  const getProgressoAtual = () => {
    const tHoje = hoje.getTime();
    if (tHoje < dataDeposito.getTime()) return 0;

    const tDeposito = dataDeposito.getTime();
    const tArquivamento = dataArquivamento.getTime();
    
    // Função para interpolação linear
    const interpolate = (startT: number, endT: number, startP: number, endP: number) => {
      if (tHoje < startT) return startP;
      if (tHoje >= endT) return endP;
      if (endT <= startT) return startP;
      const progressRatio = (tHoje - startT) / (endT - startT);
      return startP + progressRatio * (endP - startP);
    };

    // Interpola entre depósito e arquivamento
    return interpolate(tDeposito, tArquivamento, 0, posArquivamento);
  };

  return (
    <div className="mt-8 mb-4">
      <div className="relative">
        {/* Container para datas (acima da linha) */}
        <div className="absolute w-full" style={{ bottom: "42px" }}>
          {/* Data Protocolo */}
          <div className="absolute left-0">
            <span className="text-[12px] font-medium text-[#A3A3A3] drop-shadow-sm">
              {format(dataDeposito, "dd/MM/yyyy")}
            </span>
          </div>

          {/* Data Arquivamento */}
          <div className="absolute right-0 text-right">
            <span className="text-[12px] font-medium text-[#A3A3A3] drop-shadow-sm">
              {format(dataArquivamento, "dd/MM/yyyy")}
            </span>
          </div>
        </div>

        {/* Linha do tempo e marcadores */}
        <div className="relative ml-2">
          {/* Barra de progresso */}
          <div className="relative">
            {/* Barra cinza de fundo */}
            <div className="h-[2px] bg-[#C3C3C3]" />
            
            {/* Barra azul de progresso - vai até 100% se arquivado */}
            <div
              className="absolute top-[-2.5px] left-0 h-[7px] bg-[#4597B5] transition-all duration-300"
              style={{ width: `${getProgressoAtual()}%` }}
            />
          </div>

          {/* Marcadores */}
          <div className="absolute w-full" style={{ top: "-10px" }}>
            {/* Protocolo */}
            <div className="absolute -left-2.5">
              <div className="w-5 h-5 rounded-full bg-[#4597B5] shadow-md flex items-center justify-center">
                <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2.5} d="M5 13l4 4L19 7" />
                </svg>
              </div>
            </div>

            {/* Arquivamento */}
            <div className="absolute right-0">
              <div className="w-5 h-5 rounded-full bg-[#4597B5] shadow-md flex items-center justify-center">
                 <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                   <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2.5} d="M5 13l4 4L19 7" />
                 </svg>
              </div>
            </div>
          </div>
        </div>

        {/* Container para nomes das etapas (abaixo da linha) */}
        <div className="absolute w-full" style={{ top: "20px" }}>
          {/* Nome Protocolo */}
          <div className="absolute left-0">
            <span className="text-[14px] font-semibold text-black drop-shadow-sm block">
              Protocolo
            </span>
          </div>

          {/* Nome Arquivamento */}
          <div className="absolute right-0">
            <span className="text-[14px] font-semibold text-black drop-shadow-sm text-right block">
              Arquivamento
            </span>
          </div>
        </div>
      </div>
    </div>
  );
} 