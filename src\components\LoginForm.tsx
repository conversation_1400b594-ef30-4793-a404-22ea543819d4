'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { useUserPreferences } from '@/store/userPreferences';
import { saveClienteSession, getClienteSession, hasValidSession } from '@/lib/persistentSession';

interface LoginFormProps {
  initialIdentificador?: string;
  isIdentificadorDisabled?: boolean;
  redirectUrl?: string | null;
}

export default function LoginForm({ 
  initialIdentificador = '', 
  isIdentificadorDisabled = false, 
  redirectUrl = null
}: LoginFormProps) {
  const router = useRouter();
  
  // Estado local
  const [senha, setSenha] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [erro, setErro] = useState<string>('');
  
  // Estado global das preferências do usuário
  const {
    identifier,
    setIdentifier,
    hasAcceptedTerms,
    acceptTerms,
    keepConnected,
    setKeepConnected
  } = useUserPreferences();
  
  // Usar o identificador do estado global ou da prop
  const [identificador, setIdentificador] = useState<string>(identifier || initialIdentificador);
  
  // Estado local temporário para controlar o checkbox de termos
  const [aceitarTermos, setAceitarTermos] = useState<boolean>(hasAcceptedTerms);
  
  // Estado local temporário para controlar o checkbox de manter conectado
  const [manterConectado, setManterConectado] = useState<boolean>(keepConnected);
  
  // ✅ NOVO: Verificar sessão persistente ao carregar a página
  useEffect(() => {
    const checkPersistentSession = () => {
      if (hasValidSession()) {
        const session = getClienteSession();
        if (session) {
          console.log('🔄 Sessão persistente encontrada, redirecionando...', session);
          router.push('/cliente');
          return;
        }
      }
    };

    // Só verificar se não foi fornecido um identificador específico
    if (!initialIdentificador) {
      checkPersistentSession();
    }
  }, [router, initialIdentificador]);
  
  // Atualiza o estado do identificador quando a prop initialIdentificador mudar
  useEffect(() => {
    if (initialIdentificador) {
      console.log('LoginForm recebeu novo identificador:', initialIdentificador);
      setIdentificador(initialIdentificador);
      setIdentifier(initialIdentificador); // Atualiza também no estado global
    }
  }, [initialIdentificador, setIdentifier]);
  
  // Atualiza o estado local quando o estado global mudar
  useEffect(() => {
    setIdentificador(identifier || '');
    setAceitarTermos(hasAcceptedTerms);
    setManterConectado(keepConnected);
  }, [identifier, hasAcceptedTerms, keepConnected]);
  
  // Handler para manter conectado
  const handleManterConectadoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const isChecked = e.target.checked;
    setManterConectado(isChecked);
    setKeepConnected(isChecked); // Atualiza no estado global
  };
  
  // Handler para aceitar termos
  const handleAceitarTermosChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const isChecked = e.target.checked;
    setAceitarTermos(isChecked);
    if (isChecked) {
      acceptTerms(); // Atualiza no estado global, só podemos aceitar (não desaceitar)
    }
  };
  
  // Handler para alteração do identificador
  const handleIdentificadorChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setIdentificador(value);
    setIdentifier(value); // Atualiza no estado global
  };
  
  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setErro('');
    
    if (!identificador) {
      setErro('Por favor, digite o identificador');
      return;
    }
    
    if (!senha) {
      setErro('Por favor, digite sua senha');
      return;
    }

    if (!aceitarTermos) {
      setErro('Você precisa aceitar os termos de uso para continuar');
      return;
    }
    
    try {
      setLoading(true);
      
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          identificador, 
          senha,
          manterConectado 
        }),
      });
      
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Erro ao fazer login');
      }
      
      // ✅ NOVO: Salvar sessão persistente no localStorage
      if (data.sessionPersistence && data.cliente) {
        saveClienteSession({
          clienteId: data.cliente.id,
          identificador: data.cliente.identificador,
          nome: data.cliente.nome,
          email: data.cliente.email,
          telefone: data.cliente.telefone
        });
        console.log('💾 Sessão persistente salva no localStorage');
      }
      
      // Define a URL de destino após o login
      const destinationUrl = redirectUrl || '/cliente';
      console.log(`Login bem-sucedido. Redirecionando para: ${destinationUrl}`);
      
      // Redirecionar para a página de destino
      router.push(destinationUrl);
      
    } catch (error: any) {
      setErro(error.message || 'Erro ao fazer login. Verifique suas credenciais.');
      console.error('Erro de login:', error);
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <>
      <div className="text-center bg-[#45B063] w-full h-[93px] flex items-center justify-center rounded-tr-lg rounded-tl-lg">
        <Image
          src="/logo.svg"
          alt="Logo"
          width={200}
          height={80}
          className="mx-auto"
        />
      </div>
      <div className="bg-gray-50 p-8 rounded-b-lg shadow-lg">
        {erro && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {erro}
          </div>
        )}

        <form onSubmit={handleLogin} className="space-y-6">
          <div>
            <label
              htmlFor="identificador"
              className="block text-TextosEscurosEBg text-sm font-medium mb-2"
            >
              Identificador
            </label>
            <input
              type="text"
              id="identificador"
              value={identificador}
              onChange={handleIdentificadorChange}
              disabled={isIdentificadorDisabled}
              placeholder="Digite os últimos 8 números do seu telefone..."
              className="w-full bg-white px-4 py-2 border-2 border-[#EFF1F2] rounded-md outline-none  focus:outline-none"
            />
          </div>

          <div>
            <label
              htmlFor="senha"
              className="block text-TextosEscurosEBg text-sm font-medium mb-2"
            >
              Senha
            </label>
            <input
              type="password"
              id="senha"
              value={senha}
              onChange={(e) => setSenha(e.target.value)}
              placeholder="Digite sua senha"
              className="w-full bg-white px-4 py-2 border-2 border-[#EFF1F2] rounded-md outline-none  focus:outline-none"
            />
            <p className="mt-1 text-sm text-gray-500">
              Sua senha são os 3 últimos dígitos do seu CPF/CNPJ
            </p>
          </div>

          <div className="flex items-center">
            <input
              type="checkbox"
              id="manterConectado"
              checked={manterConectado}
              onChange={handleManterConectadoChange}
              className="w-4 h-4 text-verde border-gray-300 rounded focus:ring-verde"
            />
            <label
              htmlFor="manterConectado"
              className="ml-2 block text-sm text-TextosEscurosEBg"
            >
              Mantenha-me conectado
            </label>
          </div>

          <div className="flex items-center">
            <input
              type="checkbox"
              id="aceitarTermos"
              checked={aceitarTermos}
              onChange={handleAceitarTermosChange}
              className="w-4 h-4 text-verde border-gray-300 rounded focus:ring-verde"
            />
            <label
              htmlFor="aceitarTermos"
              className="ml-2 block text-sm text-TextosEscurosEBg"
            >
              Eu aceito os <a href="/termos" className="text-azul underline hover:text-azulHover">termos de uso</a>
            </label>
          </div>

          <button
            type="submit"
            disabled={loading}
            className="w-full bg-verde hover:bg-verdeEscuro text-branco font-bold py-2 px-4 rounded-md transition duration-300 ease-in-out flex justify-center items-center"
          >
            {loading ? (
              <svg
                className="animate-spin h-5 w-5 mr-3 text-branco"
                viewBox="0 0 24 24"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                ></circle>
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
            ) : (
              "Entrar"
            )}
          </button>
        </form>
      </div>
    </>
  );
} 