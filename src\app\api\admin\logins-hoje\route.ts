import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getAdminFromRequest } from '@/lib/adminAuth';

export async function GET(request: NextRequest) {
  try {
    // Verificar autenticação admin (permite tanto MASTER quanto COMUM)
    const user = await getAdminFromRequest(request);
    if (!user) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }
    const hoje = new Date();
    const inicioHoje = new Date(hoje.getFullYear(), hoje.getMonth(), hoje.getDate());
    const fimHoje = new Date(inicioHoje.getTime() + 24 * 60 * 60 * 1000);

    // Buscar todas as sessões de login de hoje com dados do cliente
    const sessõesHoje = await prisma.sessionLog.findMany({
      where: {
        loginAt: {
          gte: inicioHoje,
          lt: fimHoje,
        },
      },
      include: {
        Cliente: {
          select: {
            id: true,
            nome: true,
            autoLoginUrl: true,
            identificador: true,
          }
        }
      },
      orderBy: {
        loginAt: 'desc'
      }
    });

    // Para cada sessão, verificar se o cliente fez download hoje
    const sessõesComDownload = await Promise.all(
      sessõesHoje.map(async (sessao) => {
        const fezDownload = await prisma.protocoloDownloadLog.findFirst({
          where: {
            clienteId: sessao.clienteId,
            downloadAt: {
              gte: inicioHoje,
              lt: fimHoje,
            },
            success: true,
          }
        });

        return {
          id: sessao.id,
          clienteId: sessao.clienteId,
          nome: sessao.Cliente.nome || 'Nome não informado',
          autoLoginUrl: sessao.Cliente.autoLoginUrl,
          identificador: sessao.identificador,
          loginAt: sessao.loginAt,
          fezDownload: !!fezDownload,
        };
      })
    );

    // Contar logins únicos de hoje
    const loginsUnicos = await prisma.sessionLog.findMany({
      where: {
        loginAt: {
          gte: inicioHoje,
          lt: fimHoje,
        },
      },
      select: { clienteId: true },
      distinct: ['clienteId'],
    });

    return NextResponse.json({
      totalLoginsHoje: loginsUnicos.length,
      sessões: sessõesComDownload,
    });

  } catch (error) {
    console.error('Erro ao buscar logins de hoje:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
} 