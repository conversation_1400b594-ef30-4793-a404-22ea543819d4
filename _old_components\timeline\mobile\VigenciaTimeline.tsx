import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';

interface VigenciaTimelineProps {
  dataDeposito: Date;
  dataConcessao: Date;
  dataVigencia: Date; // Data final de vigência (renovação)
}

export default function VigenciaTimeline({
  dataDeposito,
  dataConcessao,
  dataVigencia,
}: VigenciaTimelineProps) {
  const hoje = new Date();
  
  // Define os pontos de referência visuais na timeline (%)
  const posConcessao = 50;
  const posRenovacao = 100; // Renovação (dataVigencia) no final

  // Calcula o progresso atual baseado nas datas e posições visuais
  const getProgressoAtual = () => {
    const tHoje = hoje.getTime();
    if (tHoje < dataDeposito.getTime()) return 0;

    const tDeposito = dataDeposito.getTime();
    const tConcessao = dataConcessao.getTime();
    const tVigencia = dataVigencia.getTime(); // Usando dataVigencia como ponto final
    
    // Função para interpolação linear
    const interpolate = (startT: number, endT: number, startP: number, endP: number) => {
      if (tHoje < startT) return startP;
      if (tHoje >= endT) return endP;
      if (endT <= startT) return startP;
      const progressRatio = (tHoje - startT) / (endT - startT);
      return startP + progressRatio * (endP - startP);
    };

    // Determina em qual segmento da timeline estamos
    if (tHoje >= tVigencia) return posRenovacao;
    if (tHoje >= tConcessao) {
      return interpolate(tConcessao, tVigencia, posConcessao, posRenovacao);
    }
    return interpolate(tDeposito, tConcessao, 0, posConcessao);
  };

  // Componente para um marcador da timeline
  const TimelineMarker = ({ 
    data, 
    titulo, 
    descricao, 
    isCompleted,
  }: { 
    data: Date, 
    titulo: string, 
    descricao: string,
    isCompleted: boolean,
  }) => (
    <div className="flex mb-6 relative">
      {/* Data (lado esquerdo) */}
      <div className="w-24 flex-shrink-0 mr-4 text-right">
        <span className="text-sm font-medium text-[#A3A3A3]">
          {format(data, "dd/MM/yyyy")}
        </span>
      </div>

      {/* Marcador e linha da timeline (centro) */}
      <div className="flex flex-col items-center">
        {/* Marcador */}
        <div className={`w-5 h-5 rounded-full ${
          isCompleted 
            ? "bg-[#4597B5] shadow-md flex items-center justify-center" 
            : "border-2 border-[#C3C3C3] bg-white shadow-md"
        }`}>
          {isCompleted && (
            <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2.5} d="M5 13l4 4L19 7" />
            </svg>
          )}
        </div>
        
        {/* Linha vertical (não exibir após o último item) */}
        {titulo !== "Renovação" && (
          <div className="w-1 flex-grow bg-[#C3C3C3] relative">
            {/* Barra de progresso azul */}
            {isCompleted && (
              <div className="absolute top-0 left-0 w-full h-full bg-[#4597B5]"></div>
            )}
          </div>
        )}
      </div>

      {/* Texto (lado direito) */}
      <div className="ml-4 flex-grow">
        <h3 className="text-base font-semibold text-black">{titulo}</h3>
        <p className="text-sm text-gray-500">{descricao}</p>
      </div>
    </div>
  );

  return (
    <div className="pt-4 pb-2 px-2">
      <div className="relative">
        {/* Marcadores da Timeline */}
        <TimelineMarker
          data={dataDeposito}
          titulo="Protocolo"
          descricao="Data em que o pedido foi depositado no INPI."
          isCompleted={hoje >= dataDeposito}
        />
        
        <TimelineMarker
          data={dataConcessao}
          titulo="Concessão"
          descricao="Data em que o registro foi concedido pelo INPI."
          isCompleted={hoje >= dataConcessao}
        />
        
        <TimelineMarker
          data={dataVigencia}
          titulo="Renovação"
          descricao="Data limite para renovar o registro da marca."
          isCompleted={hoje >= dataVigencia}
        />
      </div>
    </div>
  );
} 