import { format, addDays } from 'date-fns';
import { ptBR } from 'date-fns/locale';

interface DetalhesDespacho {
  nome: string | null;
}

interface RPI {
  dataPublicacao: string;
  numero: string;
}

interface Despacho {
  codigo: string;
  nome: string | null;
  DetalhesDespacho: DetalhesDespacho | null;
  RPI: RPI | null;
}

interface IndeferimentoTimelineProps {
  dataIndeferimento: Date;
  despachos: Despacho[];
}

export default function IndeferimentoTimeline({
  dataIndeferimento,
  despachos,
}: IndeferimentoTimelineProps) {
  const hoje = new Date();

  // Encontra o despacho de notificação de recurso após o indeferimento
  const despachoNotificacaoRecurso = despachos.find(despacho => 
    despacho.nome?.toLowerCase().includes("notificação de recurso") &&
    despacho.DetalhesDespacho?.nome?.toLowerCase().includes("recurso contra indeferimento") &&
    despacho.RPI?.dataPublicacao &&
    new Date(despacho.RPI.dataPublicacao) > dataIndeferimento
  );

  // Data do prazo do recurso (60 dias após indeferimento)
  const dataPrazoRecurso = addDays(dataIndeferimento, 60);
  
  // Verifica se o prazo passou sem recurso
  const prazoPerdido = hoje > dataPrazoRecurso && !despachoNotificacaoRecurso;

  // Encontra o despacho de decisão do recurso
  const despachoDecisaoRecurso = despachos.find(despacho => 
    despacho.nome?.toLowerCase().includes("recurso provido") ||
    despacho.nome?.toLowerCase().includes("recurso não provido") ||
    despacho.nome?.toLowerCase().includes("recurso prejudicado")
  );

  // Verifica se existem despachos intermediários após o indeferimento
  const temSobrestamento = !prazoPerdido && despachos.some(despacho => 
    despacho.nome?.toLowerCase().includes("sobrestamento") &&
    despacho.RPI?.dataPublicacao &&
    new Date(despacho.RPI.dataPublicacao) > dataIndeferimento
  );

  const temExigencia = !prazoPerdido && despachos.some(despacho => 
    despacho.nome?.toLowerCase().includes("exigência") &&
    despacho.RPI?.dataPublicacao &&
    new Date(despacho.RPI.dataPublicacao) > dataIndeferimento
  );

  const temOutrosDespachos = !prazoPerdido && despachos.some(despacho => 
    !despacho.nome?.toLowerCase().includes("sobrestamento") &&
    !despacho.nome?.toLowerCase().includes("exigência") &&
    !despacho.nome?.toLowerCase().includes("recurso") &&
    !despacho.nome?.toLowerCase().includes("indeferimento") &&
    despacho.RPI?.dataPublicacao &&
    new Date(despacho.RPI.dataPublicacao) > dataIndeferimento
  );

  // Calcula a estimativa de dias para análise baseado no cenário
  const getEstimativaDiasAnalise = () => {
    if (prazoPerdido) return 0;
    if (temSobrestamento) return 996;
    if (temExigencia) return 777;
    if (temOutrosDespachos) return 588;
    return 530;
  };

  const dataDecisaoRecurso = despachoDecisaoRecurso?.RPI?.dataPublicacao
    ? new Date(despachoDecisaoRecurso.RPI.dataPublicacao)
    : null;

  const getProgressoAtual = () => {
    const tHoje = hoje.getTime();
    if (tHoje < dataIndeferimento.getTime()) return 0;

    const tIndeferimento = dataIndeferimento.getTime();
    const tPrazoRecurso = dataPrazoRecurso.getTime();

    // Se perdeu o prazo
    if (prazoPerdido) {
      if (tHoje >= tPrazoRecurso) return 15;
      return (tHoje - tIndeferimento) / (tPrazoRecurso - tIndeferimento) * 15;
    }

    const tDecisaoRecurso = dataDecisaoRecurso?.getTime() || 
      addDays(dataPrazoRecurso, getEstimativaDiasAnalise()).getTime();

    if (tHoje >= tDecisaoRecurso) return 84;
    if (tHoje >= tPrazoRecurso) {
      return 15 + ((tHoje - tPrazoRecurso) / (tDecisaoRecurso - tPrazoRecurso)) * 69;
    }
    return (tHoje - tIndeferimento) / (tPrazoRecurso - tIndeferimento) * 15;
  };

  const posicaoPrazoRecurso = getProgressoAtual();

  return (
    <div className="mt-8 mb-4">
      <div className="relative">
        {/* Container para datas (acima da linha) */}
        <div className="absolute w-full" style={{ bottom: "42px" }}>
          {/* Data Indeferimento */}
          <div className="absolute left-0">
            <span className="text-[12px] font-medium text-[#A3A3A3] drop-shadow-sm whitespace-nowrap">
              {format(dataIndeferimento, "dd/MM/yyyy")}
            </span>
          </div>

          {/* Data Prazo Recurso */}
          <div className="absolute left-[15%]">
            <span className="text-[12px] font-medium text-[#A3A3A3] drop-shadow-sm whitespace-nowrap">
              {format(dataPrazoRecurso, "dd/MM/yyyy")}
            </span>
          </div>

          {/* Data Decisão Recurso - só mostra se não perdeu o prazo */}
          {!prazoPerdido && (
            <div className="absolute left-[84%]">
              <span className="text-[12px] font-medium text-[#A3A3A3] drop-shadow-sm whitespace-nowrap">
                {dataDecisaoRecurso 
                  ? format(dataDecisaoRecurso, "dd/MM/yyyy")
                  : `Em análise (est. ${format(addDays(dataPrazoRecurso, getEstimativaDiasAnalise()), "MM/yyyy")})`}
              </span>
            </div>
          )}
        </div>

        {/* Linha do tempo e marcadores */}
        <div className="relative ml-2">
          {/* Barra de progresso */}
          <div className="relative">
            {/* Barra cinza de fundo */}
            <div className="h-[2px] bg-[#C3C3C3]" />
            
            {/* Barra azul de progresso */}
            <div
              className="absolute top-[-2.5px] left-0 h-[7px] bg-[#4597B5] transition-all duration-300"
              style={{ width: `${getProgressoAtual()}%` }}
            />
          </div>

          {/* Marcadores */}
          <div className="absolute w-full" style={{ top: "-10px" }}>
            {/* Indeferimento */}
            <div className="absolute -left-2.5">
              <div className="w-5 h-5 rounded-full bg-[#4597B5] shadow-md flex items-center justify-center">
                <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2.5} d="M5 13l4 4L19 7" />
                </svg>
              </div>
            </div>

            {/* Prazo Recurso */}
            <div className="absolute left-[15%]">
              <div className={`w-5 h-5 rounded-full shadow-md flex items-center justify-center ${
                prazoPerdido ? 'border-2 border-red-500 bg-white' :
                despachoNotificacaoRecurso || hoje >= dataPrazoRecurso ? 'bg-[#4597B5]' :
                'border-2 border-[#C3C3C3] bg-white'
              }`}>
                {!prazoPerdido && (despachoNotificacaoRecurso || hoje >= dataPrazoRecurso) && (
                  <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2.5} d="M5 13l4 4L19 7" />
                  </svg>
                )}
              </div>
            </div>

            {/* Decisão Recurso - só mostra se não perdeu o prazo */}
            {!prazoPerdido && (
              <div className="absolute left-[84%]">
                <div className={`w-5 h-5 rounded-full shadow-md flex items-center justify-center ${
                  despachoDecisaoRecurso ? 'bg-[#4597B5]' :
                  'border-2 border-[#C3C3C3] bg-white'
                }`}>
                  {despachoDecisaoRecurso && (
                    <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2.5} d="M5 13l4 4L19 7" />
                    </svg>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Container para nomes das etapas (abaixo da linha) */}
        <div className="absolute w-full" style={{ top: "20px" }}>
          {/* Nome Indeferimento */}
          <div className="absolute left-0">
            <span className="text-[14px] font-semibold text-black drop-shadow-sm whitespace-nowrap">
              Indeferimento
            </span>
          </div>

          {/* Nome Prazo Recurso */}
          <div className="absolute left-[15%]">
            <span className={`text-[14px] font-semibold drop-shadow-sm whitespace-nowrap ${prazoPerdido ? 'text-red-500' : 'text-black'}`}>
              {prazoPerdido ? 'Prazo expirado' : 'Prazo para recurso'}
              {despachoNotificacaoRecurso && (
                <em className="text-[10px] not-italic block">(Notificado)</em>
              )}
            </span>
          </div>

          {/* Nome Decisão Recurso - só mostra se não perdeu o prazo */}
          {!prazoPerdido && (
            <div className="absolute left-[84%]">
              <span className="text-[14px] font-semibold text-black drop-shadow-sm whitespace-nowrap">
                Análise do recurso
              </span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
} 