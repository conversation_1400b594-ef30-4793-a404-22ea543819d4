import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getAdminFromRequest } from '@/lib/adminAuth';

const DEFAULT_PAGE_SIZE = 20;

interface BuscaParams {
  termo?: string;           // Busca geral (nome marca, número processo)
  nomeMarca?: string;       // Busca específica por nome da marca
  numeroProcesso?: string;  // Busca específica por número
  identificadorCliente?: string; // Busca por identificador
  nomeCliente?: string;     // Busca por nome do cliente
  telefone?: string;        // Busca por telefone do cliente
  clienteId?: number;       // Busca por ID do cliente
  dataInicio?: string;      // Filtro por data de depósito
  dataFim?: string;
  status?: string[];        // Filtros por status (futuro)
  apenasProcuradorRegistrese?: boolean; // Filtro para processos do procurador Registre.se
  exactMatch?: boolean;     // Se true, busca exata; se false, busca radical (parcial)
  page?: number;
  pageSize?: number;
}

export async function GET(request: NextRequest) {
  try {
    // Verificar autenticação admin (permite tanto MASTER quanto COMUM)
    const user = await getAdminFromRequest(request);
    if (!user) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    
    // Extrair parâmetros de busca
    const filtros: BuscaParams = {
      termo: searchParams.get('termo') || undefined,
      nomeMarca: searchParams.get('nomeMarca') || undefined,
      numeroProcesso: searchParams.get('numeroProcesso') || undefined,
      identificadorCliente: searchParams.get('identificadorCliente') || undefined,
      nomeCliente: searchParams.get('nomeCliente') || undefined,
      telefone: searchParams.get('telefone') || undefined,
      clienteId: searchParams.get('clienteId') ? parseInt(searchParams.get('clienteId')!) : undefined,
      dataInicio: searchParams.get('dataInicio') || undefined,
      dataFim: searchParams.get('dataFim') || undefined,
      apenasProcuradorRegistrese: searchParams.get('apenasProcuradorRegistrese') === 'true',
      exactMatch: searchParams.get('exactMatch') === 'true',
      page: parseInt(searchParams.get('page') || '1', 10),
      pageSize: parseInt(searchParams.get('pageSize') || DEFAULT_PAGE_SIZE.toString(), 10),
    };

    // Construir WHERE clause dinâmico
    const whereConditions: any[] = [];

    // Função auxiliar para criar condição de busca baseada no tipo (exata ou radical)
    const createSearchCondition = (field: any, value: string) => {
      if (filtros.exactMatch) {
        return { equals: value, mode: 'insensitive' };
      } else {
        return { contains: value, mode: 'insensitive' };
      }
    };

    // Busca por termo geral
    if (filtros.termo) {
      whereConditions.push({
        OR: [
          { numero: createSearchCondition({}, filtros.termo) },
          { Marca: { nome: createSearchCondition({}, filtros.termo) } },
        ],
      });
    }

    // Busca específica por nome da marca
    if (filtros.nomeMarca) {
      whereConditions.push({
        Marca: { nome: createSearchCondition({}, filtros.nomeMarca) },
      });
    }

    // Busca por número do processo
    if (filtros.numeroProcesso) {
      whereConditions.push({
        numero: createSearchCondition({}, filtros.numeroProcesso),
      });
    }

    // Busca por identificador do cliente
    if (filtros.identificadorCliente) {
      console.log('identificadorCliente', filtros.identificadorCliente);
      
      // Se for o identificador especial "00000013", usar lógica especial
      if (filtros.identificadorCliente === '00000013') {
        whereConditions.push({
          Procurador: {
            nome: {
              contains: 'REGISTRE-SE LTDA',
              mode: 'insensitive',
            },
          },
          NOT: {
            Despacho: {
              some: {
                codigo: 'IPAS047',
              },
            },
          },
        });
      } else {
        // Para outros identificadores, buscar pelo cliente
        const cliente = await prisma.cliente.findFirst({
          where: { identificador: filtros.identificadorCliente },
        });
        
        if (cliente) {
          whereConditions.push({
            clienteId: cliente.id,
          });
        } else {
          // Se cliente não encontrado, buscar por identificador diretamente
          whereConditions.push({
            Cliente: { identificador: { contains: filtros.identificadorCliente, mode: 'insensitive' } },
          });
        }
      }
    }

    // Busca por nome do cliente
    if (filtros.nomeCliente) {
      whereConditions.push({
        Cliente: { nome: createSearchCondition({}, filtros.nomeCliente) },
      });
    }

    // Busca por telefone do cliente (ContatoCliente)
    if (filtros.telefone) {
      whereConditions.push({
        Cliente: {
          ContatoCliente: {
            some: {
              OR: [
                { telefone: { contains: filtros.telefone, mode: 'insensitive' } },
                { telefoneSegundario: { contains: filtros.telefone, mode: 'insensitive' } },
              ]
            }
          }
        },
      });
    }

    // Busca por ID do cliente
    if (filtros.clienteId) {
      whereConditions.push({
        clienteId: filtros.clienteId,
      });
    }

    // Filtros de data
    if (filtros.dataInicio || filtros.dataFim) {
      const dateFilter: any = {};
      if (filtros.dataInicio) {
        dateFilter.gte = new Date(filtros.dataInicio);
      }
      if (filtros.dataFim) {
        dateFilter.lte = new Date(filtros.dataFim);
      }
      whereConditions.push({
        dataDeposito: dateFilter,
      });
    }

    // Filtro específico para procurador Registre.se
    if (filtros.apenasProcuradorRegistrese) {
      whereConditions.push({
        Procurador: {
          id: '65b3c0c0-aa3b-4d89-85fb-2a144e538800'
        },
      });
    }

    const whereClause = whereConditions.length > 0 ? { AND: whereConditions } : {};

    console.log('🔍 Filtros recebidos:', filtros);
    console.log('🔧 Condições WHERE construídas:', JSON.stringify(whereConditions, null, 2));
    console.log('📋 Cláusula WHERE final:', JSON.stringify(whereClause, null, 2));

    // Include clause - IGUAL ao da API cliente/processos para compatibilidade total
    const includeClause = {
      Cliente: { 
        select: { 
          crmStageId: true, 
          identificador: true, 
          nome: true,
          autoLoginUrl: true,
          ContatoCliente: {
            select: {
              telefone: true,
              telefoneSegundario: true,
              email: true,
            }
          }
        } 
      },
      Marca: { include: { NCL: true } },
      Despacho: {
        orderBy: { RPI: { dataPublicacao: 'asc' as const } },
        include: {
          DetalhesDespacho: true,
          ProtocoloDespacho: true,
          RPI: { select: { dataPublicacao: true, numero: true } },
        },
      },
      Titular: { select: { nomeRazaoSocial: true, pais: true, uf: true } },
      Procurador: { select: { nome: true } },
    };

    // Contar total de processos
    const totalProcessos = await prisma.processo.count({
      where: whereClause,
    });
    console.log('totalProcessos:', totalProcessos);
    
    // Buscar processos com paginação
    const skip = (filtros.page! - 1) * filtros.pageSize!;
    const processosEncontrados = await prisma.processo.findMany({
      where: whereClause,
      include: includeClause,
      orderBy: {
        dataDeposito: 'desc',
      },
      skip,
      take: filtros.pageSize,
    });
    
    console.log('processosEncontrados length:', processosEncontrados.length);
    if (processosEncontrados.length > 0) {
      console.log('primeiro processo:', {
        id: processosEncontrados[0].id,
        numero: processosEncontrados[0].numero,
        marca: processosEncontrados[0].Marca?.nome,
        cliente: processosEncontrados[0].Cliente,
        despachos: processosEncontrados[0].Despacho?.length
      });
    }
    // Aplicar a mesma lógica de dataPublicacaoRPI da API original
    const textoDespachoPublicacao = "publicação de pedido de registro para oposição";
    const processosFinaisComDatas = processosEncontrados.map((processo: any) => {
      const despachoPublicacao = processo.Despacho?.find(
        (despacho: any) => despacho.nome?.toLowerCase().includes(textoDespachoPublicacao)
      );
      if (despachoPublicacao?.RPI?.dataPublicacao) {
        const dataPublicacao = new Date(despachoPublicacao.RPI.dataPublicacao);
        const dataFimOposicao = new Date(dataPublicacao);
        dataFimOposicao.setDate(dataFimOposicao.getDate() + 60);
        return {
          ...processo,
          dataPublicacaoRPI: dataPublicacao.toISOString(),
          dataOposicao: dataFimOposicao.toISOString()
        };
      }
      return processo;
    });

    // Calcular informações de paginação
    const totalPages = Math.ceil(totalProcessos / filtros.pageSize!);
    const hasNextPage = filtros.page! < totalPages;
    const hasPreviousPage = filtros.page! > 1;

    // Resposta compatível com ProcessoCard
    return NextResponse.json({
      processos: processosFinaisComDatas,
      totalProcessos,
      currentPage: filtros.page,
      pageSize: filtros.pageSize,
      totalPages,
      hasNextPage,
      hasPreviousPage,
      filtrosAplicados: filtros,
    });

  } catch (error) {
    console.error('Erro na busca administrativa:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
} 