const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function createAdminUser() {
  try {
    // Configurações do usuário master
    const email = "<EMAIL>";
    const nome = 'Administrador';
    const senha = '#Rgse//7599@'; // Altere esta senha!
    const role = 'MASTER';

    // Verificar se já existe
    const existingUser = await prisma.adminUser.findUnique({
      where: { email }
    });

    if (existingUser) {
      console.log('❌ Usuário admin já existe!');
      console.log(`Email: ${existingUser.email}`);
      console.log(`Nome: ${existingUser.nome}`);
      console.log(`Role: ${existingUser.role}`);
      return;
    }

    // Hash da senha
    const senhaHash = await bcrypt.hash(senha, 12);

    // Criar usuário
    const user = await prisma.adminUser.create({
      data: {
        email,
        nome,
        senha: senhaHash,
        role,
        ativo: true
      }
    });

    console.log('✅ Usuário admin criado com sucesso!');
    console.log('📧 Email:', email);
    console.log('🔑 Senha:', senha);
    console.log('👤 Nome:', nome);
    console.log('🛡️  Role:', role);
    console.log('');
    console.log('⚠️  IMPORTANTE: Altere a senha após o primeiro login!');
    console.log('🌐 Acesse: http://localhost:3000/admin/login');

  } catch (error) {
    console.error('❌ Erro ao criar usuário admin:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createAdminUser(); 