import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getAdminFromRequest, requireAdminRole } from '@/lib/adminAuth';

export async function GET(request: NextRequest) {
  try {
    // Verificar autenticação e role MASTER
    const user = await getAdminFromRequest(request);
    if (!user || !requireAdminRole(['MASTER'])(user)) {
      return NextResponse.json(
        { error: 'Acesso negado. Apenas usuários MASTER podem acessar esta funcionalidade.' },
        { status: 403 }
      );
    }
    const { searchParams } = new URL(request.url);
    const query = searchParams.get('q');
    const limit = parseInt(searchParams.get('limit') || '10');

    if (!query || query.length < 2) {
      return NextResponse.json({
        clientes: [],
        message: 'Digite pelo menos 2 caracteres para buscar'
      });
    }

    // Buscar clientes por nome, identificador ou autoLoginUrl
    const clientes = await prisma.cliente.findMany({
      where: {
        OR: [
          {
            nome: {
              contains: query,
              mode: 'insensitive'
            }
          },
          {
            identificador: {
              contains: query,
              mode: 'insensitive'
            }
          },
          {
            autoLoginUrl: {
              contains: query,
              mode: 'insensitive'
            }
          }
        ]
      },
      select: {
        id: true,
        nome: true,
        identificador: true,
        autoLoginUrl: true,
        numeroDocumento: true,
        _count: {
          select: {
            Processo: true
          }
        }
      },
      orderBy: [
        { nome: 'asc' }
      ],
      take: limit
    });

    return NextResponse.json({
      clientes: clientes.map(cliente => ({
        id: cliente.id,
        nome: cliente.nome || 'Nome não informado',
        identificador: cliente.identificador,
        autoLoginUrl: cliente.autoLoginUrl,
        numeroDocumento: cliente.numeroDocumento,
        totalProcessos: cliente._count.Processo
      })),
      total: clientes.length
    });

  } catch (error) {
    console.error('Erro ao buscar clientes:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
} 