import { Despacho_Interface } from "./appTypes";
import { EtapaDefinicao, definicoesEtapas } from "./etapasConfig";
import { ExigenciaInfo } from "./timelineNovasTypes";

// Função auxiliar para formatar data como DD/MM/AAAA
export function formatarData(data: Date | null): string {
  if (!data) return '';
  const dia = String(data.getDate()).padStart(2, '0');
  const mes = String(data.getMonth() + 1).padStart(2, '0'); // Mês é 0-indexado
  const ano = data.getFullYear();
  return `${dia}/${mes}/${ano}`;
}

// Função auxiliar para encontrar uma EtapaDefinicao pelo ID
export function encontrarDefinicaoEtapaPorId(id: string): EtapaDefinicao | undefined {
  return definicoesEtapas.find(etapa => etapa.id === id);
}

// Função auxiliar para calcular a data da próxima terça-feira
export function calcularProximaTerca(dataBase: Date, diasAproximados: number): Date {
  const dataAlvo = new Date(dataBase);
  dataAlvo.setDate(dataAlvo.getDate() + diasAproximados);

  const diaSemana = dataAlvo.getDay(); // 0 = Domingo, 1 = Segunda, ..., 6 = Sábado
  if (diaSemana !== 2) { // Se não for Terça
    const diasAteProximaTerca = (2 - diaSemana + 7) % 7;
    dataAlvo.setDate(dataAlvo.getDate() + diasAteProximaTerca);
  }
  return dataAlvo;
}

// Nomes de despachos que indicam publicação
export const nomesDespachoPublicacao: string[] = [
  "publicação de pedido de registro para oposição (exame formal concluído)",
  "republicação de pedido (por perda da prioridade)",
  "republicação de pedido",
  "publicação de pedido de registro para oposição (exame formal de designação concluído)"
].map(nome => nome.toLowerCase());

// Função auxiliar para verificar se um despacho é de publicação
export function isDespachoDePublicacao(despacho: Despacho_Interface): boolean {
  const nomeDespacho = despacho.nome?.toLowerCase();
  const nomeDetalhes = despacho.DetalhesDespacho?.nome?.toLowerCase();
  
  const matchNomeDespacho = nomeDespacho ? nomesDespachoPublicacao.includes(nomeDespacho) : false;
  const matchNomeDetalhes = nomeDetalhes ? nomesDespachoPublicacao.includes(nomeDetalhes) : false;
  
  return matchNomeDespacho || matchNomeDetalhes;
}

// Função auxiliar para encontrar a data da publicação mais recente
export function encontrarDataPublicacaoMaisRecente(despachos: Despacho_Interface[]): Date | null {
  let dataMaisRecente: Date | null = null;
  for (const despacho of despachos) {
    if (isDespachoDePublicacao(despacho) && despacho.RPI?.dataPublicacao) {
      const dataAtual = new Date(despacho.RPI.dataPublicacao);
      if (!dataMaisRecente || dataAtual > dataMaisRecente) {
        dataMaisRecente = dataAtual;
      }
    }
  }
  return dataMaisRecente;
}

// Função auxiliar para verificar se há oposição APÓS uma data específica
export function verificarOposicaoAposData(despachos: Despacho_Interface[], dataReferencia: Date): boolean {
  for (const despacho of despachos) {
    const dataDespachoOuProtocolo = despacho.RPI?.dataPublicacao ? new Date(despacho.RPI.dataPublicacao) :
                                    (despacho.ProtocoloDespacho && despacho.ProtocoloDespacho.length > 0 && despacho.ProtocoloDespacho[0].data) ? new Date(despacho.ProtocoloDespacho[0].data) : null;

    if (dataDespachoOuProtocolo && dataDespachoOuProtocolo >= dataReferencia) {
      if (despacho.nome?.toLowerCase().includes("notificação de oposição")) {
        return true;
      }
      if (despacho.ProtocoloDespacho?.some(p => p.codigoServico === "332")) { // Código para "Oposição"
        return true;
      }
    }
  }
  return false;
}

// Função auxiliar para encontrar a data do protocolo de manifestação (código 339)
export function encontrarDataProtocoloManifestacao(despachos: Despacho_Interface[]): Date | null {
  for (const despacho of despachos) {
    if (despacho.ProtocoloDespacho) {
      for (const protocolo of despacho.ProtocoloDespacho) {
        // Modificado para aceitar qualquer código que comece com "339"
        // Trata casos como "339", "339.1", "3391", "339.6", "3396"
        if (protocolo.codigoServico && protocolo.codigoServico.replace('.', '').startsWith('339') && protocolo.data) {
          return new Date(protocolo.data);
        }
      }
    }
  }
  return null;
}

// Função auxiliar para encontrar a data da RPI do despacho "Notificação de oposição" mais recente APÓS a publicação do pedido.
export function encontrarDataNotificacaoOposicaoMaisRecente(despachos: Despacho_Interface[], dataReferenciaPublicacaoPedido: Date): Date | null {
  let dataRpiNotificacaoMaisRecente: Date | null = null;

  for (const despacho of despachos) {
    if (despacho.nome?.toLowerCase().includes("notificação de oposição") && despacho.RPI?.dataPublicacao) {
      const dataRpiDespacho = new Date(despacho.RPI.dataPublicacao);
      if (dataRpiDespacho >= dataReferenciaPublicacaoPedido) {
        if (!dataRpiNotificacaoMaisRecente || dataRpiDespacho > dataRpiNotificacaoMaisRecente) {
          dataRpiNotificacaoMaisRecente = dataRpiDespacho;
        }
      }
    }
  }
  return dataRpiNotificacaoMaisRecente;
}

// --- Listas de Nomes de Despachos e Funções Auxiliares para Exigências, Arquivamentos, Sobrestamentos ---

export const nomesDespachoExigencia: string[] = [
  "exigência formal",
  "exigência de mérito",
  "exigência de conformidade",
  "exigência de mérito (em petição)",
  "exigência sobre alto renome",
  "exigência de pagamento",
  "notificação de novo impedimento legal em grau de recurso"
].map(nome => nome.toLowerCase());

export const nomesDespachoArquivamentoPorNaoCumprimento: string[] = [
  "decisão de considerar pedido inexistente por exigência de pagamento não respondida",
  "decisão de considerar pedido inexistente por exigência de pagamento não cumprida",
  "decisão de considerar pedido inexistente por exigência formal não respondida",
  "decisão de considerar pedido inexistente por exigência formal não cumprida",
  "arquivamento definitivo de pedido de registro por falta de cumprimento de exigência de mérito",
  "arquivamento definitivo de designação por falta de cumprimento de exigência de mérito"
].map(nome => nome.toLowerCase());

export const nomesDespachoArquivamentoOutros: string[] = [
  "decisão de considerar pedido inexistente por falta de pagamento",
  "arquivamento definitivo de pedido de registro por falta de procuração",
  "deferimento da petição - desistência de pedido de registro de marca", 
  "arquivamento definitivo de pedido de registro por falta de documentos de marca de certificação",
  "arquivamento definitivo de pedido de registro por falta de documentos de marca coletiva",
  "arquivamento de ofício de pedido de registro de marca",
  "cancelamento de ofício de registro de marca",
  "arquivamento definitivo de designação por falta de docs. de marca coletiva ou de certificação"
].map(nome => nome.toLowerCase());

export const nomeDespachoCumprimentoExigencia: string = 
  "deferimento da petição - cumprimento de exigência decorrente do exame formal em pedido de registro".toLowerCase();

export const nomesDespachoSobrestamento: string[] = [
  "sobrestamento do exame de mérito",
  "sobrestamento do exame de mérito (em petição)",
  "sobrestamento da instrução técnica",
  "sobrestamento"
].map(nome => nome.toLowerCase());

// Função genérica de verificação
export function verificarNomeDespacho(despacho: Despacho_Interface, listaNomes: string[]): boolean {
  const nomeDespacho = despacho.nome?.toLowerCase().trim();
  const nomeDetalhes = despacho.DetalhesDespacho?.nome?.toLowerCase().trim();
  return !!(nomeDespacho && listaNomes.includes(nomeDespacho)) || !!(nomeDetalhes && listaNomes.includes(nomeDetalhes));
}

export function isDespachoDeExigencia(despacho: Despacho_Interface): boolean {
  return verificarNomeDespacho(despacho, nomesDespachoExigencia);
}

// Atualizando o tipo de retorno e a lógica
export type TipoArquivamento = 
  | 'POR_NAO_CUMPRIMENTO_EXIGENCIA' 
  | 'POR_DESISTENCIA' 
  | 'OUTRO';

export interface ResultadoIsArquivamento {
  eArquivamento: boolean;
  tipoArquivamento?: TipoArquivamento;
}

export function isDespachoDeArquivamento(despacho: Despacho_Interface): ResultadoIsArquivamento {
  const nomeDespachoLower = despacho.nome?.toLowerCase().trim();
  const detalhesNomeLower = despacho.DetalhesDespacho?.nome?.toLowerCase().trim();
  const textoComplementarLower = despacho.textoComplementar?.toLowerCase() || "";
  const codigoDespacho = despacho.codigo?.trim(); // Adicionado para verificar o código

  // Checagem específica para "Indeferimento da petição (exigência não cumprida)" pelo código
  if (codigoDespacho === "120.3") {
    return { eArquivamento: true, tipoArquivamento: 'POR_NAO_CUMPRIMENTO_EXIGENCIA' };
  }

  if (verificarNomeDespacho(despacho, nomesDespachoArquivamentoPorNaoCumprimento)) {
    return { eArquivamento: true, tipoArquivamento: 'POR_NAO_CUMPRIMENTO_EXIGENCIA' };
  }
  
  const eDeferimentoPeticaoNomePrincipal = nomeDespachoLower === "deferimento da petição";
  const eDeferimentoPeticaoDetalhes = detalhesNomeLower === "deferimento da petição";

  const temCodigoDesistencia = despacho.ProtocoloDespacho?.some(p => 
    p.codigoServico === "355.1" || p.codigoServico === "3551" || 
    p.codigoServico === "383.1" || p.codigoServico === "3831"
  );

  if ((eDeferimentoPeticaoNomePrincipal || eDeferimentoPeticaoDetalhes) && temCodigoDesistencia) {
    const contemPalavraDesistenciaNoNomeOuComplementar = 
      nomeDespachoLower?.includes("desistência") ||
      textoComplementarLower.includes("desistência") ||
      textoComplementarLower.includes("desistencia");

    if (contemPalavraDesistenciaNoNomeOuComplementar) {
      return { eArquivamento: true, tipoArquivamento: 'POR_DESISTENCIA' };
    }
  }

  if (verificarNomeDespacho(despacho, nomesDespachoArquivamentoOutros)) {
    if (nomeDespachoLower?.includes("desistência") || detalhesNomeLower?.includes("desistência")) {
        return { eArquivamento: true, tipoArquivamento: 'POR_DESISTENCIA' };
    }
    return { eArquivamento: true, tipoArquivamento: 'OUTRO' };
  }
  
  return { eArquivamento: false };
}

// Ajuste para receber ExigenciaInfo opcionalmente
export function isDespachoDeCumprimentoExigencia(
  despacho: Despacho_Interface, 
  exigenciaAtiva?: ExigenciaInfo | null
): boolean {
  const nomeDespacho = despacho.nome?.toLowerCase().trim();
  const nomeDetalhes = despacho.DetalhesDespacho?.nome?.toLowerCase().trim();
  const textoComplementarLower = despacho.textoComplementar?.toLowerCase() || "";
  
  // Condição 1: Nome específico de cumprimento de exigência formal
  const condicaoNomeEspecifico = (nomeDespacho === nomeDespachoCumprimentoExigencia) || (nomeDetalhes === nomeDespachoCumprimentoExigencia);
  if (condicaoNomeEspecifico) return true;

  // Condição 2: Código de serviço 338.1 (Cumprimento de exigência formal) E nome "deferimento da petição"
  if (despacho.ProtocoloDespacho?.some(p => p.codigoServico === "338.1") && 
      (nomeDespacho?.includes("deferimento da petição") || nomeDetalhes?.includes("deferimento da petição"))) {
      return true; 
  }

  // Condição 3: Despacho é "Deferimento da petição" (ou similar no nome) E 
  // existe uma exigenciaAtiva com codigoServicoProtocoloOriginador E
  // o despacho atual tem um protocolo com o MESMO código de serviço.
  if (exigenciaAtiva && exigenciaAtiva.codigoServicoProtocoloOriginador) {
    const nomeGenericoDeferimentoPeticao = nomeDespacho === "deferimento da petição" || nomeDetalhes === "deferimento da petição" || 
                                         nomeDespacho === "deferimento de petição" || nomeDetalhes === "deferimento de petição"; // Variações
    if (nomeGenericoDeferimentoPeticao) {
      if (despacho.ProtocoloDespacho?.some(p => p.codigoServico === exigenciaAtiva.codigoServicoProtocoloOriginador)) {
        return true; // É um deferimento de petição que corresponde ao protocolo da exigência ativa
      }
    }
  }

  // Condição 4: Tratamento específico para manifestação em grau de recurso (código 376)
  // Isso ocorre quando há uma "Notificação de novo impedimento legal em grau de recurso" (que é uma exigência)
  // e o usuário protocola uma "Petição de manifestação em grau de recurso" (código 376).
  if (exigenciaAtiva) { // Garante que estamos no contexto de uma exigência ativa
    const eDeferimentoPeticaoOuManifestacao = 
        nomeDespacho?.includes("deferimento da petição") || nomeDetalhes?.includes("deferimento da petição") ||
        nomeDespacho?.includes("manifestação") || nomeDetalhes?.includes("manifestação") || // Verifica se o nome do despacho contém "manifestação"
        textoComplementarLower.includes("manifestação sobre parecer proferido em grau de recurso"); // Verifica texto complementar

    if (eDeferimentoPeticaoOuManifestacao && despacho.ProtocoloDespacho?.some(p => p.codigoServico === "376" || p.codigoServico === "376.0")) {
      // Se a exigência ativa foi a "Notificação de novo impedimento legal em grau de recurso",
      // E o despacho atual é um "deferimento da petição" ou "manifestação" com código 376, então é um cumprimento.
      // A lógica para `codigoServicoProtocoloOriginador` na exigência ativa precisa ser ajustada
      // para que essa "Notificação de novo impedimento..." armazene um código que possa ser comparado aqui,
      // ou podemos confiar que uma exigência ativa seguida de um protocolo 376 é suficiente.
      // Por ora, vamos assumir que se a exigência está ativa e recebemos um 376, é o cumprimento dela.
      return true;
    }
  }
  
  return false;
}

export function isDespachoDeSobrestamento(despacho: Despacho_Interface): boolean {
  return verificarNomeDespacho(despacho, nomesDespachoSobrestamento);
}

// --- Novas listas e funções para Deferimento, Concessão e Arquivamento por Falta de Pagamento ---

export const nomesDespachoDeferimento: string[] = [
  "deferimento do pedido",
  "recurso provido (decisão reformada para: deferimento)" // Considerado como um tipo de deferimento
].map(nome => nome.toLowerCase());

export function isDespachoDeDeferimento(despacho: Despacho_Interface): boolean {
  return verificarNomeDespacho(despacho, nomesDespachoDeferimento);
}

export const nomesDespachoConcessao: string[] = [
  "concessão de registro",
  "concessão de registro em designação" // Adicionado com base na sua lista
  // "Recurso contra arquivamento..." e "Recurso contra cancelamento..." não levam à concessão diretamente,
  // mas revertem um status. A concessão virá de um despacho específico de "Concessão de registro".
].map(nome => nome.toLowerCase());

export function isDespachoDeConcessao(despacho: Despacho_Interface): boolean {
  return verificarNomeDespacho(despacho, nomesDespachoConcessao);
}

// Funções para identificar pagamento de taxa de concessão pelos códigos de serviço do protocolo
export function isDespachoDePagamentoTaxaOrdinaria(despacho: Despacho_Interface): boolean {
  return despacho.ProtocoloDespacho?.some(p => p.codigoServico === "372") ?? false;
}

export function isDespachoDePagamentoTaxaExtraordinaria(despacho: Despacho_Interface): boolean {
  return despacho.ProtocoloDespacho?.some(p => p.codigoServico === "373") ?? false;
}

export const nomesDespachoArquivamentoFaltaPagamentoTaxa: string[] = [
  "arquivamento definitivo de pedido de registro por falta de pagamento da concessão"
].map(nome => nome.toLowerCase());

export function isDespachoDeArquivamentoFaltaPagamentoTaxa(despacho: Despacho_Interface): boolean {
  // Primeiro verifica pelo nome do despacho
  if (verificarNomeDespacho(despacho, nomesDespachoArquivamentoFaltaPagamentoTaxa)) {
    return true;
  }
  // TODO: Adicionar lógica para verificar por código de serviço específico se houver um para este tipo de arquivamento.
  // Por enquanto, focaremos no nome do despacho fornecido.
  return false;
}

// --- Novas listas e funções para Indeferimento ---
export const nomesDespachoIndeferimento: string[] = [
  "indeferimento do pedido"
  // Adicionar outros nomes exatos ou variações se necessário
].map(nome => nome.toLowerCase());

export function isDespachoDeIndeferimento(despacho: Despacho_Interface): boolean {
  // Verifica o nome principal do despacho ou o nome nos detalhes do despacho
  const nomeMatch = verificarNomeDespacho(despacho, nomesDespachoIndeferimento);
  if (nomeMatch) return true;

  // Verifica se é "Deferimento da petição - Oposição" (acolhimento de oposição de terceiro)
  const despachoNomeLower = despacho.nome?.toLowerCase() || "";
  const acolhimentoOposicaoMatch = 
    despachoNomeLower.includes("deferimento da petição") && 
    despachoNomeLower.includes("oposição") &&
    despacho.ProtocoloDespacho?.some(p => p.codigoServico === "332.1");

  return acolhimentoOposicaoMatch || false;
}

// --- Novas listas e funções para Apresentação de Recurso contra Indeferimento ---
export const nomesDespachoNotificacaoRecursoIndeferimento: string[] = [
  "notificação de recurso"
].map(nome => nome.toLowerCase());

export function isDespachoDeApresentacaoRecursoIndeferimento(despacho: Despacho_Interface): boolean {
  const nomeDespachoLower = despacho.nome?.toLowerCase() || "";
  const textoComplementarLower = despacho.textoComplementar?.toLowerCase() || "";

  // Condição 1: Despacho "Notificação de recurso" com texto complementar específico
  if (nomesDespachoNotificacaoRecursoIndeferimento.includes(nomeDespachoLower) && 
      textoComplementarLower.includes("recurso contra indeferimento")) {
    return true;
  }

  // Condição 2: Protocolo com código de serviço específico
  if (despacho.ProtocoloDespacho?.some(p => p.codigoServico === "3000" || p.codigoServico === "30001" || p.codigoServico === "333.17" || p.codigoServico === "33317")) {
    return true;
  }

  // Condição 3 (Opcional, se o nome do protocolo for confiável e disponível):
  // const nomeProtocoloMatch = despacho.ProtocoloDespacho?.some(p => 
  //   p.nome?.toLowerCase().includes("recurso contra indeferimento de pedido de registro de marca")
  // );
  // if (nomeProtocoloMatch) return true;

  return false;
}

// --- Novas listas e funções para Decisão de Recurso ---
export const nomesDespachoRecursoProvido: string[] = [
  "recurso provido (decisão reformada para: deferimento)",
  "recurso provido (decisão reformada para: deferimento parcial)",
  "recurso provido parcialmente (decisão reformada para: deferimento parcial)",
  "recurso provido parcialmente (outros)"
].map(nome => nome.toLowerCase());

export const nomesDespachoRecursoNaoProvido: string[] = [
  "recurso não provido (decisão mantida)"
].map(nome => nome.toLowerCase());

export function isDespachoDeRecursoProvido(despacho: Despacho_Interface): boolean {
  return verificarNomeDespacho(despacho, nomesDespachoRecursoProvido);
}

export function isDespachoDeRecursoNaoProvido(despacho: Despacho_Interface): boolean {
  return verificarNomeDespacho(despacho, nomesDespachoRecursoNaoProvido);
}

// --- Novas listas e funções para Nulidade Administrativa ---
export const nomesDespachoInstauracaoNulidade: string[] = [
  "notificação de instauração de processo de nulidade a requerimento",
  "notificação de instauração de processo de nulidade de ofício"
].map(nome => nome.toLowerCase());

export function isDespachoDeInstauracaoNulidade(despacho: Despacho_Interface): boolean {
  return verificarNomeDespacho(despacho, nomesDespachoInstauracaoNulidade);
}

export const nomesDespachoConcessaoMantidaPosNulidade: string[] = [
  "requerimento não provido (mantida a concessão)"
].map(nome => nome.toLowerCase());

export function isDespachoDeConcessaoMantidaPosNulidade(despacho: Despacho_Interface): boolean {
  const nomeDespachoLower = despacho.nome?.toLowerCase().trim();
  const nomeDetalhesLower = despacho.DetalhesDespacho?.nome?.toLowerCase().trim();

  // Nome explícito para concessão mantida
  if (nomeDespachoLower && nomesDespachoConcessaoMantidaPosNulidade.includes(nomeDespachoLower)) return true;
  if (nomeDetalhesLower && nomesDespachoConcessaoMantidaPosNulidade.includes(nomeDetalhesLower)) return true;

  // Se o nome do despacho indicar explicitamente ANULAÇÃO,
  // então NÃO é um despacho de concessão mantida, mesmo que o código do protocolo (368.1) sugira.
  const nomesExplicitamenteAnulacao = ["requerimento provido (nulo o registro)", "nulidade administrativa de registro de marca deferida"];
  if (nomeDespachoLower && nomesExplicitamenteAnulacao.includes(nomeDespachoLower)) {
      return false;
  }
  if (nomeDetalhesLower && nomesExplicitamenteAnulacao.includes(nomeDetalhesLower)) {
      return false;
  }

  // Se os nomes não foram conclusivos para manutenção E não foram explicitamente de anulação,
  // então verificar o código de serviço do protocolo para desistência de nulidade.
  if (despacho.ProtocoloDespacho?.some(p => p.codigoServico === "368.1" || p.codigoServico === "3681")) {
    return true;
  }
  return false;
}

export const nomesDespachoRegistroAnulado: string[] = [
  "requerimento provido (nulo o registro)", // Anulação direta
  "nulidade administrativa de registro de marca deferida" // Nome do serviço que leva à anulação
].map(nome => nome.toLowerCase());

export function isDespachoDeRegistroAnulado(despacho: Despacho_Interface): boolean {
  const nomeDespachoLower = despacho.nome?.toLowerCase().trim();
  const nomeDetalhesLower = despacho.DetalhesDespacho?.nome?.toLowerCase().trim();

  // Se o nome do despacho for um dos nomes de INSTAURAÇÃO de nulidade, não é uma anulação.
  if (isDespachoDeInstauracaoNulidade(despacho)) {
    return false;
  }

  // Nomes que explicitamente indicam anulação
  if (nomeDespachoLower && nomesDespachoRegistroAnulado.includes(nomeDespachoLower)) return true;
  if (nomeDetalhesLower && nomesDespachoRegistroAnulado.includes(nomeDetalhesLower)) return true;

  // Se o nome do despacho indicar explicitamente que a CONCESSÃO FOI MANTIDA,
  // então NÃO é um despacho de anulação, mesmo que o código do protocolo sugira.
  // Usamos a lista nomesDespachoConcessaoMantidaPosNulidade para essa verificação.
  if (nomeDespachoLower && nomesDespachoConcessaoMantidaPosNulidade.includes(nomeDespachoLower)) {
      return false; 
  }
  if (nomeDetalhesLower && nomesDespachoConcessaoMantidaPosNulidade.includes(nomeDetalhesLower)) {
      return false;
  }

  // Se os nomes não foram conclusivos para anulação E não foram explicitamente de manutenção,
  // então verificar o código de serviço do protocolo.
  if (despacho.ProtocoloDespacho?.some(p => p.codigoServico === "336.1" || p.codigoServico === "3361")) {
    return true;
  }
  // Casos onde o despacho principal é "Deferimento da petição" mas o texto complementar ou o serviço indicam anulação.
  // Esta lógica deve ser CUIDADOSA para não conflitar com a prioridade do nome.
  // Só deve ser aplicada se os nomes explícitos de manutenção/anulação não deram match.
  if (nomeDespachoLower === "deferimento da petição") { // Somente o nome genérico "deferimento da petição"
      if (despacho.ProtocoloDespacho?.some(p => p.codigoServico === "336.1" || p.codigoServico === "3361")) {
          return true;
      }
      // Poderia verificar texto complementar aqui se necessário e se o nome não for específico.
  }
  return false;
}

// Esta função precisará ser implementada com os nomes de despacho ou códigos de serviço corretos.
export function isDespachoDeManifestacaoNulidade(despacho: Despacho_Interface): boolean {
  const codigosManifestacaoNulidade = [
    "339.3", "3393", // Contestação a processo de nulidade de registro de marca (esse era o que já tínhamos)
    "3015", "30151",   // MANIFESTACAO (CONTRA PEDIDO DE NULIDADE POR CADUCIDADE)
    "3016.1", "30161", // MANIFESTACAO (CONTRA PEDIDO DE NULIDADE)
    "3396", "339.6",   // MANIFESTACAO EM PROCESSO DE NULIDADE DE REGISTRO DE MARCA (EX OFFICIO)
    "3395", "339.5",   // MANIFESTACAO EM PROCESSO DE NULIDADE DE REGISTRO DE MARCA (REQUERIMENTO DE TERCEIRO)
    "3394", "339.4"    // MANIFESTACAO EM PROCESSO DE NULIDADE DE REGISTRO DE MARCA (ADMINISTRATIVO)
    // Adicionar outros códigos relevantes conforme necessário
  ];

  if (despacho.ProtocoloDespacho?.some(p => p.codigoServico && codigosManifestacaoNulidade.includes(p.codigoServico))) {
    return true;
  }
  
  // Adicionar outras verificações se necessário (ex: por nome do despacho)
  // const nomeDespachoLower = despacho.nome?.toLowerCase();
  // if (nomeDespachoLower?.includes("manifestação sobre processo de nulidade") || 
  //     nomeDespachoLower?.includes("manifestação em processo de nulidade") ||
  //     nomeDespachoLower?.includes("contestação a processo de nulidade")) {
  //   return true;
  // }
  
  return false; 
}

// --- Funções para Renovação de Registro ---
export function isDespachoDeRenovacaoPagaOrdinaria(despacho: Despacho_Interface): boolean {
  const nomeMatch = despacho.nome?.toLowerCase().includes("deferimento da petição");
  const codigoServicoMatch = despacho.ProtocoloDespacho?.some(p => 
    p.codigoServico && p.codigoServico.replace('.', '').startsWith('374')
  );
  return !!(nomeMatch && codigoServicoMatch);
}

export function isDespachoDeRenovacaoPagaExtraordinaria(despacho: Despacho_Interface): boolean {
  const nomeMatch = despacho.nome?.toLowerCase().includes("deferimento da petição");
  const codigoServicoMatch = despacho.ProtocoloDespacho?.some(p => 
    p.codigoServico && p.codigoServico.replace('.', '').startsWith('375')
  );
  return !!(nomeMatch && codigoServicoMatch);
}

// Adicionar aqui as listas e funções para Deferimento e Concessão quando tivermos os nomes/códigos.
// export const nomesDespachoDeferimento: string[] = [/* ... */].map(nome => nome.toLowerCase());
// export function isDespachoDeDeferimento(despacho: Despacho_Interface): boolean { /* ... */ }

// export const nomesDespachoConcessao: string[] = [/* ... */].map(nome => nome.toLowerCase());
// export function isDespachoDeConcessao(despacho: Despacho_Interface): boolean { /* ... */ } 