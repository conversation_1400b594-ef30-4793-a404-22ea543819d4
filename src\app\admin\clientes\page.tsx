'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { <PERSON>ArrowLeft, FiSearch, FiUser, FiLink, FiActivity, FiChevronDown, FiChevronRight, FiFileText, FiEdit2, FiSave, FiX, FiPhone, FiMail, FiMapPin, FiUser as FiUserIcon, FiTool, FiAlertTriangle, FiCheckCircle, FiRefreshCw, FiArrowRight, FiUsers, FiMoreVertical, FiExternalLink, FiLogOut } from 'react-icons/fi';
import Link from 'next/link';
import Image from 'next/image';
import Breadcrumb from '@/components/Breadcrumb';

interface AdminUser {
  id: string;
  email: string;
  nome: string;
  role: 'MASTER' | 'COMUM';
}

interface Cliente {
  id: number;
  nome: string;
  identificador: string;
  autoLoginUrl: string;
  crmId: number | null;
  _count: {
    SessionLog: number;
    ProtocoloDownloadLog: number;
  };
}

interface PaginationInfo {
  currentPage: number;
  totalPages: number;
  totalCount: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}

interface ClientesResponse {
  clientes: Cliente[];
  pagination: PaginationInfo;
}

interface Processo {
  id: string;
  numero: string;
  dataDeposito: string | null;
  Marca: {
    nome: string | null;
    NCL?: Array<{
      codigo: string | null;
      especificacao: string | null;
    }>;
  } | null;
  Cliente?: {
    nome: string | null;
    identificador: string | null;
  };
  Despacho: Array<{
    nome: string | null;
    codigo: string;
  }>;
  Titular?: Array<{
    nomeRazaoSocial: string;
  }>;
}

interface ProcessosResponse {
  processos: Processo[];
  totalProcessos: number;
  currentPage: number;
  totalPages: number;
}

interface ContatoCliente {
  id?: number;
  email: string | null;
  telefone: string | null;
  telefoneSegundario: string | null;
  endereco: string | null;
  cidade: string | null;
  estado: string | null;
  cep: string | null;
}

interface ClienteCompleto {
  id: number;
  nome: string | null;
  identificador: string | null;
  tipoDeDocumento: string | null;
  numeroDocumento: string | null;
  nomeDaMarca: string | null;
  metodoComunicacao: string | null;
  autoLoginUrl: string | null;
  ContatoCliente: ContatoCliente[];
}

interface ShortUrlInfo {
  id: string;
  shortCode: string;
  createdAt: string;
  expiresAt: string | null;
  usageCount: number;
  isActive: boolean;
  analise: {
    identificadorNoToken: string | null;
    senhaNoToken: string | null;
    senhaAtualCliente: string | null;
    precisaCorrecao: boolean;
    erroDescriptografia: string | null;
    podeSerCorrigido: boolean;
  };
}

interface ShortUrlsResponse {
  cliente: {
    id: number;
    nome: string | null;
    identificador: string | null;
    temDocumento: boolean;
  };
  shortUrls: ShortUrlInfo[];
  resumo: {
    total: number;
    ativos: number;
    precisandoCorrecao: number;
    podeCorrigir: boolean;
  };
}

interface ClienteBusca {
  id: number;
  nome: string;
  identificador: string | null;
  autoLoginUrl: string | null;
  numeroDocumento: string | null;
  totalProcessos: number;
}

const ClientesPage: React.FC = () => {
  const [user, setUser] = useState<AdminUser | null>(null);
  const [authLoading, setAuthLoading] = useState(true);
  const [data, setData] = useState<ClientesResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const router = useRouter();
  const [search, setSearch] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [orderBy, setOrderBy] = useState<'id_desc' | 'id_asc' | 'nome_asc' | 'nome_desc'>('id_desc');
  const [pageSize, setPageSize] = useState(20);
  const [buscaExata, setBuscaExata] = useState(false);
  const [expandedClient, setExpandedClient] = useState<number | null>(null);
  const [clientProcessos, setClientProcessos] = useState<{ [key: number]: ProcessosResponse }>({});
  const [loadingProcessos, setLoadingProcessos] = useState<{ [key: number]: boolean }>({});
  const [clientDetails, setClientDetails] = useState<{ [key: number]: ClienteCompleto }>({});
  const [loadingDetails, setLoadingDetails] = useState<{ [key: number]: boolean }>({});
  const [editingClient, setEditingClient] = useState<number | null>(null);
  const [editForm, setEditForm] = useState<ClienteCompleto | null>(null);
  const [savingClient, setSavingClient] = useState(false);
  const [shortUrls, setShortUrls] = useState<{ [key: number]: ShortUrlsResponse }>({});
  const [loadingShortUrls, setLoadingShortUrls] = useState<{ [key: number]: boolean }>({});
  const [fixingShortUrl, setFixingShortUrl] = useState<{ [key: string]: boolean }>({});
  const [transferModal, setTransferModal] = useState<{
    isOpen: boolean;
    processo: Processo | null;
    clienteOrigemId: number | null;
  }>({ isOpen: false, processo: null, clienteOrigemId: null });
  const [clientesBusca, setClientesBusca] = useState<ClienteBusca[]>([]);
  const [buscaQuery, setBuscaQuery] = useState('');
  const [loadingBusca, setLoadingBusca] = useState(false);
  const [clienteSelecionado, setClienteSelecionado] = useState<ClienteBusca | null>(null);
  const [transferindo, setTransferindo] = useState(false);
  const [openProcessoMenu, setOpenProcessoMenu] = useState<string | null>(null);

  const fetchClientes = async (page: number = 1, searchTerm: string = '', order: string = orderBy, limit: number = pageSize, exactMatch: boolean = buscaExata) => {
    setLoading(true);
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
        orderBy: order,
        ...(searchTerm && { search: searchTerm }),
        ...(exactMatch && { exactMatch: 'true' }),
      });

      const response = await fetch(`/api/admin/clientes-autologin?${params}`);
      if (response.ok) {
        const result = await response.json();
        setData(result);
      } else {
        console.error('Erro ao buscar clientes:', response.status);
      }
    } catch (error) {
      console.error('Erro ao buscar clientes:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // Verificar autenticação
    const checkAuth = async () => {
      try {
        const response = await fetch('/api/admin/auth/me');
        if (response.ok) {
          const userData = await response.json();
          
          // Verificar se o usuário tem role MASTER
          if (userData.user.role !== 'MASTER') {
            console.log('Acesso negado: usuário não é MASTER');
            router.push('/admin/busca/');
            return;
          }
          
          setUser(userData.user);
        } else {
          router.push('/admin/login');
        }
      } catch (error) {
        console.error('Erro ao verificar autenticação:', error);
        router.push('/admin/login');
      } finally {
        setAuthLoading(false);
      }
    };

    checkAuth();
  }, [router]);

  useEffect(() => {
    if (!authLoading && user) {
      fetchClientes(currentPage, search, orderBy, pageSize, buscaExata);
    }
  }, [currentPage, authLoading, user, orderBy, pageSize, buscaExata]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (openProcessoMenu) {
        const target = event.target as HTMLElement;
        if (!target.closest('.processo-menu')) {
          closeProcessoMenu();
        }
      }
    };

    document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  }, [openProcessoMenu]);

  const handleSearch = () => {
    setCurrentPage(1);
    fetchClientes(1, search, orderBy, pageSize, buscaExata);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  const formatId = (id: number) => {
    return `#${id}`;
  };

  const fetchClientProcessos = async (clienteId: number) => {
    if (clientProcessos[clienteId]) {
      return; // Já carregamos os processos deste cliente
    }

    setLoadingProcessos(prev => ({ ...prev, [clienteId]: true }));
    
    try {
      const response = await fetch(`/api/admin/processos/busca?clienteId=${clienteId}&page=1&pageSize=10`);
      if (response.ok) {
        const result = await response.json();
        setClientProcessos(prev => ({ 
          ...prev, 
          [clienteId]: {
            processos: result.processos,
            totalProcessos: result.totalProcessos,
            currentPage: result.currentPage,
            totalPages: result.totalPages
          }
        }));
      } else {
        console.error('Erro ao buscar processos do cliente:', response.status);
      }
    } catch (error) {
      console.error('Erro ao buscar processos do cliente:', error);
    } finally {
      setLoadingProcessos(prev => ({ ...prev, [clienteId]: false }));
    }
  };

  const fetchClientDetails = async (clienteId: number) => {
    if (clientDetails[clienteId]) {
      return; // Já carregamos os detalhes deste cliente
    }

    setLoadingDetails(prev => ({ ...prev, [clienteId]: true }));
    
    try {
      const response = await fetch(`/api/admin/cliente/${clienteId}`);
      if (response.ok) {
        const result = await response.json();
        setClientDetails(prev => ({ 
          ...prev, 
          [clienteId]: result.cliente
        }));
      } else {
        console.error('Erro ao buscar detalhes do cliente:', response.status);
      }
    } catch (error) {
      console.error('Erro ao buscar detalhes do cliente:', error);
    } finally {
      setLoadingDetails(prev => ({ ...prev, [clienteId]: false }));
    }
  };

  const fetchShortUrls = async (clienteId: number) => {
    if (shortUrls[clienteId]) {
      return; // Já carregamos os short URLs deste cliente
    }

    setLoadingShortUrls(prev => ({ ...prev, [clienteId]: true }));
    
    try {
      const response = await fetch(`/api/admin/short-url/${clienteId}`);
      if (response.ok) {
        const result = await response.json();
        setShortUrls(prev => ({ 
          ...prev, 
          [clienteId]: result
        }));
      } else {
        console.error('Erro ao buscar short URLs:', response.status);
      }
    } catch (error) {
      console.error('Erro ao buscar short URLs:', error);
    } finally {
      setLoadingShortUrls(prev => ({ ...prev, [clienteId]: false }));
    }
  };

  const fixShortUrl = async (clienteId: number, shortCode: string) => {
    const key = `${clienteId}-${shortCode}`;
    setFixingShortUrl(prev => ({ ...prev, [key]: true }));

    try {
      const response = await fetch('/api/admin/short-url/fix', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          clienteId,
          shortCode,
        }),
      });

      if (response.ok) {
        const result = await response.json();
        alert(`Link corrigido com sucesso!\n\nSenha anterior: ${result.correcao.senhaAnterior}\nSenha atual: ${result.correcao.senhaAtual}`);
        
        // Recarregar short URLs para mostrar dados atualizados
        setShortUrls(prev => {
          const updated = { ...prev };
          delete updated[clienteId];
          return updated;
        });
        await fetchShortUrls(clienteId);
      } else {
        const error = await response.json();
        alert(`Erro ao corrigir link: ${error.error}`);
      }
    } catch (error) {
      console.error('Erro ao corrigir short URL:', error);
      alert('Erro ao corrigir link');
    } finally {
      setFixingShortUrl(prev => ({ ...prev, [key]: false }));
    }
  };

  const buscarClientes = async (query: string) => {
    if (query.length < 2) {
      setClientesBusca([]);
      return;
    }

    setLoadingBusca(true);
    try {
      const response = await fetch(`/api/admin/clientes/busca?q=${encodeURIComponent(query)}&limit=10`);
      if (response.ok) {
        const result = await response.json();
        setClientesBusca(result.clientes);
      } else {
        console.error('Erro ao buscar clientes:', response.status);
        setClientesBusca([]);
      }
    } catch (error) {
      console.error('Erro ao buscar clientes:', error);
      setClientesBusca([]);
    } finally {
      setLoadingBusca(false);
    }
  };

  const abrirModalTransferencia = (processo: Processo, clienteOrigemId: number) => {
    setTransferModal({
      isOpen: true,
      processo,
      clienteOrigemId
    });
    setBuscaQuery('');
    setClientesBusca([]);
    setClienteSelecionado(null);
  };

  const fecharModalTransferencia = () => {
    setTransferModal({ isOpen: false, processo: null, clienteOrigemId: null });
    setBuscaQuery('');
    setClientesBusca([]);
    setClienteSelecionado(null);
  };

  const transferirProcesso = async () => {
    if (!transferModal.processo || !transferModal.clienteOrigemId || !clienteSelecionado) {
      return;
    }

    setTransferindo(true);
    try {
      const response = await fetch('/api/admin/processo/transferir', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          processoId: transferModal.processo.id,
          clienteOrigemId: transferModal.clienteOrigemId,
          clienteDestinoId: clienteSelecionado.id,
        }),
      });

      if (response.ok) {
        const result = await response.json();
        alert(`Processo ${transferModal.processo.numero} transferido com sucesso para ${clienteSelecionado.nome}!`);
        
        // Recarregar processos do cliente atual
        setClientProcessos(prev => {
          const updated = { ...prev };
          delete updated[transferModal.clienteOrigemId!];
          return updated;
        });
        await fetchClientProcessos(transferModal.clienteOrigemId!);
        
        fecharModalTransferencia();
      } else {
        const error = await response.json();
        alert(`Erro ao transferir processo: ${error.error}`);
      }
    } catch (error) {
      console.error('Erro ao transferir processo:', error);
      alert('Erro ao transferir processo');
    } finally {
      setTransferindo(false);
    }
  };

  const toggleClientExpanded = async (clienteId: number) => {
    if (expandedClient === clienteId) {
      setExpandedClient(null);
      setEditingClient(null);
      setEditForm(null);
    } else {
      setExpandedClient(clienteId);
      await Promise.all([
        fetchClientProcessos(clienteId),
        fetchClientDetails(clienteId),
        fetchShortUrls(clienteId)
      ]);
    }
  };

  const startEditing = (cliente: ClienteCompleto) => {
    setEditingClient(cliente.id);
    // Garantir que há pelo menos um contato vazio para edição
    const clienteComContato = {
      ...cliente,
      ContatoCliente: cliente.ContatoCliente.length > 0 
        ? cliente.ContatoCliente 
        : [{
            email: '',
            telefone: '',
            telefoneSegundario: '',
            endereco: '',
            cidade: '',
            estado: '',
            cep: '',
          }]
    };
    setEditForm(clienteComContato);
  };

  const cancelEditing = () => {
    setEditingClient(null);
    setEditForm(null);
  };

  const saveClient = async () => {
    if (!editForm) return;

    setSavingClient(true);
    try {
      const response = await fetch(`/api/admin/cliente/${editForm.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          nome: editForm.nome,
          tipoDeDocumento: editForm.tipoDeDocumento,
          numeroDocumento: editForm.numeroDocumento,
          nomeDaMarca: editForm.nomeDaMarca,
          metodoComunicacao: editForm.metodoComunicacao,
          contatos: editForm.ContatoCliente,
        }),
      });

      if (response.ok) {
        const result = await response.json();
        setClientDetails(prev => ({
          ...prev,
          [editForm.id]: result.cliente
        }));
        setEditingClient(null);
        setEditForm(null);
        
        // Atualizar também os dados básicos na lista principal
        await fetchClientes(currentPage, search);
      } else {
        const error = await response.json();
        alert(`Erro ao salvar: ${error.error}`);
      }
    } catch (error) {
      console.error('Erro ao salvar cliente:', error);
      alert('Erro ao salvar dados do cliente');
    } finally {
      setSavingClient(false);
    }
  };

  const updateEditForm = (field: keyof ClienteCompleto, value: any) => {
    if (!editForm) return;
    setEditForm(prev => prev ? { ...prev, [field]: value } : null);
  };

  const updateContact = (index: number, field: keyof ContatoCliente, value: string) => {
    if (!editForm) return;
    const updatedContacts = [...editForm.ContatoCliente];
    updatedContacts[index] = { ...updatedContacts[index], [field]: value };
    setEditForm(prev => prev ? { ...prev, ContatoCliente: updatedContacts } : null);
  };

  const addContact = () => {
    if (!editForm) return;
    const newContact: ContatoCliente = {
      email: '',
      telefone: '',
      telefoneSegundario: '',
      endereco: '',
      cidade: '',
      estado: '',
      cep: '',
    };
    setEditForm(prev => prev ? { 
      ...prev, 
      ContatoCliente: [...prev.ContatoCliente, newContact] 
    } : null);
  };

  const removeContact = (index: number) => {
    if (!editForm) return;
    const updatedContacts = editForm.ContatoCliente.filter((_, i) => i !== index);
    setEditForm(prev => prev ? { ...prev, ContatoCliente: updatedContacts } : null);
  };

  const formatDataDeposito = (data: string | null) => {
    if (!data) return 'N/A';
    return new Date(data).toLocaleDateString('pt-BR');
  };

  const getStatusColor = (despachos: Array<{ nome: string | null; codigo: string }>) => {
    if (!despachos || despachos.length === 0) return 'bg-gray-100 text-gray-800';
    
    const ultimoDespacho = despachos[despachos.length - 1];
    const nomeDespacho = ultimoDespacho.nome?.toLowerCase() || '';
    
    if (nomeDespacho.includes('deferimento')) return 'bg-green-100 text-green-800';
    if (nomeDespacho.includes('indeferimento')) return 'bg-red-100 text-red-800';
    if (nomeDespacho.includes('publicação')) return 'bg-blue-100 text-blue-800';
    if (nomeDespacho.includes('concessão')) return 'bg-emerald-100 text-emerald-800';
    
    return 'bg-yellow-100 text-yellow-800';
  };

  const getStatusText = (despachos: Array<{ nome: string | null; codigo: string }>) => {
    if (!despachos || despachos.length === 0) return 'Sem despachos';
    
    const ultimoDespacho = despachos[despachos.length - 1];
    return ultimoDespacho.nome || ultimoDespacho.codigo || 'Status não definido';
  };

  const handleLogout = async () => {
    try {
      await fetch('/api/admin/auth/logout', { method: 'POST' });
      router.push('/admin/login');
    } catch (error) {
      console.error('Erro no logout:', error);
    }
  };

  const toggleProcessoMenu = (processoId: string) => {
    setOpenProcessoMenu(openProcessoMenu === processoId ? null : processoId);
  };

  const closeProcessoMenu = () => {
    setOpenProcessoMenu(null);
  };

  const handleOrderByChange = (newOrderBy: typeof orderBy) => {
    setOrderBy(newOrderBy);
    setCurrentPage(1);
  };

  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize);
    setCurrentPage(1);
  };

  if (authLoading) {
    return (
      <div className="min-h-screen bg-backgroundCinza flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Carregando...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-backgroundCinza">
      {/* Header igual ClienteHeader */}
      <header className="bg-green-500 shadow-md fixed top-0 left-0 right-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-2 flex justify-between items-center">
          <div>
            <Image src="/logo.svg" alt="Logo Registre.se" width={150} height={40} className="h-16 w-auto" priority />
          </div>
          <button
            onClick={handleLogout}
            title="Sair"
            className="flex items-center justify-center h-10 w-10 rounded-full bg-white text-green-500 hover:bg-green-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-green-500 focus:ring-white cursor-pointer"
            aria-label="Sair"
          >
            <FiLogOut className="w-6 h-6" />
          </button>
        </div>
      </header>
      <div className="h-20" /> {/* Espaço para o header fixo */}

      <div className="max-w-7xl mx-auto px-6 py-8">
        <Breadcrumb items={[
          { label: 'Admin', href: '/admin' },
          { label: 'Clientes' }
        ]} />
        
        {/* Formulário de Busca */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
          <div className="p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-lg font-semibold text-gray-900">
                Gerenciar Clientes
              </h2>
              <div className="text-sm text-gray-600">
                {data?.pagination.totalCount || 0} clientes encontrados
              </div>
            </div>

            <div className="space-y-4">
              {/* Linha de busca */}
              <div className="flex space-x-4">
                <div className="flex-1 relative">
                  <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                  <input
                    type="text"
                    placeholder="Buscar por nome, identificador ou URL..."
                    value={search}
                    onChange={(e) => setSearch(e.target.value)}
                    onKeyPress={handleKeyPress}
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                  />
                </div>
                <button
                  onClick={handleSearch}
                  disabled={loading}
                  className="inline-flex items-center justify-center px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
                >
                  {loading ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Buscando...
                    </>
                  ) : (
                    <>
                      <FiSearch className="w-4 h-4 mr-2" />
                      Buscar
                    </>
                  )}
                </button>
              </div>

              {/* Controles de ordenação e paginação */}
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div className="flex flex-col sm:flex-row gap-4">
                  {/* Seletor de ordenação */}
                  <div className="flex items-center space-x-2">
                    <label className="text-sm font-medium text-gray-700 whitespace-nowrap">
                      Ordenar por:
                    </label>
                    <select
                      value={orderBy}
                      onChange={(e) => handleOrderByChange(e.target.value as typeof orderBy)}
                      className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="id_desc">Mais recentes</option>
                      <option value="id_asc">Mais antigos</option>
                      <option value="nome_asc">Nome (A-Z)</option>
                      <option value="nome_desc">Nome (Z-A)</option>
                    </select>
                  </div>

                  {/* Seletor de resultados por página */}
                  <div className="flex items-center space-x-2">
                    <label className="text-sm font-medium text-gray-700 whitespace-nowrap">
                      Por página:
                    </label>
                    <select
                      value={pageSize}
                      onChange={(e) => handlePageSizeChange(parseInt(e.target.value))}
                      className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value={10}>10</option>
                      <option value={20}>20</option>
                      <option value={50}>50</option>
                      <option value={100}>100</option>
                    </select>
                  </div>
                </div>

                {/* Informações de resultados */}
                <div className="text-sm text-gray-600">
                  Mostrando {data?.clientes.length || 0} de {data?.pagination.totalCount || 0} clientes
                </div>
              </div>

              {/* Toggle de busca exata */}
              <div className="mt-4 pt-4 border-t border-gray-200">
                <label className="flex items-center space-x-2 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={buscaExata}
                    onChange={(e) => setBuscaExata(e.target.checked)}
                    className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <div className="flex flex-col">
                    <span className="text-sm font-medium text-gray-700">Busca exata</span>
                    <span className="text-xs text-gray-500">
                      {buscaExata ? 'Busca pelo termo exato' : 'Busca por termos que contenham o texto'}
                    </span>
                  </div>
                </label>
              </div>
            </div>
          </div>
        </div>

        {/* Lista de Clientes */}
        <div className="bg-white shadow rounded-lg">
          {loading ? (
            <div className="p-8 text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
              <p className="mt-4 text-gray-600">Carregando clientes...</p>
            </div>
          ) : data?.clientes.length === 0 ? (
            <div className="p-8 text-center text-gray-500">
              Nenhum cliente encontrado
            </div>
          ) : (
            <>
              {/* Cabeçalho da tabela */}
              <div className="border-b border-gray-200 px-6 py-3 bg-gray-50">
                <div className="grid grid-cols-12 gap-4 text-sm font-medium text-gray-900">
                  <div className="col-span-3">Cliente</div>
                  <div className="col-span-2">Identificador</div>
                  <div className="col-span-4">AutoLogin URL</div>
                  <div className="col-span-2">Estatísticas</div>
                  <div className="col-span-1">ID</div>
                </div>
              </div>

              {/* Lista */}
              <div className="divide-y divide-gray-200">
                {data?.clientes.map((cliente) => (
                  <div key={cliente.id}>
                    {/* Linha principal do cliente */}
                    <div 
                      className="px-6 py-4 hover:bg-gray-50 cursor-pointer transition-colors"
                      onClick={() => toggleClientExpanded(cliente.id)}
                    >
                      <div className="grid grid-cols-12 gap-4 items-center">
                        {/* Cliente com ícone de expansão */}
                        <div className="col-span-3">
                          <div className="flex items-center">
                            <div className="flex-shrink-0 mr-2">
                              {expandedClient === cliente.id ? (
                                <FiChevronDown className="w-4 h-4 text-gray-500" />
                              ) : (
                                <FiChevronRight className="w-4 h-4 text-gray-500" />
                              )}
                            </div>
                            <div className="flex-shrink-0 h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                              <FiUser className="w-5 h-5 text-blue-600" />
                            </div>
                            <div className="ml-3">
                              <p className="text-sm font-medium text-gray-900">
                                {cliente.nome || 'Nome não informado'}
                              </p>
                              <p className="text-xs text-gray-500">
                                ID: {cliente.id}
                              </p>
                            </div>
                          </div>
                        </div>

                        {/* Identificador */}
                        <div className="col-span-2">
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                            {cliente.identificador}
                          </span>
                        </div>

                        {/* AutoLogin URL */}
                        <div className="col-span-4">
                          <div className="flex items-center space-x-2">
                            <FiLink className="w-4 h-4 text-gray-400 flex-shrink-0" />
                            <a
                              href={cliente.autoLoginUrl}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-sm text-blue-600 hover:text-blue-800 truncate"
                              title={cliente.autoLoginUrl}
                              onClick={(e) => e.stopPropagation()}
                            >
                              {cliente.autoLoginUrl}
                            </a>
                          </div>
                        </div>

                        {/* Estatísticas */}
                        <div className="col-span-2">
                          <div className="flex items-center space-x-4 text-sm text-gray-500">
                            <div className="flex items-center">
                              <FiActivity className="w-4 h-4 mr-1" />
                              {cliente._count.SessionLog} logins
                            </div>
                            <div>
                              {cliente._count.ProtocoloDownloadLog} downloads
                            </div>
                          </div>
                        </div>

                        {/* ID */}
                        <div className="col-span-1 text-xs text-gray-500">
                          {formatId(cliente.id)}
                        </div>
                      </div>
                    </div>

                    {/* Seção expandida com dados e processos */}
                    {expandedClient === cliente.id && (
                      <div className="px-6 py-4 bg-gray-50 border-t">
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                          {/* Seção de Dados do Cliente */}
                          <div className="bg-white rounded-lg border border-gray-200 p-4">
                            <div className="flex items-center justify-between mb-4">
                              <h3 className="text-sm font-medium text-gray-900 flex items-center">
                                <FiUserIcon className="w-4 h-4 mr-2" />
                                Dados do Cliente
                              </h3>
                              {clientDetails[cliente.id] && !editingClient && (
                                <button
                                  onClick={() => startEditing(clientDetails[cliente.id])}
                                  className="flex items-center text-blue-600 hover:text-blue-800 text-sm"
                                >
                                  <FiEdit2 className="w-4 h-4 mr-1" />
                                  Editar
                                </button>
                              )}
                              {editingClient === cliente.id && (
                                <div className="flex space-x-2">
                                  <button
                                    onClick={saveClient}
                                    disabled={savingClient}
                                    className="flex items-center text-green-600 hover:text-green-800 text-sm disabled:opacity-50"
                                  >
                                    <FiSave className="w-4 h-4 mr-1" />
                                    {savingClient ? 'Salvando...' : 'Salvar'}
                                  </button>
                                  <button
                                    onClick={cancelEditing}
                                    className="flex items-center text-red-600 hover:text-red-800 text-sm"
                                  >
                                    <FiX className="w-4 h-4 mr-1" />
                                    Cancelar
                                  </button>
                                </div>
                              )}
                            </div>

                            {loadingDetails[cliente.id] ? (
                              <div className="flex items-center justify-center py-6">
                                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
                                <span className="ml-2 text-sm text-gray-600">Carregando detalhes...</span>
                              </div>
                            ) : clientDetails[cliente.id] ? (
                              editingClient === cliente.id && editForm ? (
                                // Formulário de edição
                                <div className="space-y-4">
                                  <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">Nome *</label>
                                    <input
                                      type="text"
                                      value={editForm.nome || ''}
                                      onChange={(e) => updateEditForm('nome', e.target.value)}
                                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                      placeholder="Nome do cliente"
                                    />
                                  </div>
                                  
                                  <div className="grid grid-cols-2 gap-3">
                                    <div>
                                      <label className="block text-sm font-medium text-gray-700 mb-1">Tipo de Documento</label>
                                      <select
                                        value={editForm.tipoDeDocumento || ''}
                                        onChange={(e) => updateEditForm('tipoDeDocumento', e.target.value)}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                      >
                                        <option value="">Selecione</option>
                                        <option value="CPF">CPF</option>
                                        <option value="CNPJ">CNPJ</option>
                                      </select>
                                    </div>
                                    <div>
                                      <label className="block text-sm font-medium text-gray-700 mb-1">Número do Documento</label>
                                      <input
                                        type="text"
                                        value={editForm.numeroDocumento || ''}
                                        onChange={(e) => updateEditForm('numeroDocumento', e.target.value)}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                        placeholder="000.000.000-00"
                                      />
                                    </div>
                                  </div>

                                  <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">Nome da Marca</label>
                                    <input
                                      type="text"
                                      value={editForm.nomeDaMarca || ''}
                                      onChange={(e) => updateEditForm('nomeDaMarca', e.target.value)}
                                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                      placeholder="Nome da marca principal"
                                    />
                                  </div>

                                  {/* Contatos */}
                                  <div className="border-t pt-4">
                                    <div className="flex items-center justify-between mb-4">
                                      <div>
                                        <label className="block text-sm font-medium text-gray-700">Informações de Contato</label>
                                        <p className="text-xs text-gray-500 mt-1">Adicione os dados de contato do cliente</p>
                                      </div>
                                      <button
                                        type="button"
                                        onClick={addContact}
                                        className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
                                      >
                                        <FiUserIcon className="w-4 h-4 mr-2" />
                                        Adicionar Contato
                                      </button>
                                    </div>
                                    
                                    <div className="space-y-4">
                                      {editForm.ContatoCliente.map((contato, index) => (
                                        <div key={index} className="border border-gray-200 rounded-lg p-4 bg-white shadow-sm">
                                          <div className="flex justify-between items-center mb-4">
                                            <div className="flex items-center">
                                              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                                                <FiUserIcon className="w-4 h-4 text-blue-600" />
                                              </div>
                                              <span className="text-sm font-medium text-gray-900">
                                                Contato {index + 1}
                                              </span>
                                            </div>
                                            {editForm.ContatoCliente.length > 1 && (
                                              <button
                                                type="button"
                                                onClick={() => removeContact(index)}
                                                className="inline-flex items-center p-1 border border-transparent rounded-full text-red-400 hover:text-red-600 hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors"
                                                title="Remover contato"
                                              >
                                                <FiX className="w-4 h-4" />
                                              </button>
                                            )}
                                          </div>
                                          
                                          <div className="grid grid-cols-1 gap-4">
                                            {/* Email */}
                                            <div>
                                              <label className="block text-xs font-medium text-gray-700 mb-1">
                                                <FiMail className="w-3 h-3 inline mr-1" />
                                                Email
                                              </label>
                                              <input
                                                type="email"
                                                value={contato.email || ''}
                                                onChange={(e) => updateContact(index, 'email', e.target.value)}
                                                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                                                placeholder="<EMAIL>"
                                              />
                                            </div>

                                            {/* Telefones */}
                                            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                                              <div>
                                                <label className="block text-xs font-medium text-gray-700 mb-1">
                                                  <FiPhone className="w-3 h-3 inline mr-1" />
                                                  Telefone Principal
                                                </label>
                                                <input
                                                  type="tel"
                                                  value={contato.telefone || ''}
                                                  onChange={(e) => updateContact(index, 'telefone', e.target.value)}
                                                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                                                  placeholder="(11) 99999-9999"
                                                />
                                              </div>
                                              <div>
                                                <label className="block text-xs font-medium text-gray-700 mb-1">
                                                  <FiPhone className="w-3 h-3 inline mr-1" />
                                                  Telefone Secundário
                                                </label>
                                                <input
                                                  type="tel"
                                                  value={contato.telefoneSegundario || ''}
                                                  onChange={(e) => updateContact(index, 'telefoneSegundario', e.target.value)}
                                                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                                                  placeholder="(11) 3333-3333"
                                                />
                                              </div>
                                            </div>

                                            {/* Endereço */}
                                            <div>
                                              <label className="block text-xs font-medium text-gray-700 mb-1">
                                                <FiMapPin className="w-3 h-3 inline mr-1" />
                                                Endereço Completo
                                              </label>
                                              <input
                                                type="text"
                                                value={contato.endereco || ''}
                                                onChange={(e) => updateContact(index, 'endereco', e.target.value)}
                                                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                                                placeholder="Rua das Flores, 123, Apto 45, Centro"
                                              />
                                            </div>

                                            {/* Cidade, Estado, CEP */}
                                            <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
                                              <div>
                                                <label className="block text-xs font-medium text-gray-700 mb-1">
                                                  Cidade
                                                </label>
                                                <input
                                                  type="text"
                                                  value={contato.cidade || ''}
                                                  onChange={(e) => updateContact(index, 'cidade', e.target.value)}
                                                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                                                  placeholder="São Paulo"
                                                />
                                              </div>
                                              <div>
                                                <label className="block text-xs font-medium text-gray-700 mb-1">
                                                  Estado
                                                </label>
                                                <input
                                                  type="text"
                                                  value={contato.estado || ''}
                                                  onChange={(e) => updateContact(index, 'estado', e.target.value.toUpperCase())}
                                                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                                                  placeholder="SP"
                                                  maxLength={2}
                                                />
                                              </div>
                                              <div>
                                                <label className="block text-xs font-medium text-gray-700 mb-1">
                                                  CEP
                                                </label>
                                                <input
                                                  type="text"
                                                  value={contato.cep || ''}
                                                  onChange={(e) => updateContact(index, 'cep', e.target.value)}
                                                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                                                  placeholder="01234-567"
                                                  maxLength={9}
                                                />
                                              </div>
                                            </div>
                                          </div>
                                        </div>
                                      ))}
                                      
                                      {editForm.ContatoCliente.length === 0 && (
                                        <div className="text-center py-8 text-gray-500 border-2 border-dashed border-gray-300 rounded-lg">
                                          <FiUserIcon className="w-8 h-8 mx-auto text-gray-400 mb-2" />
                                          <p className="text-sm">Nenhum contato adicionado</p>
                                          <p className="text-xs">Clique em "Adicionar Contato" para começar</p>
                                        </div>
                                      )}
                                    </div>
                                  </div>
                                </div>
                              ) : (
                                // Visualização dos dados
                                <div className="space-y-4">
                                  <div>
                                    <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">Nome</label>
                                    <p className="text-sm text-gray-900">{clientDetails[cliente.id].nome || 'Não informado'}</p>
                                  </div>
                                  
                                  <div className="grid grid-cols-2 gap-4">
                                    <div>
                                      <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">Documento</label>
                                      <p className="text-sm text-gray-900">
                                        {clientDetails[cliente.id].tipoDeDocumento && clientDetails[cliente.id].numeroDocumento 
                                          ? `${clientDetails[cliente.id].tipoDeDocumento}: ${clientDetails[cliente.id].numeroDocumento}`
                                          : 'Não informado'
                                        }
                                      </p>
                                    </div>
                                    <div>
                                      <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">Identificador</label>
                                      <p className="text-sm text-gray-900">{clientDetails[cliente.id].identificador || 'Não informado'}</p>
                                    </div>
                                  </div>

                                  <div>
                                    <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">Nome da Marca</label>
                                    <p className="text-sm text-gray-900">{clientDetails[cliente.id].nomeDaMarca || 'Não informado'}</p>
                                  </div>

                                  {/* Contatos */}
                                  <div className="border-t pt-4">
                                    <div className="flex items-center justify-between mb-3">
                                      <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">
                                        Informações de Contato
                                      </label>
                                      <span className="text-xs text-gray-400">
                                        {clientDetails[cliente.id].ContatoCliente.length} contato(s)
                                      </span>
                                    </div>
                                    
                                    {clientDetails[cliente.id].ContatoCliente.length === 0 ? (
                                      <div className="text-center py-6 text-gray-500 border-2 border-dashed border-gray-300 rounded-lg">
                                        <FiUserIcon className="w-6 h-6 mx-auto text-gray-400 mb-2" />
                                        <p className="text-sm">Nenhum contato cadastrado</p>
                                        <p className="text-xs">Clique em "Editar" para adicionar contatos</p>
                                      </div>
                                    ) : (
                                      <div className="space-y-3">
                                        {clientDetails[cliente.id].ContatoCliente.map((contato, index) => (
                                          <div key={index} className="border border-gray-200 rounded-lg p-4 bg-white shadow-sm">
                                            <div className="flex items-center mb-3">
                                              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                                                <FiUserIcon className="w-4 h-4 text-blue-600" />
                                              </div>
                                              <span className="text-sm font-medium text-gray-900">
                                                Contato {index + 1}
                                              </span>
                                            </div>
                                            
                                            <div className="grid grid-cols-1 gap-3 text-sm">
                                              {contato.email && (
                                                <div className="flex items-center">
                                                  <FiMail className="w-4 h-4 text-gray-400 mr-3 flex-shrink-0" />
                                                  <div>
                                                    <span className="text-gray-900">{contato.email}</span>
                                                    <div className="text-xs text-gray-500">Email</div>
                                                  </div>
                                                </div>
                                              )}
                                              
                                              {(contato.telefone || contato.telefoneSegundario) && (
                                                <div className="flex items-start">
                                                  <FiPhone className="w-4 h-4 text-gray-400 mr-3 flex-shrink-0 mt-0.5" />
                                                  <div>
                                                    {contato.telefone && (
                                                      <div className="text-gray-900">{contato.telefone}</div>
                                                    )}
                                                    {contato.telefoneSegundario && (
                                                      <div className="text-gray-900">{contato.telefoneSegundario}</div>
                                                    )}
                                                    <div className="text-xs text-gray-500">
                                                      {contato.telefone && contato.telefoneSegundario ? 'Telefones' : 'Telefone'}
                                                    </div>
                                                  </div>
                                                </div>
                                              )}
                                              
                                              {(contato.endereco || contato.cidade || contato.estado || contato.cep) && (
                                                <div className="flex items-start">
                                                  <FiMapPin className="w-4 h-4 text-gray-400 mr-3 flex-shrink-0 mt-0.5" />
                                                  <div>
                                                    {contato.endereco && (
                                                      <div className="text-gray-900">{contato.endereco}</div>
                                                    )}
                                                    {(contato.cidade || contato.estado || contato.cep) && (
                                                      <div className="text-gray-900">
                                                        {[contato.cidade, contato.estado, contato.cep].filter(Boolean).join(', ')}
                                                      </div>
                                                    )}
                                                    <div className="text-xs text-gray-500">Endereço</div>
                                                  </div>
                                                </div>
                                              )}
                                              
                                              {!contato.email && !contato.telefone && !contato.telefoneSegundario && !contato.endereco && !contato.cidade && !contato.estado && !contato.cep && (
                                                <div className="text-center py-2 text-gray-400 text-xs">
                                                  Nenhuma informação de contato preenchida
                                                </div>
                                              )}
                                            </div>
                                          </div>
                                        ))}
                                      </div>
                                    )}
                                  </div>

                                  {/* Seção de Links de Acesso */}
                                  <div className="border-t pt-4 mt-4">
                                    <div className="mb-3">
                                      <h4 className="text-xs font-medium text-gray-500 uppercase tracking-wide flex items-center">
                                        <FiLink className="w-3 h-3 mr-2" />
                                        Links de Acesso
                                        {shortUrls[cliente.id] && (
                                          <span className="ml-2 text-xs text-gray-400">
                                            ({shortUrls[cliente.id].resumo.total} links)
                                          </span>
                                        )}
                                        {shortUrls[cliente.id]?.resumo.precisandoCorrecao > 0 && (
                                          <span className="ml-2 flex items-center text-xs text-amber-600">
                                            <FiAlertTriangle className="w-3 h-3 mr-1" />
                                            {shortUrls[cliente.id].resumo.precisandoCorrecao} precisam correção
                                          </span>
                                        )}
                                      </h4>
                                    </div>

                                    {loadingShortUrls[cliente.id] ? (
                                      <div className="flex items-center justify-center py-4">
                                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
                                        <span className="ml-2 text-xs text-gray-600">Carregando links...</span>
                                      </div>
                                    ) : shortUrls[cliente.id]?.shortUrls.length === 0 ? (
                                      <div className="text-center py-4 text-gray-500 text-xs">
                                        Nenhum link de acesso encontrado
                                      </div>
                                    ) : (
                                      <div className="space-y-2">
                                        {shortUrls[cliente.id]?.shortUrls.map((shortUrl) => (
                                          <div 
                                            key={shortUrl.id} 
                                            className={`p-2 rounded border text-xs transition-colors ${
                                              shortUrl.analise.precisaCorrecao 
                                                ? 'border-amber-200 bg-amber-50' 
                                                : 'border-gray-200 bg-gray-50'
                                            }`}
                                          >
                                            <div className="flex items-center justify-between mb-1">
                                              <div className="flex items-center space-x-2">
                                                <code className="text-xs font-mono bg-white px-1 py-0.5 rounded border">
                                                  {shortUrl.shortCode}
                                                </code>
                                                {shortUrl.isActive ? (
                                                  <FiCheckCircle className="w-3 h-3 text-green-500" />
                                                ) : (
                                                  <FiX className="w-3 h-3 text-red-500" />
                                                )}
                                              </div>
                                              
                                              {shortUrl.analise.precisaCorrecao && shortUrl.analise.podeSerCorrigido && (
                                                <button
                                                  onClick={() => fixShortUrl(cliente.id, shortUrl.shortCode)}
                                                  disabled={fixingShortUrl[`${cliente.id}-${shortUrl.shortCode}`]}
                                                  className="flex items-center text-amber-600 hover:text-amber-800 text-xs disabled:opacity-50"
                                                >
                                                  {fixingShortUrl[`${cliente.id}-${shortUrl.shortCode}`] ? (
                                                    <FiRefreshCw className="w-3 h-3 mr-1 animate-spin" />
                                                  ) : (
                                                    <FiTool className="w-3 h-3 mr-1" />
                                                  )}
                                                  Corrigir
                                                </button>
                                              )}
                                            </div>

                                            <div className="grid grid-cols-2 gap-2 text-xs text-gray-600">
                                              <div>
                                                <span className="font-medium">Usos:</span> {shortUrl.usageCount}
                                              </div>
                                              <div>
                                                <span className="font-medium">Criado:</span> {new Date(shortUrl.createdAt).toLocaleDateString('pt-BR')}
                                              </div>
                                            </div>

                                            {shortUrl.analise.precisaCorrecao && (
                                              <div className="mt-2 p-2 bg-white rounded border border-amber-200">
                                                <div className="flex items-center text-xs text-amber-700 mb-1">
                                                  <FiAlertTriangle className="w-3 h-3 mr-1" />
                                                  Problema detectado
                                                </div>
                                                {shortUrl.analise.erroDescriptografia ? (
                                                  <p className="text-xs text-red-600">{shortUrl.analise.erroDescriptografia}</p>
                                                ) : (
                                                  <p className="text-xs text-amber-600">
                                                    Senha no link ({shortUrl.analise.senhaNoToken}) diferente da senha atual ({shortUrl.analise.senhaAtualCliente})
                                                  </p>
                                                )}
                                              </div>
                                            )}
                                          </div>
                                        ))}
                                      </div>
                                    )}
                                  </div>
                                </div>
                              )
                            ) : (
                              <div className="text-center py-6 text-gray-500 text-sm">
                                Erro ao carregar detalhes do cliente
                              </div>
                            )}
                          </div>

                          {/* Seção de Processos */}
                          <div className="bg-white rounded-lg border border-gray-200 p-4">
                            <div className="mb-4">
                              <h3 className="text-sm font-medium text-gray-900 flex items-center">
                                <FiFileText className="w-4 h-4 mr-2" />
                                Processos do Cliente
                                {clientProcessos[cliente.id] && (
                                  <span className="ml-2 text-xs text-gray-500">
                                    ({clientProcessos[cliente.id].totalProcessos} processos)
                                  </span>
                                )}
                              </h3>
                            </div>

                            {loadingProcessos[cliente.id] ? (
                              <div className="flex items-center justify-center py-6">
                                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                                <span className="ml-2 text-sm text-gray-600">Carregando processos...</span>
                              </div>
                            ) : clientProcessos[cliente.id]?.processos.length === 0 ? (
                              <div className="text-center py-6 text-gray-500 text-sm">
                                Nenhum processo encontrado para este cliente
                              </div>
                            ) : (
                              <div className="space-y-3">
                                {clientProcessos[cliente.id]?.processos.map((processo) => (
                                  <div 
                                    key={processo.id} 
                                    className="bg-white p-4 rounded-lg border border-gray-200 hover:border-gray-300 hover:shadow-sm transition-all relative"
                                  >
                                    {/* Layout Desktop */}
                                    <div className="hidden lg:block">
                                      <div className="grid grid-cols-2 gap-6">
                                        {/* Coluna 1: Dados do Processo */}
                                        <div className="space-y-3">
                                          {/* Número do processo */}
                                          <div>
                                            <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide mb-1">
                                              Processo
                                            </label>
                                            <Link 
                                              href={`/processo/${processo.numero}`}
                                              className="font-medium text-blue-600 hover:text-blue-800 text-sm"
                                              target="_blank"
                                            >
                                              {processo.numero}
                                            </Link>
                                          </div>

                                          {/* Data de depósito */}
                                          <div>
                                            <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide mb-1">
                                              Data Depósito
                                            </label>
                                            <span className="text-sm text-gray-600">
                                              {formatDataDeposito(processo.dataDeposito)}
                                            </span>
                                          </div>
                                        </div>

                                        {/* Coluna 2: Dados da Marca */}
                                        <div className="space-y-3">
                                          {/* Nome da marca com tooltip */}
                                          <div>
                                            <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide mb-1">
                                              Marca
                                            </label>
                                            <div 
                                              className="text-sm text-gray-900 truncate cursor-help"
                                              title={processo.Marca?.nome || 'Marca não informada'}
                                            >
                                              {processo.Marca?.nome || 'Não informada'}
                                            </div>
                                          </div>

                                          {/* Classe NCL */}
                                          {processo.Marca?.NCL && processo.Marca.NCL.length > 0 && (
                                            <div>
                                              <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide mb-1">
                                                Classe NCL
                                              </label>
                                              <div className="flex flex-wrap gap-1">
                                                {processo.Marca.NCL.map((ncl, index) => (
                                                  <span 
                                                    key={index}
                                                    className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 cursor-help"
                                                    title={ncl.especificacao || `Classe ${ncl.codigo}`}
                                                  >
                                                    {ncl.codigo}
                                                  </span>
                                                ))}
                                              </div>
                                            </div>
                                          )}
                                        </div>
                                      </div>

                                      {/* Menu de ações no canto superior direito */}
                                      <div className="absolute top-4 right-4 processo-menu">
                                        <button
                                          onClick={(e) => {
                                            e.stopPropagation();
                                            toggleProcessoMenu(processo.id);
                                          }}
                                          className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full transition-all"
                                        >
                                          <FiMoreVertical className="w-4 h-4" />
                                        </button>
                                        
                                        {openProcessoMenu === processo.id && (
                                          <div className="absolute right-0 top-full mt-1 w-48 bg-white rounded-md shadow-lg border border-gray-200 py-1 z-10">
                                            <button
                                              onClick={(e) => {
                                                e.stopPropagation();
                                                abrirModalTransferencia(processo, cliente.id);
                                                closeProcessoMenu();
                                              }}
                                              className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
                                            >
                                              <FiArrowRight className="w-4 h-4 mr-3" />
                                              Transferir processo
                                            </button>
                                            <a
                                              href={`/processo/${processo.numero}`}
                                              target="_blank"
                                              rel="noopener noreferrer"
                                              className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
                                              onClick={(e) => {
                                                e.stopPropagation();
                                                closeProcessoMenu();
                                              }}
                                            >
                                              <FiExternalLink className="w-4 h-4 mr-3" />
                                              Ver detalhes
                                            </a>
                                          </div>
                                        )}
                                      </div>
                                    </div>

                                    {/* Layout Mobile */}
                                    <div className="lg:hidden">
                                      <div className="flex items-start justify-between mb-3">
                                        <div className="flex-1">
                                          <Link 
                                            href={`/processo/${processo.numero}`}
                                            className="font-medium text-blue-600 hover:text-blue-800 text-base"
                                            target="_blank"
                                          >
                                            {processo.numero}
                                          </Link>
                                          
                                          {/* Nome da marca com truncate e tooltip */}
                                          <div 
                                            className="text-sm text-gray-900 mt-1 truncate cursor-help"
                                            title={processo.Marca?.nome || 'Marca não informada'}
                                          >
                                            {processo.Marca?.nome || 'Marca não informada'}
                                          </div>

                                          {/* Classes NCL */}
                                          {processo.Marca?.NCL && processo.Marca.NCL.length > 0 && (
                                            <div className="flex flex-wrap gap-1 mt-2">
                                              {processo.Marca.NCL.slice(0, 3).map((ncl, index) => (
                                                <span 
                                                  key={index}
                                                  className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 cursor-help"
                                                  title={ncl.especificacao || `Classe ${ncl.codigo}`}
                                                >
                                                  {ncl.codigo}
                                                </span>
                                              ))}
                                              {processo.Marca.NCL.length > 3 && (
                                                <span className="text-xs text-gray-500 px-2 py-1">
                                                  +{processo.Marca.NCL.length - 3} mais
                                                </span>
                                              )}
                                            </div>
                                          )}
                                        </div>
                                        
                                        {/* Menu de ações mobile */}
                                        <div className="processo-menu ml-3">
                                          <button
                                            onClick={(e) => {
                                              e.stopPropagation();
                                              toggleProcessoMenu(processo.id);
                                            }}
                                            className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full transition-all"
                                          >
                                            <FiMoreVertical className="w-4 h-4" />
                                          </button>
                                          
                                          {openProcessoMenu === processo.id && (
                                            <div className="absolute right-0 top-full mt-1 w-48 bg-white rounded-md shadow-lg border border-gray-200 py-1 z-10">
                                              <button
                                                onClick={(e) => {
                                                  e.stopPropagation();
                                                  abrirModalTransferencia(processo, cliente.id);
                                                  closeProcessoMenu();
                                                }}
                                                className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
                                              >
                                                <FiArrowRight className="w-4 h-4 mr-3" />
                                                Transferir processo
                                              </button>
                                              <a
                                                href={`/processo/${processo.numero}`}
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
                                                onClick={(e) => {
                                                  e.stopPropagation();
                                                  closeProcessoMenu();
                                                }}
                                              >
                                                <FiExternalLink className="w-4 h-4 mr-3" />
                                                Ver detalhes
                                              </a>
                                            </div>
                                          )}
                                        </div>
                                      </div>
                                      
                                      <div className="flex items-center justify-between text-sm">
                                        <span className="text-gray-600">
                                          {formatDataDeposito(processo.dataDeposito)}
                                        </span>
                                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(processo.Despacho)}`}>
                                          {getStatusText(processo.Despacho)}
                                        </span>
                                      </div>
                                    </div>

                                  </div>
                                ))}

                                {/* Mostrar mais processos se houver */}
                                {clientProcessos[cliente.id] && clientProcessos[cliente.id].totalProcessos > 10 && (
                                  <div className="text-center py-2">
                                    <Link
                                      href={`/admin/busca/?clienteId=${cliente.id}`}
                                      className="text-sm text-blue-600 hover:text-blue-800 font-medium"
                                    >
                                      Ver todos os {clientProcessos[cliente.id].totalProcessos} processos →
                                    </Link>
                                  </div>
                                )}
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>

              {/* Paginação */}
              {data && data.pagination.totalPages > 1 && (
                <div className="border-t border-gray-200 px-6 py-4">
                  <div className="flex items-center justify-between">
                    <div className="text-sm text-gray-700">
                      Página {data.pagination.currentPage} de {data.pagination.totalPages} 
                      ({data.pagination.totalCount} clientes no total)
                    </div>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => setCurrentPage(currentPage - 1)}
                        disabled={!data.pagination.hasPrevPage}
                        className="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        Anterior
                      </button>
                      <button
                        onClick={() => setCurrentPage(currentPage + 1)}
                        disabled={!data.pagination.hasNextPage}
                        className="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        Próxima
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </>
          )}
        </div>

        {/* Modal de Transferência */}
        {transferModal.isOpen && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900">
                  Transferir Processo
                </h3>
                <button
                  onClick={fecharModalTransferencia}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <FiX className="w-5 h-5" />
                </button>
              </div>

              {transferModal.processo && (
                <div className="mb-4 p-3 bg-gray-50 rounded-lg">
                  <p className="text-sm text-gray-600">Processo a ser transferido:</p>
                  <p className="font-medium text-gray-900">
                    {transferModal.processo.numero}
                  </p>
                  {transferModal.processo.Marca?.nome && (
                    <p className="text-sm text-gray-600">
                      Marca: {transferModal.processo.Marca.nome}
                    </p>
                  )}
                </div>
              )}

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Buscar cliente de destino:
                </label>
                <div className="relative">
                  <input
                    type="text"
                    value={buscaQuery}
                    onChange={(e) => {
                      setBuscaQuery(e.target.value);
                      buscarClientes(e.target.value);
                    }}
                    placeholder="Digite nome, identificador ou link de acesso..."
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  {loadingBusca && (
                    <div className="absolute right-3 top-2.5">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
                    </div>
                  )}
                </div>
              </div>

              {/* Resultados da busca */}
              {clientesBusca.length > 0 && (
                <div className="mb-4 max-h-48 overflow-y-auto border border-gray-200 rounded-md">
                  {clientesBusca.map((cliente) => (
                    <div
                      key={cliente.id}
                      onClick={() => setClienteSelecionado(cliente)}
                      className={`p-3 cursor-pointer hover:bg-gray-50 border-b border-gray-100 last:border-b-0 ${
                        clienteSelecionado?.id === cliente.id ? 'bg-blue-50 border-blue-200' : ''
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium text-gray-900">{cliente.nome}</p>
                          <p className="text-sm text-gray-600">
                            ID: {cliente.identificador || 'N/A'}
                          </p>
                          {cliente.totalProcessos > 0 && (
                            <p className="text-xs text-gray-500">
                              {cliente.totalProcessos} processos
                            </p>
                          )}
                        </div>
                        {clienteSelecionado?.id === cliente.id && (
                          <FiCheckCircle className="w-5 h-5 text-blue-600" />
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {buscaQuery.length >= 2 && clientesBusca.length === 0 && !loadingBusca && (
                <div className="mb-4 p-3 text-center text-gray-500 text-sm">
                  Nenhum cliente encontrado
                </div>
              )}

              {/* Cliente selecionado */}
              {clienteSelecionado && (
                <div className="mb-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
                  <div className="flex items-center">
                    <FiUsers className="w-4 h-4 text-blue-600 mr-2" />
                    <div>
                      <p className="font-medium text-blue-900">
                        Cliente selecionado: {clienteSelecionado.nome}
                      </p>
                      <p className="text-sm text-blue-700">
                        {clienteSelecionado.identificador}
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {/* Botões */}
              <div className="flex space-x-3">
                <button
                  onClick={fecharModalTransferencia}
                  className="flex-1 px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
                >
                  Cancelar
                </button>
                <button
                  onClick={transferirProcesso}
                  disabled={!clienteSelecionado || transferindo}
                  className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                >
                  {transferindo ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Transferindo...
                    </>
                  ) : (
                    <>
                      <FiArrowRight className="w-4 h-4 mr-2" />
                      Transferir
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ClientesPage; 