const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function debugAdminAuth() {
  try {
    console.log('🔍 Verificando usuários admin no banco...\n');
    
    const users = await prisma.adminUser.findMany({
      select: {
        id: true,
        email: true,
        nome: true,
        senha: true,
        role: true,
        ativo: true,
        ultimoLogin: true
      }
    });

    if (users.length === 0) {
      console.log('❌ Nenhum usuário admin encontrado no banco!');
      console.log('💡 Execute: node scripts/create-admin-user.js');
      return;
    }

    console.log(`✅ Encontrados ${users.length} usuário(s) admin:\n`);

    for (const user of users) {
      console.log(`👤 Usuário: ${user.nome}`);
      console.log(`📧 Email: ${user.email}`);
      console.log(`🛡️  Role: ${user.role}`);
      console.log(`🟢 Ativo: ${user.ativo}`);
      console.log(`🔑 Hash: ${user.senha.substring(0, 20)}...`);
      console.log(`📅 Último login: ${user.ultimoLogin || 'Nunca'}\n`);

      // Teste de senhas comuns
      const senhasParaTestar = [
        '#Rgse//7599@',
        'admin@123',
        'admin',
        '#Atendiment/o@Comercia/l#'
      ];

      console.log(`🔍 Testando senhas para ${user.email}:`);
      
      for (const senha of senhasParaTestar) {
        const isValid = await bcrypt.compare(senha, user.senha);
        console.log(`   "${senha}" → ${isValid ? '✅ VÁLIDA' : '❌ Inválida'}`);
      }
      
      console.log('─'.repeat(50) + '\n');
    }

    // Verificar sessões ativas
    console.log('🔗 Verificando sessões ativas...\n');
    
    const sessions = await prisma.adminSession.findMany({
      where: {
        expiresAt: {
          gte: new Date()
        }
      },
      include: {
        AdminUser: {
          select: { email: true, nome: true }
        }
      }
    });

    if (sessions.length === 0) {
      console.log('❌ Nenhuma sessão ativa encontrada');
    } else {
      console.log(`✅ ${sessions.length} sessão(ões) ativa(s):`);
      sessions.forEach(session => {
        console.log(`   ${session.AdminUser.email} - Expira em: ${session.expiresAt}`);
      });
    }

  } catch (error) {
    console.error('❌ Erro ao verificar autenticação admin:', error);
  } finally {
    await prisma.$disconnect();
  }
}

debugAdminAuth(); 