import { NextRequest } from 'next/server';
import { jwtVerify } from 'jose';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export interface AdminUser {
  id: string;
  email: string;
  nome: string;
  role: 'MASTER' | 'COMUM';
}

export async function verifyAdminSession(token: string): Promise<AdminUser | null> {
  try {
    console.log('🔍 Verificando sessão admin para token:', token?.substring(0, 20) + '...');
    
    // Verificar JWT
    const secret = new TextEncoder().encode(process.env.JWT_SECRET || 'fallback-secret');
    const { payload } = await jwtVerify(token, secret);
    console.log('✅ JWT válido, payload:', payload);

    // Verificar se sessão existe no banco e não expirou
    const session = await prisma.adminSession.findUnique({
      where: { token },
      include: { AdminUser: true },
    });

    console.log('🔍 Sessão encontrada no banco:', session ? 'Sim' : 'Não');
    
    if (!session) {
      console.log('❌ Sessão não encontrada no banco');
      return null;
    }
    
    if (new Date() > session.expiresAt) {
      console.log('❌ Sessão expirada:', session.expiresAt);
      return null;
    }
    
    if (!session.AdminUser.ativo) {
      console.log('❌ Usuário inativo');
      return null;
    }

    console.log('✅ Sessão válida para usuário:', session.AdminUser.email);

    return {
      id: session.AdminUser.id,
      email: session.AdminUser.email,
      nome: session.AdminUser.nome,
      role: session.AdminUser.role,
    };
  } catch (error) {
    console.error('Erro na verificação de sessão admin:', error);
    return null;
  }
}

export async function getAdminFromRequest(request: NextRequest): Promise<AdminUser | null> {
  const token = request.cookies.get('admin-session')?.value;
  if (!token) return null;
  
  return verifyAdminSession(token);
}

export function requireAdminRole(roles: ('MASTER' | 'COMUM')[] = ['MASTER', 'COMUM']) {
  return (user: AdminUser | null): boolean => {
    if (!user) return false;
    return roles.includes(user.role);
  };
}

// Middleware helper
export async function createAdminMiddleware(
  request: NextRequest,
  allowedRoles: ('MASTER' | 'COMUM')[] = ['MASTER', 'COMUM']
) {
  const user = await getAdminFromRequest(request);
  
  if (!user || !requireAdminRole(allowedRoles)(user)) {
    return {
      authenticated: false,
      user: null,
      redirect: '/admin/login'
    };
  }
  
  return {
    authenticated: true,
    user,
    redirect: null
  };
} 