import React, { useState, useEffect } from 'react';
// date-fns não é mais necessário aqui diretamente
// import { format, addYears } from 'date-fns';
// import { ptBR } from 'date-fns/locale';
import ProcessoDetalhesHeader from './ProcessoDetalhesHeader'; // Importa o novo componente
import ProcessoDetalhesEspecificacoes from './ProcessoDetalhesEspecificacoes'; // Importa o novo componente
import ProcessoDetalhesDespachos from './ProcessoDetalhesDespachos'; // Importa o novo componente
import ProcessoDetalhesCitacoes from './ProcessoDetalhesCitacoes'; // Importa o novo componente
import ProcessoDetalhesPeticoes from './ProcessoDetalhesPeticoes'; // Importa

// Define a interface CitacaoApi aqui (ou importe de um local compartilhado)
interface CitacaoApi {
    processo: string;
    marca: string;
    classe: string;
    titular: string;
    dataRpi: string;
    numeroRpi?: number;
    despacho: string;
    complemento: string;
}

// Remove definições locais de interface que causam conflito
/*
interface Despacho {
    ProtocoloDespacho?: ProtocoloDespacho[]; 
}
interface ProtocoloDespacho { 
    id: string;
    numero: string;
    data: Date | string;
    codigoServico: string;
    requerenteNomeRazaoSocial: string;
    Despacho?: { nome: string | null } | null;
    complemento?: string | null;
}
*/

// Assume que a interface Processo (importada ou global) já define Despacho e Marca
// Você pode precisar importar explicitamente: import type { Processo, Despacho } from '../types'; ou similar
interface ProcessoDetalhesProps {
  processo: any; // Mantém 'any' por segurança, mas idealmente a interface Processo importada
  sidebarBgColorClass: string;
  showSidebar: boolean;
}

// Componente interno para uma linha de detalhe
interface DetalheRowProps {
  label1: string;
  value1: React.ReactNode;
  label2: string;
  value2: React.ReactNode;
}
const DetalheRow: React.FC<DetalheRowProps> = ({ label1, value1, label2, value2 }) => (
  <div className="border-b border-gray-300 py-2 grid grid-cols-2 gap-x-4 items-center text-sm">
    {/* Coluna 1 */}
    <div className="flex">
      <span className="text-gray-600 mr-2">{label1}:</span>
      <span className="font-medium text-gray-800 truncate" title={typeof value1 === 'string' ? value1 : ''}>{value1}</span>
    </div>
    {/* Coluna 2 */}
    <div className="flex">
      <span className="text-gray-600 mr-2">{label2}:</span>
      <span className="font-medium text-gray-800 truncate" title={typeof value2 === 'string' ? value2 : ''}>{value2}</span>
    </div>
  </div>
);

// Re-adiciona sidebarBgColorClass, showSidebar das props desestruturadas
const ProcessoDetalhes: React.FC<ProcessoDetalhesProps> = ({ processo, sidebarBgColorClass, showSidebar }) => {

  const primeiraNCL = processo.Marca?.NCL?.[0];

  // Estado para citações
  const [citacoes, setCitacoes] = useState<CitacaoApi[]>([]);
  const [loadingCitacoes, setLoadingCitacoes] = useState<boolean>(true);
  const [errorCitacoes, setErrorCitacoes] = useState<string | null>(null);

  // Coleta todos os ProtocoloDespacho para petições
  const todasPeticoes = processo.Despacho?.flatMap((despacho: any) =>
    despacho.ProtocoloDespacho?.map((protocolo: any) => ({
        ...protocolo,
        Despacho: { nome: despacho.nome }
    })) || []
  ) || [];

  // useEffect para buscar citações
  useEffect(() => {
    const fetchCitacoes = async () => {
      if (!processo?.numero) {
        setLoadingCitacoes(false);
        setErrorCitacoes('Número do processo não fornecido.');
        setCitacoes([]);
        return;
      }

      setLoadingCitacoes(true);
      setErrorCitacoes(null);
      try {
        const baseUrl = 'https://api-v3-rgsys.registrese.app.br/api';
        const response = await fetch(`${baseUrl}/processos/${processo.numero}/citacoes`);

        if (!response.ok) {
          throw new Error(`Erro ao buscar citações: ${response.statusText}`);
        }

        const data = await response.json();
        setCitacoes(data.despachos || []);
      } catch (err) {
        console.error("Falha ao buscar citações:", err);
        setErrorCitacoes(err instanceof Error ? err.message : 'Ocorreu um erro desconhecido.');
        setCitacoes([]);
      } finally {
        setLoadingCitacoes(false);
      }
    };

    fetchCitacoes();
  }, [processo?.numero]); // Dependência no número do processo

  return (
    <div className="flex">
        <div className={`flex-grow px-6 py-2 space-y-4`}> 
            {/* Renderiza o componente de cabeçalho */}
            <ProcessoDetalhesHeader processo={processo} />
            
            {/* Renderiza a seção de especificações */}
            <ProcessoDetalhesEspecificacoes ncl={primeiraNCL} />

            {/* Renderiza a tabela de despachos */}
            <ProcessoDetalhesDespachos despachos={processo.Despacho} />
            
            {/* Renderiza a seção de citações condicionalmente */}
            {loadingCitacoes && (
                <div className="mb-6 p-4 text-center">
                  <h4 className="text-base font-semibold mb-0 uppercase text-center bg-[#A2A2A2] text-white py-1">CITAÇÃO EM OUTROS PROCESSOS</h4>
                  <p className="text-gray-500 mt-4">Carregando citações...</p>
                </div>
            )}
            {errorCitacoes && (
                 <div className="mb-6 p-4 text-center">
                    <h4 className="text-base font-semibold mb-0 uppercase text-center bg-[#A2A2A2] text-white py-1">CITAÇÃO EM OUTROS PROCESSOS</h4>
                    <p className="text-red-500 mt-4">Erro ao carregar citações: {errorCitacoes}</p>
                 </div>
            )}
            {!loadingCitacoes && !errorCitacoes && citacoes.length > 0 && (
                <ProcessoDetalhesCitacoes citacoes={citacoes} /> // Passa as citações como prop
            )}

            {/* Renderiza a seção de petições condicionalmente */}
            {todasPeticoes.length > 0 && (
                <ProcessoDetalhesPeticoes peticoes={todasPeticoes} />
            )}
        </div>

        {/* Extensão da Sidebar */}
        {showSidebar && (
            <div
                className={`min-w-[58px] flex-shrink-0 ${sidebarBgColorClass}`}
            >
                {/* Vazio */}
            </div>
        )}
    </div>
  );
};

export default ProcessoDetalhes; 