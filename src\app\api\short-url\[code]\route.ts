import { NextRequest, NextResponse } from 'next/server';
import { getLongToken } from '@/lib/shortUrl';

export async function GET(
  request: NextRequest,
  { params }: { params: { code: string } }
) {
  try {
    const { code } = params;
    
    if (!code) {
      return NextResponse.json(
        { error: 'Código não fornecido' },
        { status: 400 }
      );
    }

    // Buscar token longo
    const result = await getLongToken(code);
    
    if (!result) {
      return NextResponse.json(
        { error: 'Código inválido, expirado ou já utilizado o máximo de vezes' },
        { status: 404 }
      );
    }

    console.log(`Short URL resolvida: ${code} -> cliente ${result.clienteId || 'N/A'}`);

    return NextResponse.json({
      longToken: result.longToken,
      clienteId: result.clienteId,
      message: 'Token recuperado com sucesso'
    });

  } catch (error) {
    console.error('Erro ao resolver short URL:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
} 