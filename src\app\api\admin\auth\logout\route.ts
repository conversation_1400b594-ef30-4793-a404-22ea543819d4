import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { cookies } from 'next/headers';

const prisma = new PrismaClient();

export async function POST(request: NextRequest) {
  try {
    const cookieStore = await cookies();
    const token = cookieStore.get('admin-session')?.value;

    if (token) {
      // Remover sessão do banco
      await prisma.adminSession.deleteMany({
        where: { token },
      });
    }

    // Limpar cookie
    cookieStore.set('admin-session', '', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 0,
      path: '/', // Mudando para '/' para coincidir com o login
    });

    return NextResponse.json({ success: true });

  } catch (error) {
    console.error('Erro no logout admin:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
} 