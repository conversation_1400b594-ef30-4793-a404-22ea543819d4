import React from 'react';

interface LogoProps {
  className?: string;
}

export default function LogoTecnologiaFutura({ className = "" }: LogoProps) {
  return (
    <svg
      viewBox="0 0 200 200"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <rect width="200" height="200" rx="20" fill="#E5F6FF" />
      <circle cx="100" cy="100" r="60" stroke="#0099FF" strokeWidth="8" />
      <path
        d="M70 100L90 120L130 80"
        stroke="#0099FF"
        strokeWidth="8"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M100 40V60M100 140V160M40 100H60M140 100H160"
        stroke="#0099FF"
        strokeWidth="8"
        strokeLinecap="round"
      />
    </svg>
  );
} 