import { format, addDays, addYears } from 'date-fns';
import { ptBR } from 'date-fns/locale';

interface ConcessaoTimelineProps {
  dataDeposito: Date;
  dataConcessao: Date;
  // Usando any[] para despachos para contornar erro de tipo entre componentes
  despachos: any[]; 
}

interface ProgressoInfo {
  etapaAtual: number; // 0: Dep->Conc, 1: Conc->Nul, 2: Nul->Renov
  progressoNaEtapaAtual: number; // 0 a 1
}

function calcularSeMarcadorCompleto(index: number, progInfo: ProgressoInfo): boolean {
  return progInfo.etapaAtual >= index;
}

function calcularProgressoEntreDatas(hojeMs: number, inicioMs: number | null, fimMs: number | null): number {
  if (inicioMs === null || fimMs === null || fimMs <= inicioMs) {
    return 0; 
  }
  const duracaoEtapa = fimMs - inicioMs;
  const tempoDecorrido = hojeMs - inicioMs;
  return Math.max(0, Math.min(1, tempoDecorrido / duracaoEtapa));
}

export default function ConcessaoTimeline({
  dataDeposito,
  dataConcessao,
  despachos,
}: ConcessaoTimelineProps) {
  const hoje = new Date();
  const hojeMs = hoje.getTime();
  
  // --- Cálculo de Datas e Estados (Movido para cima) --- 
  const dataDepositoMs = dataDeposito.getTime();
  const dataConcessaoMs = dataConcessao.getTime();
  const dataFimNulidade = addDays(dataConcessao, 180);
  const dataFimNulidadeMs = dataFimNulidade.getTime();

  const primeiraRenovacaoConfirmada = despachos.some(despacho => {
    const nomeDespacho = despacho.nome?.toLowerCase() || '';
    const dataPubDespacho = despacho.RPI?.dataPublicacao ? new Date(despacho.RPI.dataPublicacao).getTime() : null;
    
    if (nomeDespacho.includes("deferimento da petição") && 
        dataPubDespacho && 
        dataPubDespacho > dataConcessaoMs) { 
      
      const protocolos = despacho.ProtocoloDespacho as any[];
      if (protocolos && protocolos.length > 0) {
        return protocolos.some(protocolo => 
          protocolo?.codigoServico === "3745"
        );
      }
    }
    return false;
  });

  const dataPrimeiraRenovacao = addYears(dataConcessao, 10);
  const dataSegundaRenovacao = addYears(dataConcessao, 20);
  const dataFimTimeline = primeiraRenovacaoConfirmada ? dataSegundaRenovacao : dataPrimeiraRenovacao;
  const dataFimTimelineMs = dataFimTimeline.getTime();

  // --- Cálculo do Progresso (Chamado após cálculo das datas) --- 
  const calcularProgresso = (): ProgressoInfo => {
    // 0. Depósito -> Concessão
    if (hojeMs < dataConcessaoMs) {
      return { 
        etapaAtual: 0, 
        progressoNaEtapaAtual: calcularProgressoEntreDatas(hojeMs, dataDepositoMs, dataConcessaoMs)
      };
    }
    
    // 1. Concessão -> Fim Nulidade
    if (hojeMs < dataFimNulidadeMs) {
      return { 
        etapaAtual: 1, 
        progressoNaEtapaAtual: calcularProgressoEntreDatas(hojeMs, dataConcessaoMs, dataFimNulidadeMs)
      };
    }
    
    // 2. Fim Nulidade -> Renovação (1ª ou 2ª)
    if (hojeMs < dataFimTimelineMs) {
       return { 
        etapaAtual: 2, 
        progressoNaEtapaAtual: calcularProgressoEntreDatas(hojeMs, dataFimNulidadeMs, dataFimTimelineMs)
      };
    }
    
    // 3. Após Data Fim Timeline
    return { etapaAtual: 3, progressoNaEtapaAtual: 1 };
  };

  const progressoInfo = calcularProgresso(); // Chamada da função de cálculo

  // --- Componente Interno TimelineMarker --- 
  const TimelineMarker = ({ 
    data, 
    titulo, 
    descricao, 
    index, 
    isUltimoMarcador,
    progressoInfo // Recebe o progresso calculado
  }: { 
    data: Date, 
    titulo: string, 
    descricao: string,
    index: number,
    isUltimoMarcador: boolean,
    progressoInfo: ProgressoInfo
  }) => {
    const isMarkerCompleted = calcularSeMarcadorCompleto(index, progressoInfo);
    const showBlueLine = index < progressoInfo.etapaAtual || (index === progressoInfo.etapaAtual && progressoInfo.progressoNaEtapaAtual > 0);
    const blueLineHeightPercent = index < progressoInfo.etapaAtual ? 100 : progressoInfo.progressoNaEtapaAtual * 100;

    return (
      <div className="flex relative"> 
        {/* Data (lado esquerdo) */}
        <div className="w-24 flex-shrink-0 mr-4 text-right">
          <span className="text-sm font-medium text-[#A3A3A3]">
            {format(data, "dd/MM/yyyy")}
          </span>
        </div>

        {/* Marcador e Linha de Conexão (Centro) */}
        <div className="flex flex-col items-center w-5"> 
          {/* Marcador - Comentário removido */}
          <div className={`w-5 h-5 rounded-full shadow-md flex items-center justify-center flex-shrink-0 ${ 
            isMarkerCompleted 
              ? "bg-[#4597B5] border-2 border-[#4597B5]" 
              : "border-2 border-[#C3C3C3] bg-white"
            } relative z-10`}>
            {isMarkerCompleted && (
              <svg className="w-3.5 h-3.5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M5 13l4 4L19 7" />
              </svg>
            )}
          </div>

          {/* Container da Linha */}
          {!isUltimoMarcador && (
            <div className="w-1 flex-grow bg-[#C3C3C3] relative mt-[-2px]"> 
              {/* Linha Azul */}
              {showBlueLine && (
                <div
                  className="absolute top-0 left-0 w-full bg-[#4597B5] transition-height duration-300 ease-in-out"
                  style={{ height: `${blueLineHeightPercent}%` }}
                ></div>
              )}
            </div>
          )}
        </div>

        {/* Texto (lado direito) */}
        <div className="ml-4 flex-grow pb-6"> 
          <h3 className="text-base font-semibold text-black leading-tight">{titulo}</h3>
          <p className="text-sm text-gray-500 mt-0.5">{descricao}</p>
        </div>
      </div>
    );
  };

  // --- Renderização Principal --- 
  return (
    <div className="pt-4 pb-2 px-2">
        {/* Marcador 0: Protocolo */}
        <TimelineMarker
          index={0}
          data={dataDeposito}
          titulo="Protocolo"
          descricao="Data em que o pedido foi depositado no INPI."
          isUltimoMarcador={false} 
          progressoInfo={progressoInfo} // Passar progressoInfo
        />
        
        {/* Marcador 1: Concessão */}
        <TimelineMarker
          index={1}
          data={dataConcessao}
          titulo="Concessão"
          descricao="Data em que o registro foi concedido pelo INPI."
          isUltimoMarcador={false} 
          progressoInfo={progressoInfo} // Passar progressoInfo
        />
        
        {/* Marcador 2: Fim Prazo Nulidade */}
        <TimelineMarker
          index={2}
          data={dataFimNulidade}
          titulo="Prazo de nulidade"
          descricao="Data limite para terceiros pedirem a nulidade do registro."
          isUltimoMarcador={false} 
          progressoInfo={progressoInfo} // Passar progressoInfo
        />
        
        {/* Marcador 3: Renovação */}
        <TimelineMarker
          index={3}
          data={dataFimTimeline}
          titulo="Renovação"
          descricao={primeiraRenovacaoConfirmada 
            ? "Data limite para renovar o registro pela segunda vez."
            : "Data limite para renovar o registro pela primeira vez."}
          isUltimoMarcador={true} 
          progressoInfo={progressoInfo} // Passar progressoInfo
        />
    </div>
  );
} 