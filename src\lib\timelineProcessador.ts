import { 
  Processo_Interface, 
  Despacho_Interface,
  ProtocoloDespacho_Interface,
  RPI_Interface,
  DetalhesDespacho_Interface
} from "./appTypes"; // Importar tipos canônicos

export interface EventoProcessual {
  data: Date;
  tipo: 'DESPACHO' | 'PROTOCOLO_DESPACHO';
  descricaoBreve: string; 
  codigoOrigem?: string; 
  nomeDespacho?: string | null; 
  rpiNumero?: string | null; 
}

export function extrairEventosDeProcesso(processo: Processo_Interface): EventoProcessual[] {
  const eventos: EventoProcessual[] = [];

  if (!processo || !processo.Despacho) {
    return eventos;
  }

  processo.Despacho.forEach((despacho: Despacho_Interface) => {
    // Evento para o Despacho em si
    if (despacho.RPI && despacho.RPI.dataPublicacao) {
      eventos.push({
        data: new Date(despacho.RPI.dataPublicacao),
        tipo: 'DESPACHO',
        descricaoBreve: despacho.nome || despacho.DetalhesDespacho?.nome || 'Despacho não especificado',
        nomeDespacho: despacho.nome || despacho.DetalhesDespacho?.nome,
        codigoOrigem: despacho.codigo,
        rpiNumero: despacho.RPI.numero?.toString(),
      });
    }

    // Eventos para cada ProtocoloDespacho associado
    if (despacho.ProtocoloDespacho) {
      despacho.ProtocoloDespacho.forEach((protocolo: ProtocoloDespacho_Interface) => {
        if (protocolo.data) {
          eventos.push({
            data: new Date(protocolo.data),
            tipo: 'PROTOCOLO_DESPACHO',
            descricaoBreve: `Protocolo ${protocolo.codigoServico} (${protocolo.requerenteNomeRazaoSocial || 'N/A'})`,
            codigoOrigem: protocolo.codigoServico,
            nomeDespacho: despacho.nome,
            rpiNumero: despacho.RPI?.numero?.toString(),
          });
        }
      });
    }
  });

  eventos.sort((a, b) => a.data.getTime() - b.data.getTime());

  return eventos;
} 