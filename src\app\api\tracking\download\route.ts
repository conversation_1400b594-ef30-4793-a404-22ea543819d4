import { NextRequest, NextResponse } from 'next/server';
import { jwtVerify } from 'jose';
import { prisma } from '@/lib/prisma';

const JWT_SECRET = process.env.JWT_SECRET;

// Função auxiliar para verificar o token
async function verifyToken(token: string, secret: string): Promise<any | null> {
  try {
    const { payload } = await jwtVerify(
      token,
      new TextEncoder().encode(secret)
    );
    return payload;
  } catch (error) {
    console.error("Erro ao verificar token:", error);
    return null;
  }
}

// Função para extrair IP do cliente
function getClientIP(request: NextRequest): string {
  // Tenta diferentes headers para obter o IP real
  const forwarded = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');
  const cfConnectingIP = request.headers.get('cf-connecting-ip');
  
  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }
  if (realIP) {
    return realIP;
  }
  if (cfConnectingIP) {
    return cfConnectingIP;
  }
  
  return 'unknown';
}

export async function POST(request: NextRequest) {
  try {
    // Verificar autenticação
    const token = request.cookies.get('auth_token')?.value;

    if (!token || !JWT_SECRET) {
      return NextResponse.json(
        { error: 'Não autorizado' }, 
        { status: 401 }
      );
    }

    const payload = await verifyToken(token, JWT_SECRET);
    if (!payload || !payload.clientId) {
      return NextResponse.json(
        { error: 'Token inválido' }, 
        { status: 401 }
      );
    }

    // Obter dados do request
    const body = await request.json();
    const { 
      processoId, 
      numeroProcesso, 
      fileSize, 
      success = true, 
      errorMessage 
    } = body;

    if (!processoId || !numeroProcesso) {
      return NextResponse.json(
        { error: 'processoId e numeroProcesso são obrigatórios' },
        { status: 400 }
      );
    }

    // Obter informações do cliente e request
    const clienteId = payload.clientId;
    const ipAddress = getClientIP(request);
    const userAgent = request.headers.get('user-agent') || null;

    // Salvar log de download
    const downloadLog = await prisma.protocoloDownloadLog.create({
      data: {
        clienteId,
        processoId,
        numeroProcesso,
        ipAddress,
        userAgent,
        fileSize: fileSize || null,
        success,
        errorMessage: errorMessage || null,
      },
    });

    console.log(`Download registrado: Cliente ${clienteId}, Processo ${numeroProcesso}, Sucesso: ${success}`);

    return NextResponse.json({
      message: 'Download registrado com sucesso',
      logId: downloadLog.id,
    });

  } catch (error) {
    console.error('Erro ao registrar download:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
} 