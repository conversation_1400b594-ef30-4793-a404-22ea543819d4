'use client';

import { useState, useEffect, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import LoginForm from '@/components/LoginForm';
import { useUserPreferences } from '@/store/userPreferences';

// Novo componente filho para conter a lógica que depende de useSearchParams
function HomePageContent() {
  const searchParams = useSearchParams();
  const [identificador, setIdentificador] = useState<string>('');
  const [identificadorDisabled, setIdentificadorDisabled] = useState<boolean>(false);
  const [redirectUrl, setRedirectUrl] = useState<string | null>(null);

  // Estado global para preferências do usuário
  const { setIdentifier } = useUserPreferences();
  
  useEffect(() => {
    // Pega a URL de redirecionamento do parâmetro 'redirect'
    const redirectParam = searchParams.get('redirect');
    if (redirectParam) {
        setRedirectUrl(redirectParam);
        console.log('URL de redirecionamento encontrada:', redirectParam);
    }
    
    // ✅ MODIFICADO: Apenas verificar parâmetro 'id' (não mais path)
      const queryIdentificador = searchParams.get('id');
      if (queryIdentificador) {
        console.log('Identificador extraído do parâmetro:', queryIdentificador);
        setIdentificador(queryIdentificador);
        setIdentifier(queryIdentificador); // Salva no estado global
        setIdentificadorDisabled(true);
    }
  }, [searchParams, setIdentifier]);
  
  // Adicionando console log para debug
  useEffect(() => {
    console.log('Estado do identificador:', identificador);
    console.log('Campo desabilitado:', identificadorDisabled);
  }, [identificador, identificadorDisabled]);

  // Retorna o JSX que estava anteriormente em Home
  return (
    <>
      {/* Overlay semi-transparente para escurecer a imagem de fundo */}
      <div className="absolute inset-0 opacity-30"></div>

      {/* Conteúdo do formulário */}
      <div className="relative z-10 flex flex-col items-center justify-center px-4 w-full">
        <h1 className="text-2xl text-center font-bold">
          Acompanhe a proteção das suas marcas
        </h1>
        <div className="w-full max-w-md mt-24">
          <LoginForm
            initialIdentificador={identificador}
            isIdentificadorDisabled={identificadorDisabled}
            redirectUrl={redirectUrl}
          />
        </div>
      </div>
    </>
  );
}

// Componente Home agora apenas configura o layout e usa Suspense
export default function Home() {
  return (
    <div
      className="min-h-screen flex items-center justify-center bg-gray-50 relative"
      style={{
        backgroundImage: "radial-gradient(#d1d5db 1px, transparent 1px)",
        backgroundSize: "30px 30px",
      }}
    >
      <Suspense fallback={<div>Carregando...</div>}> {/* Envolve HomePageContent com Suspense */}
        <HomePageContent />
      </Suspense>
    </div>
  );
} 